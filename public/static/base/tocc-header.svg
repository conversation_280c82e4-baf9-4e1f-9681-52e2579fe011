<?xml version="1.0" encoding="UTF-8"?>
<svg id="_图层_2" data-name="图层 2" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1920.5 90.36">
  <defs>
    <style>
      .cls-1 {
        stroke: #02f7bc;
        stroke-width: 6px;
      }

      .cls-1, .cls-2 {
        fill: none;
        stroke-miterlimit: 10;
      }

      .cls-3 {
        opacity: .1;
        stroke-width: 0;
      }

      .cls-2 {
        stroke: #0b8c71;
      }
    </style>
  </defs>
  <g id="_图层_1-2" data-name="图层 1">
    <g id="status-bar">
      <polygon id="bg" class="cls-3" points="540 65.36 0 65.36 0 .36 474.5 .86 540 65.36"/>
      <polyline id="border" class="cls-2" points="474.5 .86 540 65.36 0 65.36"/>
    </g>
    <g id="action-bar">
      <polygon id="bg-2" data-name="bg" class="cls-3" points="1380.5 65.86 1920.5 65.86 1920.5 .86 1446 1.36 1380.5 65.86"/>
      <polyline id="border-2" data-name="border" class="cls-2" points="1445.5 .86 1380 65.36 1920 65.36"/>
    </g>
    <g id="logo-bar">
      <polygon id="bg-3" data-name="bg" class="cls-3" points="1426 .36 498 .36 586 87.36 1338 87.36 1426 .36"/>
      <polyline id="border-3" data-name="border" class="cls-2" points="498 .36 586 87.36 1338 87.36 1426 .36"/>
      <polyline id="border-4" data-name="border" class="cls-1" points="1313.13 87.36 1338 87.36 1359.75 65.86"/>
      <polyline id="border-5" data-name="border" class="cls-1" points="563.75 65.36 586 87.36 609.58 87.36"/>
    </g>
  </g>
</svg>
