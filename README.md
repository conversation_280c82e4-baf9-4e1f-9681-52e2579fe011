# TOCC 交通运行监测调度中心

主要功能包括：大屏展示和业务操作

## 开发背景

工程基于 Vue3 + ElementPlus，通过 ResultAPI 接口的访问后台服务。

## 基本技术

| 名称       | 版本      | 说明                                     |
| ---------- | --------- | ---------------------------------------- |
| Node.js    | `18+`     |                                          |
| 包管理器   | `NPM 10+` |                                          |
| Vite       | `5.3.1`   | 使用 Vite 构建、开发、编译项目           |
| TypeScript | `5.4.0`   | 更严谨的类型，更规范，和 JavaScript 兼容 |
| Vue        | `3.4.29`  |                                          |
| Pinia      | `2.1.7`   | 应用状态管理，对标 React 中的 Redux      |
| vue-router | `4.3.3`   | 单页应用路由                             |
| Apifox     |           | https://app.apifox.com/                  |
| Eslint     | `8.57.0`  | 用于检测代码质量                         |
| Prettier   | `3.2.5`   | 用于格式化代码风格                       |

# 工作流程

## 获取代码

```sh
git clone -b dev http://*************/ys/tocc.git
```

## 安装依赖

```sh
npm install
```

使用你自己的包管理器

```shell
yarn install
pnpm install
```

### 文件结构

```text
tocc/
  |--public/       # 静态资源
  |--src/          # 源代码
    |--assets      # 静态资源
    |--components  # 可复用组件
    |--hooks       # 可复用 Hook
    |--lib         # 通用类库及 d.ts 文件
    |--router      # 路由配置
    |--service     # 接口服务
    |--store       # Pinia stores
    |--utils       # 工具库
    |--views       # 可访问页面
  |--package.json  # NPM package 文件，包含了 NPM 依赖及版本信息
```

## 启动开发服务器（dev server)

```sh
npm run dev
```

使用你自己的包管理器

```shell
yarn dev
pnpm dev
```

## 添加修改

接下来完成你需要开发的功能。

## Linting

Linting 就是梳毛的意思，把代码中的一些杂质梳掉，让它更规范、看起来更整洁。

你应该使用 `Eslint` 检测代码质量，发现有问题时应当及时解决，例如：未引用的变量定义、函数定义、以及包引入等。

对于一些未来可能引用的变量、函数等，通常不建议提交代码。如果有必要提交，你可以在报错行前添加 `// eslint-disable-next-line` 来跳过下一行的检测。

使用以下命令来检测代码质量：

```sh
npm run lint

# or
yarn lint
```

代码质量检测和修复后，应当使用 `Prettier` 来格式化代码：

```sh
npm run format

# or
yarn format
```

注意: 实际上，项目 `.vscode/settings.json` 中已经配置了**代码保存时自动格式化**，大部分情况可以跳过这个步骤的。

## 提交代码

- 先 Review（重新检查） 将你此项工作对相关文件，对于不合理的地方如果有修改记得要重新 **Linting**，然后添加到 _git stash_ 中：

  ```sh
  git add README.md
  git add src/views
  # ...
  ```

  这样写很麻烦，但将 _Commit_ 和 _单次修改_ 关联起来是有意义的。你可以通过你使用的 IDE 来进行**可视化操作**，VSCode 和 IDEA 都支持方便的 _Review_ 和 _工作树处理_ 。

- 当所有与工作相关的文件都已经添加好，如有代码修改应该更新 `package.json` 中的 _Version_ 字段，例如：`1.0.10` -> `1.0.11`。

- 然后，使用 `git commit` 创建一个 commit，在编辑框中添加本次提交的备注，然后保存。建议使用 [约定式提交](https://www.conventionalcommits.org/zh-hans/v1.0.0/) 风格来撰写你的说明，例如：“doc(readme): 修订标题格式”。

​ 或者： `git commit -m 'doc(readme): 添加开发规范'` 。

# 开发规范

## 页面命名规则

大驼峰，如：`LoginPage`、`UserList`。

## 组件命名规则

大驼峰，并且由 2 个及以上的英文单词组成，通常不超过 3 个单词。如：`MenuRenderer`。

## 模块命名规则

通常建立一个和组件同名的目录，例如：`views` / `UserList` / `UserList.vue`。

## JavaScript(TS)文件命名

短横命名，如：`user-service.ts`。

## CSS 文件命名

短横命名，如: `user-list.less`。

## 不要再写 AnyScript

请尽可能清晰地描述元素类型。在不清楚具体类型的情况下，请使用 **unknow** 而不是 **any**。这是因为 **any** 会隐藏类型，从而导致隐形的调用隐患，而 **unknow** 会显式给出警报。

# 附：从0到1搭建一个前端项目的技术参考

版本为推荐可用的，可能包含多个，括号中的是测试实践中使用的版本。名称加粗的，表示需要额外注意的版本，可能产生重大影响。

| 名称             | 版本/类型             | 说明                                                                                            |
| ---------------- | --------------------- | ----------------------------------------------------------------------------------------------- |
| **Node.js**      | 18+、20+ (`v18.18.0`) | 推荐使用 [NVM](https://juejin.cn/post/7000652162950758431) 进行版本管理。                       |
| 包管理器         | NPM / Yarn(`Yarn`)    | Yarn 有更快的速度，没有 NPM 那么多网络问题，而且比 PNPM 稳重。三个包管理，都兼容 `package.json` |
| **Vite**         | `5.3.1`               | 使用 Vite 构建、开发、编译项目                                                                  |
| TypeScript       | `5.4.0`               | 更严谨的类型，更规范，和 JavaScript 兼容                                                        |
| **Vue**          | `3.4.29`              |                                                                                                 |
| Pinia            | `2.1.7`               | 应用状态管理，对标 React 中的 Redux                                                             |
| vue-router       | `4.3.3`               | 单页应用路由                                                                                    |
| Eslint           | `8.57.0`              | 代码质量检测                                                                                    |
| Prettier         | `3.2.5`               | 代码格式化                                                                                      |
| **Element-Plus** | `2.7.7`               | UI 组建库                                                                                       |
| Sass             | `1.77.8`              | CSS 的超集                                                                                      |
| Mock.js          | 1.1.0                 | Mock 数据工具库                                                                                 |

## 搭建步骤

1. 使用 Vite 构建项目

   ```sh
   # 创建，根据导览选择要安装的套件
   yarn create vite
   ```

2. 创建 Git 仓库

   ```sh
   git init
   # 添加 Vite 生成的所有文件
   git add .
   # 创建初始化提交
   git commit -m 'Init commit'
   ```

3. 启动开发服务器

   ```sh
   # 安装依赖
   yarn install

   # 启动
   yarn dev
   ```

4. 测试 Vite 各项命令

   ```sh
   # 预览
   yarn preview
   # 编译
   yarn build
   # 检验代码质量
   yarn lint
   # 格式化代码
   yarn format
   ```

5. 安装 Element Plus

   ```sh
   yarn add element-plus
   ```

   整合按需导入

   ```sh
   npm install -D unplugin-vue-components unplugin-auto-import
   ```

6. 安装 Mock.js

   ```sh
   yarn add mockjs
   yarn add -D @types/mockjs
   ```

## 推荐的 IDE 安装步骤

[VSCode](https://code.visualstudio.com/) + [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) (禁用 Vetur).

## TS 中 导入 `.vue` 导入的类型支持

TypeScript 默认无法处理 `.vue` 文件的导入， 所以我们使用 `vue-tsc` 替换 `tsc` 命令来进行类型校验。在编辑器中，我们需要 [Volar](https://marketplace.visualstudio.com/items?itemName=Vue.volar) 来确保 TypeScript 语言服务识别出 `.vue` 类型。

## 自定义配置

查看 [Vite 配置向导](https://vitejs.dev/config/).

## 项目设置

```sh
yarn
```

### 编译和热加载以进行开发

```sh
yarn dev
```

### 类型检查, 编译和最小化以进行生产

```sh
yarn build
```

### 使用 [ESLint](https://eslint.org/) 进行整理

```sh
yarn lint
```
