{"name": "tocc", "version": "0.1.5", "private": true, "type": "module", "scripts": {"dev": "vite --host 0.0.0.0", "build:test": "run-p \"build-test {@}\" --", "build:ggj": "run-p \"build-ggj {@}\" --", "build": "run-p \"build-only {@}\" --", "preview": "vite preview", "build-ggj": "set NODE_OPTIONS=--max_old_space_size=3000 && vite build --mode ggj", "build-test": "set NODE_OPTIONS=--max_old_space_size=3000 && vite build --mode test", "build-only": "set NODE_OPTIONS=--max_old_space_size=3000 && vite build --mode production", "type-check": "vue-tsc --build --force", "lint": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --ignore-path .gitignore", "lint:fix": "eslint . --ext .vue,.js,.jsx,.cjs,.mjs,.ts,.tsx,.cts,.mts --fix --ignore-path .gitignore", "format": "prettier --write src/", "package": "rm -rf release && mkdir release && zip -qr release/tocc-web.$npm_package_version.zip dist", "fix-memory-limit": "cross-env LIMIT=4096 increase-memory-limit"}, "dependencies": {"@amap/amap-jsapi-loader": "^1.0.1", "@bmapgl-plugin/cluster": "^0.0.7", "@bmapgl-plugin/track": "^0.0.4", "@element-plus/icons-vue": "^2.3.1", "@stomp/stompjs": "^7.1.1", "@types/file-saver": "^2.0.7", "@vexip-ui/utils": "^2.16.1", "ace-builds": "^1.36.2", "animate.css": "^4.1.1", "autoprefixer": "^10.4.13", "axios": "^1.7.2", "bmap-draw": "^1.0.35", "bpmn-js": "^7.3.1", "bpmn-js-properties-panel": "^0.37.2", "camunda-bpmn-moddle": "^4.5.0", "dayjs": "^1.11.13", "echarts": "^5.5.1", "element-plus": "^2.7.7", "file-saver": "^2.0.5", "grid-layout-plus": "^1.0.5", "hls.js": "^1.6.2", "jquery": "^3.7.1", "lodash": "^4.17.21", "mapv": "^2.0.62", "mapvgl": "^1.0.0-beta.191", "nanoid": "^5.0.9", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "postcss-pxtorem": "^5.1.1", "sockjs-client": "^1.6.1", "three": "^0.175.0", "v-scale-screen": "^2.0.0", "vue": "^3.4.29", "vue-baidu-map-3x": "^1.0.39", "vue-router": "^4.4.0", "vue3-ace-editor": "^2.2.4", "vue3-draggable-resizable": "^1.6.5", "vue3-seamless-scroll": "^2.0.1"}, "devDependencies": {"@rushstack/eslint-patch": "^1.8.0", "@tsconfig/node20": "^20.1.4", "@types/jquery": "^3.5.32", "@types/lodash": "^4.17.15", "@types/node": "^20.17.52", "@types/postcss-pxtorem": "^6.1.0", "@vitejs/plugin-vue": "^5.0.5", "@vitejs/plugin-vue-jsx": "^4.1.2", "@vue/eslint-config-prettier": "^9.0.0", "@vue/eslint-config-typescript": "^13.0.0", "@vue/tsconfig": "^0.5.1", "cross-env": "^5.0.5", "eslint": "^8.57.0", "eslint-plugin-vue": "^9.23.0", "increase-memory-limit": "^1.0.3", "less": "^4.2.0", "npm-run-all2": "^6.2.0", "prettier": "^3.2.5", "sass": "^1.77.8", "typescript": "~5.4.0", "vite": "^5.3.1", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^2.0.21"}}