import http from '@/utils/http/http'

const getPageList = (data) => {
  return http.post('/sys/menu/permission/pageList', data)
}

const getList = (data) => {
  return http.post('/sys/menu/permission/getList', data)
}

const create = (data) => {
  return http.post('/sys/menu/permission/create', data)
}

const update = (data) => {
  return http.post('/sys/menu/permission/update', data)
}

const remove = (data) => {
  return http.post('/sys/menu/permission/remove', data)
}

const getById = (data) => {
  return http.post('/sys/menu/permission/getById', data)
}

export default { getPageList, getList, create, remove, getById, update }
