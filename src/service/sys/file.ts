import http from '@/utils/http/http'

export const download = (filename: string) =>
  http.get<Blob>(`/sys/file/download/${filename}`, null, { loading: false, responseType: 'blob' })

export const downloadFile = async (filename: string) => {
  // Fetch blob
  const res = await fileService.download(filename)
  const data = res.data
  const contentType = res.headers['Content-Type'] as string | undefined
  const blob = new Blob([data], { type: contentType ?? 'application/octet-stream' })

  // Create downloading
  const link = document.createElement('a')
  const url = URL.createObjectURL(blob)
  link.href = url

  // Parse the origin filename
  const parts = filename.split('_')
  const saveName = parts.length > 2 ? parts[2] : filename
  link.download = decodeURI(saveName) // Url is the filename unique
  link.click()
}
const fileService = {
  download,
  downloadFile
}

export default fileService
