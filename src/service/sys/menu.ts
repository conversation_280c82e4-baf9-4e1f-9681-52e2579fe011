import http from '@/utils/http/http'

const getPageList: any = (data: any) => {
  return http.post('/sys/menu/pageList', data, {target: '.crud-main'})
}

const create = (data: any) => {
  return http.post('/sys/menu/create', data)
}

const createAll = (data: any) => {
  return http.post('/sys/menu/createAll', data)
}

const update = (data: any) => {
  return http.put('/sys/menu/update', data)
}

const remove = (id: any) => {
  return http.del('/sys/menu/remove/' + id, null)
}

const removeAll = (data: any) => {
  return http.del('/sys/menu/removeAll', data)
}

const getById: any = (id: any) => {
  return http.get(`sys/menu/getById/${id}`, null)
}

const getJsonTree: any = () => {
  return http.post('/sys/menu/getJsonTree', {}, {loading: false})
}

const getMenuPermissionTree = () => {
  return http.get('/sys/menu/getMenuPermissionTree', null)
}

export default { getPageList, create, createAll, remove, removeAll, getById, update, getJsonTree, getMenuPermissionTree }
