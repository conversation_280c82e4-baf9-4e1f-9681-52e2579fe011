import http from '@/utils/http/http'

const pageList: any = (data: any) => {
  return http.get('/sys/setting/pageList', data)
}
const queryList = (data: any) => {
  return http.get('/sys/setting/queryList', data)
}
const getById = (id: string) => {
  return http.get(`/sys/setting/getById/${id}`, {loading: false})
}
const getNextSort = () => {
  return http.get('/sys/setting/getNextSort', {loading: false})
}
const create = (data: any) => {
  return http.post('/sys/setting/create', data)
}
const update = (data: any) => {
  return http.put('/sys/setting/update', data)
}
const remove = (id: string) => {
  return http.del(`/sys/setting/remove/${id}`, null)
}

const removeAll = (ids: string | Array<string>) => {
  return http.del(`/sys/setting/removeAll/${ids}`, null)
}

const refreshCache = (data: any) => {
  return http.get('/sys/setting/refreshCache', data)
}


export default { pageList, queryList, create, remove, removeAll, getById, getNextSort, update, refreshCache }
