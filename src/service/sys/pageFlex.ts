import http from '@/utils/http/http'

const pageList = (data: any) => {
return http.get('/sys/pageFlex/pageList', data)
}
const queryList = (data: any) => {
return http.get('/sys/pageFlex/queryList', data)
}
const getById = (id: string) => {
return http.get(`/sys/pageFlex/getById/${id}`, null, {loading: false})
}
const getLayoutById = (id: string) => {
return http.get(`/sys/pageFlex/getLayoutById/${id}`, null, {loading: false})
}
const getNextSort = () => {
return http.get('/sys/pageFlex/getNextSort', {loading: false})
}
const getLayoutByPageCode = (id: string) => {
return http.get(`/sys/pageFlex/getLayoutByPageCode/${id}`, null, {loading: false})
}
const create = (data: any) => {
return http.post('/sys/pageFlex/create', data)
}
const update = (data: any) => {
return http.put('/sys/pageFlex/update', data)
}
const remove = (ids: string | Array<string>) => {
return http.del(`/sys/pageFlex/remove/${ids}`, null)
}

export default { pageList, queryList, create, remove, getById, getLayoutById, getLayoutByPageCode, getNextSort, update }
