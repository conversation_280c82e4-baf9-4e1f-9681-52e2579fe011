import type { User } from '@/store/user'
import http from '@/utils/http/http'

interface QueryUserList {
  loginName?: string
  unitId?: string
  existIds?: string[]
}

const getList = (data: QueryUserList) => {
  return http.post<Result<User[]>>('/sys/user/list', data)
}

interface QueryUserPage extends PageParam {
  loginName?: string
  userName?: string
  unitId?: string
}

const getPageList: any = (data: QueryUserPage) => {
  return http.get('/sys/user/pageList', data)
}

const create = (data: any) => {
  return http.post('/sys/user/create', data)
}

const update = (data: any) => {
  return http.put('/sys/user/update', data)
}

const updatePassword = (data: any) => {
  return http.post('/sys/user/updatePassword', data)
}

const resetPassword = (id: string) => {
  return http.put(`/sys/user/reset/password/${id}`, {})
}

const remove = (id: any) => {
  return http.del(`/sys/user/remove/${id}`, { loading: false })
}

const removeAll = (ids: any) => {
  return http.del(`/sys/user/removeAll/${ids}`, null)
}

const getById: any = (id: any) => {
  return http.get(`/sys/user/getById/${id}`, null)
}

const getUnAuthPageList: any = (data: any) => {
  return http.post<Result<PageResult<User>>>('/sys/user/unAuthPageList', data, { loading: true })
}

const syncUser = () => {
  return http.put('/sync/syncUser', {})
}

const userService = {
  getList,
  getPageList,
  create,
  remove,
  removeAll,
  getById,
  update,
  updatePassword,
  getUnAuthPageList,
  syncUser,
  resetPassword
}
export default userService
