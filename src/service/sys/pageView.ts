import http from '@/utils/http/http'

const getLayoutById = (id: string) => {
return http.get(`/sys/bigScreen/pageView/getLayoutById/${id}`, null, {loading: false})
}
const getLayoutByPageCode = (id: string) => {
return http.get(`/sys/bigScreen/pageView/getLayoutByPageCode/${id}`, null, {loading: false})
}
const loaderData = (data: any) => {
return http.post('/sys/bigScreen/pageView/loaderData', data)
}

export default { getLayoutById, getLayoutByPageCode, loaderData }
