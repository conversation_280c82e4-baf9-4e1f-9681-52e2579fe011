import type { FileInfo } from '@/store/sys/file-info'
import http from '@/utils/http/http'

export interface QueryFileInfo extends PageParam {
  name?: string
  type?: number
}

export const page = (params: QueryFileInfo) =>
  http.get<Result<PageResult<FileInfo>>>(`/sys/fileInfo/page`, params, { loading: false })

export const detail = (id: number) =>
  http.get<Result<FileInfo>>(`/sys/fileInfo/detail/${id}`, null, { loading: false })

export const saveOrUpdate = (data: FormData) =>
  http.put<Result<FileInfo | null>>('/sys/fileInfo/saveOrUpdate', data, {
    loading: false,
    headers: { 'Content-Type': 'multipart/form-data' }
  })

export const remove = (id: number) => http.del(`/sys/fileInfo/${id}`, null, { loading: false })

export const removeBatch = (ids: number[]) =>
  http.del(`/sys/fileInfo/batch/${ids}`, null, { loading: false })

export const publish = (id: number) =>
  http.put(`/sys/fileInfo/publish/${id}`, null, { loading: false })

export const unpublish = (id: number) =>
  http.put(`/sys/fileInfo/unpublish/${id}`, null, { loading: false })

const fileInfoService = {
  page,
  detail,
  saveOrUpdate,
  remove,
  removeBatch,
  publish,
  unpublish
}

export default fileInfoService
