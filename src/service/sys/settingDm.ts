import http from '@/utils/http/http'

const pageList = (data: any) => {
return http.get('/sys/settingDm/pageList', data)
}
const queryList = (data: any) => {
return http.get('/sys/settingDm/queryList', data)
}
const getById = (id: string) => {
return http.get(`/sys/settingDm/getById/${id}`, null, {loading: false})
}
const getNextSort = () => {
return http.get('/sys/settingDm/getNextSort', {loading: false})
}
const create = (data: any) => {
return http.post('/sys/settingDm/create', data)
}
const update = (data: any) => {
return http.put('/sys/settingDm/update', data)
}
const remove = (ids: string | Array<string>) => {
return http.del(`/sys/settingDm/remove/${ids}`, null)
}

export default { pageList, queryList, create, remove, getById, getNextSort, update }
