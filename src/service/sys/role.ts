import http from '@/utils/http/http'

const getPageList: any = (data: any) => {
  return http.get('/sys/role/pageList', data)
}

const create = (data: any) => {
  return http.post('/sys/role/create', data)
}

const update = (data: any) => {
  return http.put('/sys/role/update', data)
}

const getById: any = (id: any) => {
  return http.get(`/sys/role/getById/${id}`, null)
}

const remove = (id: any) => {
  return http.del(`/sys/role/remove/${id}`, null)
}

const removeAll = (ids: any) => {
  return http.del(`/sys/role/removeAll/${ids}`, null)
}

export default { getPageList, create, remove, removeAll, getById, update }
