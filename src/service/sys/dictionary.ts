import http from '@/utils/http/http'

const pageList = (data: any) => {
  return http.get('/sys/dictionary/pageList', data)
}
const queryTreeList = (data: any) => {
  return http.get('/sys/dictionary/queryTreeList', data, {loading: false})
}
const queryTreeAllList = (data: any) => {
  return http.get('/sys/dictionary/queryTreeAllList', data, {loading: false})
}
const getById = (id: string) => {
  return http.get(`/sys/dictionary/getById/${id}`, null, {loading: false})
}
const getNextSort = (data: any) => {
  return http.get('/sys/dictionary/getNextSort', data, {loading: false})
}
const create = (data: any) => {
  return http.post('/sys/dictionary/create', data)
}
const update = (data: any) => {
  return http.put('/sys/dictionary/update', data)
}
const remove = (ids: string | Array<string>) => {
  return http.del(`/sys/dictionary/remove/${ids}`, null)
}
const queryList: any = (data: any) => {
  return http.get(`/sys/dictionary/queryList`, data, {loading: false})
}

export default { pageList, queryTreeList, queryTreeAllList, getById, getNextSort, create, remove, update, queryList}
