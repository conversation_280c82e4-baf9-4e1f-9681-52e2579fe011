import http from '@/utils/http/http'

export interface Unit {}
export interface UnitTree extends Unit {
  children?: UnitTree[]
}
const getPageList: any = (data: any) => {
  return http.get('/sys/unit/pageList', data)
}

const create = (data: any) => {
  return http.post('/sys/unit/create', data)
}

const update = (data: any) => {
  return http.put('/sys/unit/update', data)
}

const syncUnit = () => {
  return http.put('/sync/syncOrgan', {})
}

const remove = (id: any) => {
  return http.del(`/sys/unit/remove/${id}`, null)
}

const removeAll = (ids: any) => {
  return http.del(`/sys/unit/removeAll/${ids}`, null)
}

const getById: any = (id: any) => {
  return http.get(`sys/unit/getById/${id}`, null)
}

const getUnitTree = (data: any) => {
  return http.post<Result<UnitTree[]>>('/sys/unit/getUnitTree', data, {
    loading: false,
    target: '.ys-crud-main'
  })
}

const loading = (isLoading: boolean) => {
  http.loading(isLoading)
}

const unitService = {
  getPageList,
  create,
  remove,
  removeAll,
  getById,
  update,
  getUnitTree,
  loading,
  syncUnit
}
export default unitService
