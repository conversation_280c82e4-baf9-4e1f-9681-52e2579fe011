import http from '@/utils/http/http'

const pageList = (data: any) => {
return http.get('/sys/pageComponent/pageList', data)
}
const queryList = (data: any) => {
return http.get('/sys/pageComponent/queryList', data)
}
const getById = (id: string) => {
return http.get(`/sys/pageComponent/getById/${id}`, null, {loading: false})
}
const getNextSort = () => {
return http.get('/sys/pageComponent/getNextSort', {loading: false})
}
const create = (data: any) => {
return http.post('/sys/pageComponent/create', data)
}
const update = (data: any) => {
return http.put('/sys/pageComponent/update', data)
}
const remove = (ids: string | Array<string>) => {
return http.del(`/sys/pageComponent/remove/${ids}`, null)
}

export default { pageList, queryList, create, remove, getById, getNextSort, update }
