import http from '@/utils/http/http'

const getPageList = (data: any) => {
  return http.post('/sys/role/pageList', data)
}

const create = (data: any) => {
  return http.post('/sys/role/roleuser/create', data)
}

const getListByRoleId: any = (roleId: any) => {
  return http.get(`/sys/role/roleuser/queryListByRoleId/${roleId}`, null)
}

const remove = (data: any) => {
  return http.post('/sys/role/remove', data)
}

const getById = (data: any) => {
  return http.post('/sys/role/getById', data)
}

export default { getPageList, create, remove, getById, getListByRoleId }
