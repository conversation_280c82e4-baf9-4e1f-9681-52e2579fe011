import http from '@/utils/http/http'

const login: any = (data: any) => {
  //const menus: Menu[] = []
  const res: any = http.post('/login', data, { target: 'button', loading: false })
  //const menuTree: Menu[] = res.data.data.menuTree
  // menuTree.forEach((item, index, array) => {
  //   console.log(item.label, item.menuType)
  //   if(item.menuType == '1') {
  //     menus.push(item)
  //   }
  // })
  // res.data.data.menuTree = menus
  return res
}

// sso login
const ssoLogin: any = (tickle: string) => {
  const res: any = http.post(`/sso/login?tickle=${tickle}`, {}, { target: 'button' })
  return res
}

const loginOut: any = (data: any) => {
  // return http.get('/logout', data)
  return http.get('/sso/logout', data)
}

export default {
  login,
  loginOut,
  ssoLogin
}
