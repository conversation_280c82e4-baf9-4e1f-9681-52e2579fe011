import http from '@/utils/http/http'

const pageList = (data: any) => {
return http.get('/basic/warnRule/pageList', data)
}
const queryList = (data: any) => {
return http.get('/basic/warnRule/queryList', data)
}
const getById = (id: string) => {
return http.get(`/basic/warnRule/getById/${id}`, null, {loading: false})
}
const create = (data: any) => {
return http.post('/basic/warnRule/create', data)
}
const update = (data: any) => {
return http.put('/basic/warnRule/update', data)
}
const remove = (ids: string | Array<string>) => {
return http.del(`/basic/warnRule/remove/${ids}`, null)
}

export default { pageList, queryList, create, remove, getById, update }
