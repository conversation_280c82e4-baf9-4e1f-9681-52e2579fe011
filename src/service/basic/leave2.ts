import http from '@/utils/http/http'

const getPageList: any = (data: any) => {
    return http.get("/basic/leave/pageList", data);
};

const create = (data: any) => {
    return http.post("/basic/leave/create", data);
};

const update = (data: any) => {
    return http.put("/basic/leave/update", data);
};

const apply = (data: any) => {
    return http.post("/basic/leave2/apply", data);
};

const remove = (id: any) => {
    return http.del(`/basic/leave/remove/${id}`, null);
};

const removeAll = (ids: any) => {
    return http.del(`/basic/leave/removeAll/${ids}`, null);
};

const getById: any = (id: any) => {
    return http.get(`basic/leave/getById/${id}`, null);
};
/*---------------------工作流-------------*/

const findAllTask: any = (id: any) => {
    return http.get(`basic/leave/findAllTask/${id}`, null);
};

const getHistoryTask: any = (id: any) => {
    return http.get(`basic/leave/getHistoryTask/${id}`, null);
};

const completeTask = (data: any) => {
    return http.post(`basic/leave2/completeTask`, data)
}

const backTask = (data: any) => {
    return http.post(`basic/leave2/backTask`, data)
}


export default {getPageList, create, remove, removeAll, getById, update, findAllTask, getHistoryTask, apply, completeTask , backTask}