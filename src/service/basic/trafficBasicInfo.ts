import http from '@/utils/http/http'

/** 基础设施 */
const getInfraList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getInfraList', data, { loading: true })
}

/** 地图信息-基础信息-桥梁 */
const adsExpresswayMapDataBirdgeInfo: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/adsExpresswayMapDataBirdgeInfo', data, {
    loading: true
  })
}

/** 地图信息-基础信息-涵洞 */
const adsExpresswayMapDataCulvertInfo: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/adsExpresswayMapDataCulvertInfo', data, {
    loading: true
  })
}

/** 地图信息-基础信息-隧道 */
const adsExpresswayMapDataTunnelInfo: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/adsExpresswayMapDataTunnelInfo', data, {
    loading: true
  })
}

/** 综合枢纽 */
const getTransportationHubList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getTransportationHubList', data, { loading: true })
}

/** 综合枢纽视频点 */
const getHubCameraList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getHubCameraList', data, { loading: true })
}

/** 枢纽 */
const getHubList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getHubList', data, { loading: true })
}

/**
 * 获取监控数据
 */
const getCameraList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getCameraList', data, { loading: true })
}

/**
 * 获取指定类型的摄像头列表
 */
const getCameraListByType = (data: any) => {
    return http.get(`/basic/traffic/basicinfo/getCameraListByType`, data, { loading: true });
}

/**
 * 获取拥堵路段关联摄像头
 */
const getRoadCongestionCameraList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getRoadCongestionCameraList', data, { target: '.road-congestion-loading' })
}

/**
 * 获取旅游公路观景台
 */
const getTourismStationList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getTourismStationList', data, { loading: true })
}
/**
 * 机场宽表的坐标点
 */
const getAirportInfoList: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getAirportInfoList', data, { loading: true })
}
/**
 * 机场宽表的坐标点
 */
const getRealWeather: any = (data: any) => {
  return http.post('/basic/traffic/basicinfo/getRealWeather', data, { loading: false })
}

export default {
  getInfraList,
  getTransportationHubList,
  getHubCameraList,
  getHubList,
  getCameraList,
  getCameraListByType,
  getRoadCongestionCameraList,
  getTourismStationList,
  adsExpresswayMapDataBirdgeInfo,
  adsExpresswayMapDataCulvertInfo,
  adsExpresswayMapDataTunnelInfo,
  getAirportInfoList,
  getRealWeather
}
