import http from '@/utils/http/http'

const pageList: any = (data: any) => {
  return http.get('/basic/duty/pageList', data)
}
const getList = (data: any) => {
  return http.get('/basic/duty/getList', data)
}
const getById = (id: string) => {
  return http.get(`/basic/duty/getById/${id}`, {loading: false})
}
const create = (data: any) => {
  return http.post('/basic/duty/create', data)
}
const update = (data: any) => {
  return http.put('/basic/duty/update', data)
}
const remove = (id: string) => {
  return http.del(`/basic/duty/remove/${id}`, null)
}

const removeAll = (ids: string | Array<string>) => {
  return http.del(`/basic/duty/removeAll/${ids}`, null)
}


export default { pageList, getList, create, remove, removeAll, getById, update}
