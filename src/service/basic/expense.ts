import http from '@/utils/http/http'

const getPageList = (data: any) => {
    return http.get('/basic/expense/pageList', data)
}
const queryList = (data: any) => {
    return http.get('/basic/expense/queryList', data)
}
const getById = (id: string) => {
    return http.get(`/basic/expense/getById/${id}`, null, { loading: false })
}
const create = (data: any) => {
    return http.post('/basic/expense/create', data)
}
const update = (data: any) => {
    return http.put('/basic/expense/update', data)
}
const remove = (ids: string | Array<string>) => {
    return http.del(`/basic/expense/remove/${ids}`, null)
}
const apply = (data: any) => {
    return http.post("/basic/expense/apply", data);
};
const removeAll = (ids: any) => {
    return http.del(`/basic/expense/removeAll/${ids}`, null);
};
const completeTask = (data: any) => {
    return http.post(`basic/expense/completeTask`, data)
}
export default { getPageList, queryList, create, remove, getById, update, apply, removeAll, completeTask }
