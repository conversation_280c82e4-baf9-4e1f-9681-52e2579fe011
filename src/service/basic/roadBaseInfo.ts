import http from '@/utils/http/http'

const getKeyRoadBaseInfoList: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

const getKeyRoadDetailInfoList: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoDetailList', data, { loading: false })
}

const getRoadSegListById: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getRoadSegListById', data, { loading: false })
}

/**获取高速公路路网数据 */
const getHighwayList: any = (data: any) => {
  data.cacheKey = 'highway'
  //data.filter = "type = '高速'"
  if (data.filter) {
    data.filter = data.filter + " and type='高速'"
  } else {
    data.filter = "type='高速'"
  }
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, {
    loading: true,
    timeout: 60000 * 3
  })
}

/**获取国道路网数据 */
const getNationalwayList: any = (data: any) => {
  data.cacheKey = 'gd'
  data.filter = "type='国省干线' and r_type='国道'"
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

/**获取省道路网数据 */
const getProvincialwayList: any = (data: any) => {
  data.cacheKey = 'sd'
  data.filter = "type='国省干线' and r_type = '省道'"
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

/**获取国省干线路网数据 */
const getNationalAndProvincialwayList: any = (data: any) => {
  data.cacheKey = 'gs'
  if (data.filter) {
    data.filter = data.filter + " and type='国省干线'"
  } else {
    data.filter = "type='国省干线'"
  }
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

/**获取农村公路路网数据 */
const getCountrywayList: any = (data: any) => {
  data.cacheKey = 'country'
  if (data.filter) {
    data.filter = data.filter + " and type='农村公路'"
  } else {
    data.filter = "type='农村公路'"
  }
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

/**获取旅游公路路网数据 */
const getTouristwayList: any = (data: any) => {
  data.cacheKey = 'tourist'
  if (data.filter) {
    data.filter = data.filter + " and r_type='旅游公路'"
  } else {
    data.filter = "r_type='旅游公路'"
  }
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

/**获取铁路路网数据 */
const getRailwayList: any = (data: any) => {
  data.cacheKey = 'railway'
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

/**获取三级道路路网数据（农村公路）-县道 */
const getTertiarywayList: any = (data: any) => {
  data.cacheKey = 'xiand'
  data.filter = "type='农村公路' and r_type = '县道'"
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}

/**获取三级道路路网数据（农村公路）-县道 */
const getXiangwayList: any = (data: any) => {
  data.cacheKey = 'xiangd'
  data.filter = "type='农村公路' and r_type = '乡道'"
  return http.post('/basic/roadBaseInfo/getKeyRoadBaseInfoList', data, { loading: true })
}
/** 道路技术状况 */
const getRoadTechnicalConditionList: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getRoadTechnicalConditionList', data, { loading: false })
}

/** 实时路况数据 */
const getRealTimeTrafficList: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getRealTimeTrafficList', data, { loading: false })
}

/** 实时路况数据 */
const getRealTimeRoadCongestion: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getRealTimeRoadCongestion', data, { loading: true })
}

/** 实时路况数据 */
const getRealTimeRoadCongestionV2: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getRealTimeRoadCongestionV2', data, { loading: true })
}

/** 旅游公路 */
const getTourismRoadList: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getTourismRoadList', data, { loading: false })
}

/**
 * 获取车辆定位数据
 */
const getVehicleLocationList: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getVehicleLocationList', data, { loading: false })
}

/**
 * 根据id获取基础数据
 */
const getRoadBaseInfo: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getRoadBaseInfo', data, { loading: false })
}

/**
 * 根据id获取国省干线-基础数据-道路
 */
const getTrunkRoadBaseInfo: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getTrunkRoadBaseInfo', data, { loading: false })
}

/**
 * 获取所有国省干线-基础数据-道路
 */
const getAllRoadHealth: any = (data: any) => {
  return http.post('/basic/roadBaseInfo/getAllRoadHealth', data, { loading: false })
}

export default {
  getKeyRoadBaseInfoList,
  getRoadSegListById,
  getHighwayList,
  getNationalwayList,
  getProvincialwayList,
  getNationalAndProvincialwayList,
  getTertiarywayList,
  getXiangwayList,
  getRoadTechnicalConditionList,
  getRealTimeTrafficList,
  getRealTimeRoadCongestion,
  getRealTimeRoadCongestionV2,
  getVehicleLocationList,
  getTourismRoadList,
  getCountrywayList,
  getTouristwayList,
  getRoadBaseInfo,
  getTrunkRoadBaseInfo,
  getKeyRoadDetailInfoList,
  getAllRoadHealth,
  getRailwayList
}
