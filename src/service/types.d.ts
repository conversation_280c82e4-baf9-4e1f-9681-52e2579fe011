interface AppPagination {
  page: number
  pageSize: number
}

interface PageParam {
  pageNumber: number
  pageSize: number
}

interface Result<T> {
  code: string
  msg: string
  data: T
}

interface PageResult<T> {
  total: number
  size: number
  current: number
  pages: number
  records: T[]
}

interface DVisualParam {
  s?: number
  n?: number
  apiId?: string
  filter?: string
  type?: string
}
interface DVisualResult<T> {
  message: string
  state: number
  list: T
  n: number
  s: number
  total: number
}

/**高级查询 左侧树 */
interface AdvancedQueryTree {
  id: string,
  label: string
  children?: Tree[]
}

interface AdvancedQueryModel {
  title: string
  open: boolean
  tree: Array<any>
  advancedQueryForm: any //查询条件
  tableList: Array<any>
  tableHeight?: int //表格的高度
}

interface BasicLineModel {
  xAxisData: object
  seriesData: object
}
