import http from '@/utils/http/http'

const pageList = (data: any) => {
    return http.get('/mongodb/user/pageList', data)
}

const remove = (ids: string | Array<string>) => {
    return http.del(`/mongodb/user/remove/${ids}`, null)
}


const create = (data: any) => {
    return http.post('/mongodb/user/create', data)
}


const update = (data: any) => {
    return http.put('/mongodb/user/update', data)
}


const getById: any = (id: any) => {
    return http.get(`/mongodb/user/getById/${id}`, null)
}


export default {pageList, remove, create, update, getById}
