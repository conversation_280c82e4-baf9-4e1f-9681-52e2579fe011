import http from '@/utils/http/http'
import type { McuChannelGroup } from '@/store/mcu/channel-group.ts'

export const sync = () => http.post('/mcu/channelGroup/sync')
export const page = (params: Record<string, any>) =>
  http.get<Result<PageResult<McuChannelGroup>>>('/mcu/channelGroup/page', params, {
    loading: false
  })

const channelGroupService = {
  sync,
  page
}

export default channelGroupService
