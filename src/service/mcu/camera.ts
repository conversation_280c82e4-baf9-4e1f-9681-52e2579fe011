import type { McuCamera } from '@/store/mcu/camera'
import http from '@/utils/http/http'

export interface SearchParams extends AppPagination {
  keyword: string
  status: string
  type: string
}
export const sync = () => http.post('/mcu/camera/sync', null, { loading: false })
export const jksync = () => http.post('/mcu/camera/jksync', null, { loading: false })
export const page = (params: SearchParams) =>
  http.get<Result<PageResult<McuCamera>>>(
    '/mcu/camera/page',
    {
      keyword: params.keyword,
      curPage: params.page,
      pageSize: params.pageSize,
      status: params.status,
      type: params.type
    },
    { loading: false }
  )
const getVideoUrl: any = (id: any) => {
  return http.get(`/mcu/camera/getVideoUrl/${id}`, null)
}
const cameraService = {
  sync,
  page,
  jksync,
  getVideoUrl
}

export default cameraService
