import axios from 'axios'

export interface FetchRoleParam extends AppPagination {
  name: string
}

export const fetch = async (params: FetchRoleParam) => {
  const { page, pageSize } = params
  const data = {
    s: (page - 1) * pageSize,
    n: pageSize,
    name: params.name
  }
  return (await axios.post('/apiV1/dput/role/list', data)).data
}

const roleService = {
  fetch
}

export default roleService
