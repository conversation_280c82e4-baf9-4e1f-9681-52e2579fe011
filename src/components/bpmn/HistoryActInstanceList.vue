<template>
    <el-row style="margin: 10px 0;">
        <el-col :span="12">
            <div style="display: flex">
                <div style="width: 4px; background: #409eff; height: 28px; vertical-align: middle"></div>
                <div style="display: flex; height: 28px; align-items: center; margin-left: 6px">审批历史</div>
            </div>
        </el-col>
        <el-col :span="12">
            <div style="display: flex; height: 28px; align-items: center; justify-content: end">
                <el-button type="primary" style="margin-right: 4px" icon="PictureFilled" size="small" @click="showWorkFlowImg">流程图</el-button>
            </div>
        </el-col>
    </el-row>
    <el-table :data="historyActInstanceList" style="width: 100%;">
        <el-table-column prop="activityType" label="动作类型" width="160" />
        <el-table-column prop="taskName" label="任务名称" width="120" />
        <el-table-column prop="assignee" label="办理人" width="120" />
        <el-table-column prop="message" label="审批意见" width="200" />
        <el-table-column prop="startTime" label="开始时间" width="172" />
        <el-table-column prop="endTime" label="结束时间" width="172" />
    </el-table>
    <el-dialog class="main-form-dialog" v-model="dialogFormVisible" title="流程图" style="width: 1000px" :close-on-click-modal="false">
        <work-flow-img ref="workFlowImgRef" :processInstanceId="processInstanceId"></work-flow-img>
    </el-dialog>
</template>

<script lang="ts" setup name="PieChart">
import { ref, onMounted, nextTick } from 'vue'
import { ElTable } from 'element-plus'
import bpmnEngineApi from '@/service/bpmn/engine'
import WorkFlowImg from '@/components/bpmn/WorkFlowImg.vue'
const dialogFormVisible = ref(false)
const processInstanceId = ref('')
const historyActInstanceList = ref([])
const workFlowImgRef = ref<InstanceType<typeof WorkFlowImg>>()

onMounted(() => {
    console.log('--------------historyAcinstanceList-------');
})

defineExpose({
    getHistoryActInstanceList: (str: string) => {
        processInstanceId.value = str
        getList()
    }
})

const getList = async () => {
    const res = await bpmnEngineApi.getHistoryActInstanceList(processInstanceId.value)
    historyActInstanceList.value = res.data.data
    //let res = await bpmnEngineApi.findAllTask(dataForm.instanceId)
}

const showWorkFlowImg = async() => {
    dialogFormVisible.value = true
    await nextTick()
    workFlowImgRef.value.showWorkFlow(processInstanceId.value)
}
</script>
