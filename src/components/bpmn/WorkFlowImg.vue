<template>
    <div>
        <el-image style="width: 100%; background-color: beige" :src="bpmnImage" fit="contain" />
    </div>
</template>

<script setup name="PieChart">
import axios from 'axios';
import {ref, onMounted} from 'vue'
import bpmnEngineApi from '@/service/bpmn/engine'

const bpmnImage = ref()
// const props = defineProps(['processInstanceId'])
const processInstanceId = ref('')

onMounted(() => {
    console.log('--------------bpmn.work-flow-img--------');
});

defineExpose({
    showWorkFlow: (str)=> {
        processInstanceId.value = str
        _init()
    }
})

const _init = async () => {
    const res = await bpmnEngineApi.workFlowImage(processInstanceId.value)
    bpmnImage.value = URL.createObjectURL(new Blob([res.data], { type: 'image/svg+xml;charset=utf-8' }));
}
</script>