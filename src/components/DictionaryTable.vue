<template>
  <div>
    <div class="crud-main-content">
      <div class="crud-main-query">
        <el-form :model="queryParam" :inline="true" class="query-form-inline">
          <el-form-item label="关键字">
            <el-input v-model="queryParam.queryKeyword" placeholder="字典名称 / 字典编码" clearable />
          </el-form-item>
          <el-form-item label="字典状态">
            <el-select v-model="queryParam.status" placeholder="请选择" clearable>
              <el-option label="正常" value="0" />
              <el-option label="无效" value="1" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary"
              @click="getPageList">&nbsp;&nbsp;&nbsp;&nbsp;查询&nbsp;&nbsp;&nbsp;&nbsp;</el-button>
          </el-form-item>
        </el-form>
      </div>
      <div class="mb-4 crud-main-content-toolbar">
        <el-button type="primary" :icon="Plus" @click="openAdd()">新增</el-button>
        <el-button type="danger" :icon="Delete" @click="_Delete()" :disabled="selectedRows.length === 0">删除</el-button>
        <el-tag v-if="queryParam.parentCode != null" style="float: right" type="primary">
          【字典名称：{{ queryParam.parentName }}】【字典编码：{{ queryParam.parentCode }}】
        </el-tag>
      </div>
      <el-table
        :data="dataList"
        style="width: 100%; height: 280px"
        show-overflow-tooltip
        @selection-change="handleSelectionChange"
        @sort-change="handleSortChange"
        header-cell-class-name="tocc-table-header-cell"
        header-row-class-name="tocc-table-header-row"
        row-class-name="tocc-table-row"
      >
        <el-table-column type="selection" width="55" />
        <el-table-column type="index" label="编号" width="80" align="center" />
        <!-- <el-table-column prop="id" label="ID" width="120" /> -->
        <!-- <el-table-column prop="dicCode" label="字典编码" align="center" sortable="custom"/> -->
        <el-table-column prop="dicName" label="字典名称" align="center" />
        <el-table-column prop="dicCode" label="字典编码" align="center" />
        <el-table-column prop="dicSort" label="字典排序" width="100" align="center" />
        <el-table-column prop="status" label="字典状态" width="100" align="center">
          <template #default="scope">
            <el-tag v-if="scope.row.status == 0" type="success">正常</el-tag>
            <el-tag v-if="scope.row.status == 1" type="danger">无效</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="remark" label="备注" width="80" align="center" />
        <!-- <el-table-column prop="createTime" label="创建时间" /> -->
        <el-table-column prop="updateTime" label="更新时间" align="center" />
        <el-table-column fixed="right" label="操作" min-width="30" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="openUpdate(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="crud-main-content-pagination">
        <el-pagination v-model:current-page="queryParam.pageNumber" v-model:page-size="queryParam.pageSize"
          :page-sizes="[defPageSize, 10, 25, 50, 100]" layout="->,total, sizes, prev, pager, next" :total="queryParam.total"
          @current-change="handleCurrentChange" />
      </div>
    </div>

    <!-- 弹出框 -->
    <el-dialog v-model="dialogFormVisible" :title="dataForm.id ? '修改字典' : '新增字典'" width="680px"
      :close-on-click-modal="false" :close-on-press-escape="false" class="main-form-dialog">
      <el-form :model="dataForm" label-width="100px" style="max-width: 600px" :rules="rules" ref="ruleFormRef"
        >
        <el-form-item label="父级字典">
          <!-- <el-tree-select v-if="dialogFormVisible"
					placeholder="默认顶级"
					clearable
				    v-model="dataForm.parentCode"
					:default-expanded-keys="[]"
				    lazy
					check-strictly
				    :load="loadNode"
				    :props="treeProps"
					@node-click="handleNodeClick"
				  /> -->
          <el-tree-select v-if="dialogFormVisible" placeholder="默认顶级" clearable v-model="dataForm.parentCode"
            :data="dictTree" :default-expanded-keys="[]" check-strictly :props="treeProps"
            @node-click="handleNodeClick" @change="initForm" ref="dictTreeRef"/>
        </el-form-item>
        <el-form-item label="字典名称" prop="dicName">
          <el-input v-model="dataForm.dicName" placeholder="必填" />
        </el-form-item>
        <el-form-item label="字典编码" prop="dicCode">
          <el-input v-model="dataForm.dicCode" placeholder="必填" />
        </el-form-item>
        <el-form-item label="字典排序" prop="dicSort">
          <el-input-number v-model="dataForm.dicSort" controls-position="right" style="width: 100%" :min="-1" :max="9999"
            :step="1" step-strictly placeholder="必填" />
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="dataForm.remark" type="textarea" />
        </el-form-item>
		<el-form-item v-if="props.classify == null" label="字典分类" prop="classify">
		  <el-select v-model="dataForm.classify" placeholder="必填" clearable>
		    <el-option label="系统字典" value="1" />
		    <el-option label="业务字典" value="2" />
		  </el-select>
		</el-form-item>
        <el-form-item label="字典状态" prop="status">
          <el-switch v-model="dataForm.status" placeholder="必填" inline-prompt active-text="正常" inactive-text="无效"
            active-value="0" inactive-value="1" size="large" />
        </el-form-item>
      </el-form>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" @click="_AddorUpdate(ruleFormRef)"> 确定 </el-button>
          <el-button @click="dialogFormVisible = false">取消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script lang="ts" setup>
import { ElTable } from 'element-plus'
import { Delete, Plus } from '@element-plus/icons-vue'
import { ElMessageBox, ElMessage } from 'element-plus'
import type Node from 'element-plus/es/components/tree/src/model/node'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { reactive, ref, onMounted, nextTick, getCurrentInstance } from 'vue'
import dictionaryApi from '@/service/sys/dictionary'
const emit = defineEmits(['update'])
const props = defineProps({
  classify: {
    type: String,
    default: null
  }
})
const treeProps = {
  label: 'label',
  children: 'children',
  isLeaf: 'leaf'
}

const dictTreeRef = ref()
const dialogFormVisible = ref(false)
const formLabelWidth = '140px'


const ruleFormRef = ref<FormInstance>()

const defaultAsc = true
//const defaultSortBy = 'dic_sort'
const defaultSortBy = null
const defPageNumber = getCurrentInstance()!.appContext.config.globalProperties.$pageNumber
const defPageSize = getCurrentInstance()!.appContext.config.globalProperties.$pageSize

const queryParam = reactive<any>({
  queryKeyword: '',
  parentId: null,
  parentCode: null,
  parentName: null,
  dicName: '',
  dicCode: '',
  status: '',
  classify: props.classify,
  asc: defaultAsc,
  sortBy: defaultSortBy,
  pageNumber: defPageNumber,
  pageSize: defPageSize,
  total: 0
})

const dataForm = reactive<any>({
  id: null,
  parentId: null,
  parentCode: null,
  dicName: null,
  dicCode: '',
  classify: props.classify,
  dicSort: 1,
  status: '0',
  remark: ''
})

const value = ref()

const dataList = ref([])

onMounted(() => {
  //console.log('onMounted')
  _init()
})

function _init() {
  getPageList()
}

//懒加载结构树
const loadNode = async (node: Node, resolve: (data: Tree[]) => void, reject: () => void) => {
  if (node.level === 0) {
    dictionaryApi
      .queryTreeList({ classify: props.classify, parentCode: null })
      .then((res: any) => {
        return resolve(res.data.data)
      })
      .catch((err) => {
        return reject()
      })
  } else {
    dictionaryApi
      .queryTreeList({ classify: props.classify, parentCode: node.data.value })
      .then((res: any) => {
        return resolve(res.data.data)
      })
      .catch((err) => {
        return reject()
      })
  }
}

interface Tree {
  id: number
  label: string
  children?: Tree[]
  checkList?: String[]
  classify: string
}
const dictTree = ref<Tree[]>([])

/** 获取菜单结构树 */
async function getDictTree() {
  const res:any = await dictionaryApi.queryTreeAllList({ classify: props.classify, parentCode: null })
  dictTree.value = res.data.data
}

const handleNodeClick = (nodeData: Tree) => {
  dataForm.parentId = nodeData.id
  dataForm.classify = nodeData.classify
}

const _AddorUpdate = async (formEl: FormInstance | undefined) => {
  if (dataForm.id == null || dataForm.id == '') {
    _Add(formEl)
  } else {
    _Update(formEl)
  }
}

/** 新增 */
const openAdd = async () => {
  getDictTree()
  delete dataForm.id
  dialogFormVisible.value = true
  nextTick(() => {
    resetForm(ruleFormRef.value)
	initForm()
  })
}

const initForm = async () => {
	const res:any = await dictionaryApi.getNextSort({ classify: dataForm.classify, parentCode: dataForm.parentCode })
	const nextSort = res.data.data
	if(nextSort){
		dataForm.dicSort = nextSort
	}
	const node:Node = dictTreeRef.value.getNode(dataForm.parentCode)
	if(node){
		dataForm.classify = node.data.classify
	}
}

const _Add = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (!valid) {
      return
    }
    const res = await dictionaryApi.create(dataForm)
    dialogFormVisible.value = false
    ElMessage.success('保存成功！')
    _init()
    nextTick(() => {
      emit('update', queryParam.parentId)
    })
  })
}

/**U 修改 */
const openUpdate = async (data: any) => {
  getDictTree()
  dialogFormVisible.value = true
  const res:any = await dictionaryApi.getById(data.id)
  for (let key in res.data.data) {
    dataForm[key] = res.data.data[key]
  }
}

const _Update = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (!valid) {
      return
    }
    const res = await dictionaryApi.update(dataForm)
    dialogFormVisible.value = false
    ElMessage.success('保存成功！')
    _init()
	//console.log(queryParam.parentId, dataForm.parentId)
    nextTick(() => {
      emit('update', queryParam.parentId)
    })
	if(dataForm.parentId){
		nextTick(() => {
		  emit('update', dataForm.parentId)
		})
	}
  })
}

/*** D 删除 */
const selectedRows = ref<Array<any>>([])
const handleSelectionChange = (val: Array<any>) => {
  selectedRows.value = val
}

function _Delete() {
  ElMessageBox.confirm('请确认是否删除数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const ids: Array<string> = []
    selectedRows.value.forEach((row) => {
      ids.push(row.id)
    })
    const res = await dictionaryApi.remove(ids)
    _init()
    nextTick(() => {
      emit('update', queryParam.parentId)
    })
    selectedRows.value = [] // 删除后清空选中项
  })
}

const setDicCode = (parentId: string, parentName: string, parentCode: string) => {
  queryParam.parentId = parentId
  queryParam.parentName = parentName
  queryParam.parentCode = parentCode
  dataForm.parentCode = parentCode
  _init()
}

/**查询 */
async function getPageList() {
  const res:any = await dictionaryApi.pageList(queryParam)
  dataList.value = res.data.data.records
  queryParam.total = res.data.data.total
}

/**排序**/
const handleSortChange = (val: {column: any, prop: string, order: any }) => {
  //console.dir(val)
  if (!val.order) {
    queryParam.asc = defaultAsc
    queryParam.sortBy = defaultSortBy
  } else {
    queryParam.asc = val.order === 'ascending' ? true : false
    queryParam.sortBy = val.prop.replace(/([A-Z])/g, '_$1').toLowerCase()
  }
  getPageList()
}

const handleCurrentChange = (current: any) => {
  queryParam.pageNumber = current
  getPageList()
}

/****----------------------校验------------------------- */

const rules = reactive<FormRules<any>>({
  dicName: [
    {
      required: true,
      message: '字典名称不能为空',
      trigger: 'change'
    }
  ],
  dicCode: [
    {
      required: true,
      message: '字典编码不能为空',
      trigger: 'change'
    }
  ],
  status: [
    {
      required: true,
      message: '字典状态不能为空',
      trigger: 'change'
    }
  ],
  classify: [
    {
      required: true,
      message: '字典分类不能为空',
      trigger: 'change'
    }
  ],
  dicSort: [
    {
      required: true,
      message: '字典排序不能为空',
      trigger: 'change'
    }
  ]
})

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

defineExpose({
  setDicCode
})
</script>

<style></style>
