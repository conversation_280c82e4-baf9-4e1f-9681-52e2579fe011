<template>
  <div class="breadcrumb-bar flex align-center">
    <div @click="changeCollapse()" style="cursor: pointer;" class="flex justify-center w-12">
	  <ElIcon v-if="isCollapse"><Expand /></ElIcon>
	  <ElIcon v-else><Fold /></ElIcon>
    </div>
    <div>
      <ElBreadcrumb separator="/">
        <ElBreadcrumbItem v-for="crumb in props.crumbs" :to="{path: crumb.path}" :key="crumb.path">
          <template v-if="typeof crumb.label === 'string'">
            {{ crumb.label }}
          </template>
          <template v-else>
            <component :is="crumb.label" />
          </template>
        </ElBreadcrumbItem>
      </ElBreadcrumb>
    </div>
  </div>
</template>

<script lang="tsx" setup>
import { type Component } from 'vue'
import { computed } from 'vue'
import { ElBreadcrumb, ElBreadcrumbItem } from 'element-plus'
import { Fold, Expand } from '@element-plus/icons-vue'
import useAppStore from "@/store/app";
const appStore = useAppStore();
const isCollapse = computed(() => appStore.isCollapse);

export interface CrumbData {
  label: string | Component
  to?: string
  path?: string
}
const props = defineProps<{
  crumbs: CrumbData[]
}>()

const changeCollapse = () => {
	appStore.changeCollapse()
}
</script>

<style lang="scss" scoped>
.breadcrumb-bar {
  padding: 10px 0;
  background: white;
}
</style>
