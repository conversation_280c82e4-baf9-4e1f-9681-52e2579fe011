<template>
  <div class="data-list-container">
    <ul class="data-list">
      <li class="data-list-header">
        <div class="data-list-header-item" :style="{ ...item.style }" v-for="(item, index) in tableHead.list"
          :key="index">
          <span v-if="item.type === 'sort'">{{ item.label }}</span>
          <span v-else-if="item.type === 'text'">{{ item.label }}</span>
          <slot v-else-if="item.type === 'slot'" :name="item.slot" :item="item" :index="index"></slot>
        </div>
      </li>
      <div class="data-list-body">
        <vue3-seamless-scroll ref="scrollRef" v-model="tableBody.roll" :list="tableData" class="scroll"
          v-bind="tableBody.attrs">
          <li v-for="(event, index) in tableData" :key="event.id" class="data-list-item">
            <div class="data-list-item-type" :style="{ ...item.style }" v-for="(item, idx) in tableBody.list"
              :key="idx">
              <div class="data-list-item-index" v-if="item.type === 'sort'">
                <span :style="getRowStyle(index)" style="padding:0.2rem 0.5125rem;">{{ index + 1 }}</span>
              </div>
              <span v-else-if="item.type === 'text'">{{ event[item.key] }}</span>
              <span v-else-if="item.type === 'time'">{{ formatTimestampToTime(event[item.key]) }}</span>
              <slot v-else-if="item.type === 'slot'" :name="item.slot" :item="event" :index="index"></slot>
              <slot v-else-if="item.type === 'btn'" :name="item.slot" :item="event" :index="index"></slot>
            </div>
          </li>
        </vue3-seamless-scroll>
      </div>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, ref, onMounted, watch } from 'vue';
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
/*
**@props
  label 名称
  type 展示类型
  slot 插槽
  sort 是否展示序号
  key 数据键名
  style 样式
  roll 是否滚动
  attrs 滚动组件配置项
  list 数据源
**@event:
    通过内嵌插槽抛出事件
*/
interface PropsHead {
  label?: string,
  type: string,
  slot?: string
  style?: Object
}
interface PropsBody extends PropsHead {
  key: string,

}
interface TableHead {
  list: Array<PropsHead>
}
interface TableBody {
  sort: boolean,
  roll: boolean,
  attrs: object,
  list: Array<PropsBody>
}
const prop = defineProps<{
  tableHead: TableHead;
  tableBody: TableBody;
  tableData: any[],
}>();
// 根据序号设置背景颜色的函数，返回一个样式对象
const getRowStyle = (index: number): { backgroundColor?: string } => {
  if (index === 0) {
    return { backgroundColor: '#FF4949' };  // 特别重大
  } else if (index === 1) {
    return { backgroundColor: '#FF9200' };  // 重大
  } else if (index === 2) {
    return { backgroundColor: '#FFC823' };  // 较大
  }
  return {};  // 默认为空，不改变颜色
};
const formatTimestampToTime = (timestamp: string) => {
  const date = new Date(timestamp);
  const hours = date.getHours().toString().padStart(2, '0');
  const minutes = date.getMinutes().toString().padStart(2, '0');
  return `${hours}:${minutes}`;
}

</script>

<style scoped lang="less">
.data-list-header-item,
.data-list-item-type {
  flex: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.data-list-body {
  overflow: hidden !important;
}
</style>