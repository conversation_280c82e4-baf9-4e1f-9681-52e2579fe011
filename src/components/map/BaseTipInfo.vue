<template>
  <div style="display: none">
    <div
      ref="contentRef"
      class="tip-background-body tip-border"
      @wheel="handleMouse"
      @mouseover="handleMouse"
      @mouseout="handleMouse"
      @mousemove="handleMouse"
      :style="getStyle()"
    >
      <div class="tip-background-content">
        <el-scrollbar>
          <div class="tip-btns" v-show="showButton">
            <div class="tip-btn">
              <el-button
                v-for="item in buttonTexts"
                :key="item"
                class="button-glass-query"
                type="primary"
                @click.capture="$emit('clickBtn', item)"
                >{{ item }}
              </el-button>
            </div>
          </div>
          <div style="margin: 0rem 0.5rem 0rem 1rem">
            <slot></slot>
          </div>
        </el-scrollbar>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, nextTick } from 'vue'

const emit = defineEmits(['close', 'clickBtn'])

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  width: {
    type: String,
    default: '391px'
  },
  height: {
    type: String,
    default: '190px'
  },
  showButton: {
    type: Boolean,
    default: false
  },
  buttonTexts: {
    type: Array<String>,
    default: []
  }
})

const cameraFlag = ref(false)
const contentRef = ref()

//防止滚动事件穿透到地图
const handleMouse = (event: MouseEvent) => {
  event.stopPropagation()
}

const close = () => {
  emit('close')
}

const getStyle = () => {
  let width = props.width
  if (width.includes('rem')) {
    width = Number(props.width.replace('rem', '')) * 13 + 'px'
  }
  let height = props.height
  if (height.includes('rem')) {
    height = Number(props.height.replace('rem', '')) * 13 + 'px'
  }
  console.log(`width: ${width};height: ${height};`)
  return `width: ${width};height: ${height};`
}

const closeCameraDialog = () => {
  cameraFlag.value = false
}

defineExpose({
  contentRef,
  closeCameraDialog
})
</script>

<style scoped lang="scss">
@import url(@/assets/css/tip.css);
.tip-border {
  position: relative;
  background-color: #fff;
  border-radius: 4px;
  padding-top: 0;
  box-shadow: 0 5px 10px 0 rgba(0,0,0,0.1);
  .tip-btns {
    padding: 0 10px;
    z-index: 999;
    display: flex;
    justify-content: flex-start;
    align-items: center;
    flex-direction: row;
    flex-wrap: wrap;
    margin-bottom: 10px;

    .tip-btn {
      height: 20px;

      .el-button {
        height: 25px;
        border-radius: 15px !important;
        margin-bottom: 5px;
        margin-left: 0 !important;
        margin-right: 5px;
      }
    }
  }

  &:before {
    position: absolute;
    bottom: -27px;
    left: calc(50% - 15px);
    content: '';
    border: 15px solid transparent;
    transform: rotate(90deg);
    border-left-color: #ffffff;
  }
}
</style>
