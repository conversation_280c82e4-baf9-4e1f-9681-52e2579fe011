<template>
  <div class="inner">
    <div class="left">
      速度：
      <el-select v-model="trackSpeed" @Change="setSpeed"
      placeholder="Select" 
      size="large" style="width: 100px">
        <el-option label="0.25倍速" :value="0.25"></el-option>
        <el-option label="0.5倍速" :value="0.5"></el-option>
        <el-option label="1倍速" :value="1"></el-option>
        <el-option label="2倍速" :value="2"></el-option>
        <el-option label="5倍速" :value="5"></el-option>
        <el-option label="10倍速" :value="10"></el-option>
      </el-select>
    </div>
    <div class="right">
      <div class="control-btn">
        <div v-show="play_status === 'play'" class="pause"><img :src="pause" alt="" @click="control_play_handel('pause')" /></div>
        <div v-show="play_status !== 'play'" class="play"><img :src="play" alt="" @click="control_play_handel('play')" /></div>
        <div class="stop"><img :src="stop" alt="" @click="control_play_handel('stop')" /></div>
      </div>
      <div class="slider">
        <el-slider v-model="aniProcess" :marks="marks" :min="0" :max="100" 
        @change="changeProcess" @input="processChangeFlag = false"/>
        <span>{{ aniProcess }}%</span>
      </div>

    </div>
  </div>
  <!-- <el-drawer
    title="轨迹播放"
    direction="rtl"
    size="20%"
    :modal="false"
    :before-close="handleClose"
    :wrapperClosable="false"
    append-to-body
    :z-index="9999"
    :show-close="false"
    style="background-color: #071C4E;"
  >
  <template #header="{ close, titleId, titleClass }">
      <div class="text-1375rem">轨迹播放</div>
      <div class="dialog-close" @click="close"></div>
    </template>
    <el-row justify="center" style="margin-top: 10px;">
      <el-button type="success" @Click="startAni">开始</el-button>
    </el-row>
    <el-row justify="center" style="margin-top: 10px;">
      <el-button type="warning" @Click="pauseAni">暂停</el-button>
    </el-row>
    <el-row justify="center" style="margin-top: 10px;">
      <el-button type="primary" @Click="resumeAni">继续</el-button>
    </el-row>
    <el-row justify="center" style="margin-top: 10px;">
      <el-button type="danger" @Click="stopAni">重置</el-button>
    </el-row>
    <el-row justify="center" style="margin-top: 10px;">
      <el-button type="info" @Click="destroyAni">清除</el-button>
    </el-row>
    <el-row justify="center" style="margin-top: 10px;">
      <el-select class="buttons-switch-group select-hub" :teleported="false" style="width: 7rem"
        v-model="trackSpeed" size="small" @Change="setSpeed">
        <el-option label="0.25倍速" :value="0.25"></el-option>
        <el-option label="0.5倍速" :value="0.5"></el-option>
        <el-option label="1倍速" :value="1"></el-option>
        <el-option label="2倍速" :value="2"></el-option>
        <el-option label="5倍速" :value="5"></el-option>
        <el-option label="10倍速" :value="10"></el-option>
      </el-select>
    </el-row>
    <el-row justify="center" style="margin-top: 10px;">
      <el-slider v-model="aniProcess" :marks="marks" :min="0" :max="100" @change="changeProcess"/>
    </el-row>
  </el-drawer> -->
</template>

<script setup lang="ts">
import * as Track from '@bmapgl-plugin/track';
import { ref, watch, onMounted, onBeforeUnmount, reactive, nextTick } from 'vue'
import { ElMessageBox, ElDrawer } from 'element-plus'
import downImg from '@/assets/images/maps/common/down.png';
import carImg from '@/assets/images/maps/common/car.png';
import play from '@/assets/images/transportation/play.png'
import stop from '@/assets/images/transportation/stop.png'
import pause from '@/assets/images/transportation/pause.png'
import { throttle } from 'lodash';
import { useBaseMap } from '@/hooks/useBaseMap';
import { MAP_TYPE } from '@/constant/map'

const props = withDefaults(defineProps<{
  mapType: MAP_TYPE
}>(), {
  mapType: MAP_TYPE.BASE
})

const { map, destroyMap, resetMap } = useBaseMap(props.mapType)
const BMapGL = (window as any).BMapGL
const aniProcess = ref(0)
const marks = reactive<any>({
  0: '0%',
  25: '25%',
  50: {
    style: {
      color: '#1989FA',
    },
    label: '50%',
  },
  75: '75%',
  100: '100%',
})

onMounted(() => {
    
});

interface Options { 
  pathList: Array<number[]>, //lng：经度, lat：纬度 ,rotation?：旋转角度, timeStamp?：时间戳
  duration?: number, 
  zoom?: number, 
  speed?: number 
}

//轨迹配置
let options: Options
//轨迹视图
let trackView: Track.View
//轨迹路线
let trackAni: Track.LocalTrack
//轨迹移动物
let movePoint: Track.GroundPoint
//地图倾斜
let mapTilt: any = 20
//地图缩放
let mapZoom: any = 7.7
//地图旋转
let mapHeading: any = 0
//进度修改控制
let processChangeFlag = false

const trackSpeed = ref(1)


const initData = (
      //点位数组[[lng, lat, timeStamp?, rotation?]...]、轨迹时间(秒)、显示层级
      { pathList, duration = undefined, zoom = 15, speed = 1 } : Options
    ) => {
  options = {pathList, duration, zoom, speed}
  trackSpeed.value = speed
  if(map.value && pathList.length > 0){
    let pointList = [];
    let trackData = [];
    for (let i = 0; i < pathList.length; i++) {
      const pathItem = pathList[i]
      const point = new BMapGL.Point(pathItem[0], pathItem[1]);
      const trackPointOption = {timeStamp: pathItem[2], rotation: pathItem[3]};
      pointList.push(point);
      trackData.push(new Track.TrackPoint(point, trackPointOption));
    }
    //如果没有设置轨迹时长，且数据带有时间戳，则自动计算时长
    if(!duration){
      const startPath = pathList[0]
      const endPath = pathList[pathList.length-1]
      const startTimeStamp = startPath[2]
      const endTimeStamp = endPath[2]
      if(startTimeStamp && endTimeStamp){
        duration = Math.ceil((endTimeStamp - startTimeStamp) / 1000)
      }
    }
    // console.dir(pathList)
    // console.dir(trackData)
    destroyAni()
    if(!trackView){
      trackView = new Track.View(map.value,{
          lineLayerOptions: {
              style: {
                  strokeWeight: 8,
                  strokeLineJoin: 'round',
                  strokeLineCap: 'round'
              }
          },
          cruiseOptions:{}
      });
    }
    let durationFmt = duration ? Math.ceil(duration / speed) : undefined;
    trackAni = new Track.LocalTrack({
        trackPath: trackData,
        duration: durationFmt,
        style: {
            sequence: true,
            marginLength: 32,
            arrowColor: '#fff',
            strokeTextureUrl: downImg,
            strokeTextureWidth: 64,
            strokeTextureHeight: 32,
            traceColor: [27, 142, 236]
        } as any,
        linearTexture: [[0, '#f45e0c'], [0.5, '#f6cd0e'], [1, '#2ad61d']],
        //gradientColor: [0.1, 0.5, 0.9],
        speedMode: 1, 
    });
    addAniEvent()
    trackView.addTrackLine(trackAni);
    trackView.focusTrack(trackAni);
    addMovePoint(trackData[0].getPoint(), zoom)
    // map.value.addEventListener('tilesloaded', ()=>{
    //   startAni()
    // });
  }
}

const addAniEvent = () => {
  //轨迹状态监听
  trackAni.on(Track.LineCodes.STATUS, (status: Track.StatusCodes) => {
        switch (status) {
            case Track.StatusCodes.PLAY:
            case Track.StatusCodes.INIT:
            case Track.StatusCodes.PAUSE:
            case Track.StatusCodes.STOP:
            case Track.StatusCodes.FINISH:{
              let box = trackAni.getBBox();
              if(box){
                  let bounds = [new BMapGL.Point(box[0], box[1]), new BMapGL.Point(box[2], box[3])];
                  map.value.setViewport(bounds);
              }
              break;
            }
            default:
                break;
        }
    });
    //轨迹进度监听
    const setProcess = throttle((process: number) => {
        if(processChangeFlag){
          aniProcess.value = parseFloat((process * 100).toFixed(2))
        }
    }, 500);  //节流500毫秒一次
    trackAni.on(Track.LineCodes.PROCESS, setProcess);
}

const addMovePoint = (startPoint: any, zoom: number) => {
  map.value.centerAndZoom(startPoint, zoom);
  movePoint = new Track.GroundPoint({ 
        point: startPoint,
        //rotation: 0,
        style:{
            url: carImg,
            level: 18,
            scale: 1,
            size: new BMapGL.Size(16, 32),
            anchor: new BMapGL.Size(0.5,0.5)
        }
    });
    // movePoint.addEventListener(Track.MapCodes.CLICK,(e: any)=>{
    //     console.log('Track.GroundPoint.click', e);
    // })
    // movePoint.addEventListener(Track.MapCodes.MOUSE_OVER,(e: any)=>{
    //     console.log('Track.GroundPoint.MOUSE_OVER', e);
    // })
    // movePoint.addEventListener(Track.MapCodes.MOUSE_OUT,(e: any)=>{
    //     console.log('Track.GroundPoint.MOUSE_OUT', e);
    // })
    trackAni.setMovePoint(movePoint);
}
//开始
const startAni = () => {
  trackAni.startAnimation();
  processChangeFlag = true;
  play_status.value = 'play'
}
//重置
const stopAni = () => {
  trackAni?.stopAnimation();
  processChangeFlag = false;
  play_status.value = 'stop'
  aniProcess.value = 0
}
//暂停
const pauseAni = () => {
  processChangeFlag = false;
  trackAni.pauseAnimation();
  play_status.value = 'pause'
}
//继续
const resumeAni = () => {
  trackAni.resumeAnimation();
  processChangeFlag = true;
}
//设置速度
const setSpeed = (speed: number) => {
  let process = getAniProcess()
  stopAni()
  options.speed = speed
  initData(options)
  startAni()
  setAniProcess(process == 1 ? 0 : process)
}
//获取进度
const getAniProcess = () => {
  if(trackAni){
    return trackAni.process
  }else{
    return 0
  }
}
//设置进度
const setAniProcess = (process: number) => {
  trackAni.setProcess(process)
}
//滚动条修改进度
const changeProcess = (process: number) => {
  pauseAni()
  setAniProcess(process/100)
}

//清除轨迹
const destroyAni = () => {
  aniProcess.value = 0
  movePoint?.removeFromMap(map.value);
  movePoint?.destroy()
  if(trackAni){
    trackView?.removeTrackLine(trackAni)
  }
  trackAni?.destroy();
  map.value?.setHeading(mapHeading);
  map.value?.setTilt(mapTilt);
  map.value?.setZoom(mapZoom);
  movePoint = null as any;
  trackAni = null as any;
  trackView = null as any;
}
//关闭抽屉
const close = () => {
  destroyAni();
}
//抽屉关闭事件
const handleClose = (done: () => void) => {
  destroyAni()
  done()
}

/*
 * 在这里对视频进行 暂停 播放 停止等操作
 * */
const play_status = ref('stop')
const control_play_handel = (status: string) => {
  if(status === 'stop'){
    stopAni()
  }else if(status === 'play'){
    startAni()
  }else if(status === 'pause'){
    pauseAni()
  }
}

onBeforeUnmount(() => {
  destroyAni()
});

defineExpose({ initData, close })
</script>

<style scoped>

.inner {
			display: flex;
			width: 100%;
			height: 100%;
			align-items: center;

      .left {
        display: flex;
        align-items: center;
        justify-content: center;
      }

			.right {
        display: flex;
        align-items: center;
				margin-left: 30px;

				.slider {
					display: flex;
          width: 450px;
          margin-left: 20px;
					span {
						margin-left: 20px;
            width: 80px;
					}
				}

				.control-btn {
					display: flex;
					margin-top: 10px;
					justify-content: center;

					.play,
					.pause,
					.stop {
						cursor: pointer;
					}

					.stop {
						margin-left: 20px;
					}
				}
			}
		}
</style>
