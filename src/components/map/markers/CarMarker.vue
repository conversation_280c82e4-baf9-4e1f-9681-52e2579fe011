<template>
  <BaseTipInfo ref="baseTipInfoRef" :title="''" @close="closeInfoWindow">
    <div class="tip-item">
      <div class="tip-padding">
        <el-row>
          <el-col :span="24" class="tip-common tip-content">
            <div class="driver">
              <em class="circle"></em>{{ '琼D15171D' }}/{{ '赵晓峰' }}
              <span class="time">{{ '16:30:58' }}</span>
            </div>
            <span class="close">×</span>
          </el-col>
          <el-col :span="24" class="tip-common car-info">
            <div class="item-title">
              <span class="tip">车速：</span>
              <span class="info">{{ '49km/h（中）' }}</span>
            </div>
            <div class="item-title">
              <span class="tip">里程：</span>
              <span class="info">{{ '180km' }}</span>
            </div>
          </el-col>
          <el-col :span="24" class="tip-common address">三亚市吉阳区下洋田榆亚路13号</el-col>
          <el-col :span="24" class="tip-common functional-group no-border">
            <div class="functional-item" @click="click_fn_handel('trace')">
              <SVGIcon name="trace" color="#165DFF" />
              跟踪
            </div>
            <div class="functional-item" @click="click_fn_handel('track')">
              <SVGIcon name="track" color="#165DFF" />
              轨迹
            </div>
            <div class="functional-item " @click="click_fn_handel('video')">
              <SVGIcon name="video" color="#165DFF" />
              视频
            </div>
          </el-col>
        </el-row>
      </div>
    </div>
  </BaseTipInfo>
</template>

<script setup lang="ts">
import { onMounted, ref, reactive, watch } from 'vue'
import { useMapMarker } from '@/hooks/useMapMarker'

const emit = defineEmits(['camera-marker-open'])
const useHook = useMapMarker()
import _icon from '@/assets/images/aviation-railway/ky.png'
import BaseTipInfo from '../BaseTipInfo.vue'
import SVGIcon from '@/pages/monitor/common/SVGIcon.vue'

const baseTipInfoRef = ref<InstanceType<typeof BaseTipInfo>>()
const item: any = reactive({})

const props = defineProps({
  map: {
    type: Object
  }
})

const click_fn_handel = (type: string) => {
  switch (type) {
    case 'trace':
      //   跟踪  to do
      break
    case 'track':
      //    轨迹 to do
      break
    case 'video':
      //   视频  to do
      break
    default:
      break
  }
}

const queryParam = reactive({
  s: 0,
  n: 100,
  filter: "type='机场'",
  type: '机场'
})

watch(
  () => props.map,
  (newVal, oldVal) => {
    useHook.setMap(newVal)
  },
  { immediate: true, deep: false }
)

onMounted(() => {
  useHook.setAttribute(setData, baseTipInfoRef.value?.contentRef, _icon)
})

const addMarker = async (dataList: Array<any>) => {
  if (removeMarker() == 0) {
    useHook.addMarker(dataList, _icon)
  }
}

const setData = (_data: any) => {
  globalCopyObject(item, _data)
}

const removeMarker = () => {
  return useHook.removeMarker()
}

const closeInfoWindow = () => {
  useHook.closeInfoWindow()
}

defineExpose({
  addMarker,
  removeMarker,
  closeInfoWindow
})
</script>

<style scoped>
.tip-item {
  width: 100%;
  height: 100%;
}

.tip-padding {
  width: 100%;
  padding: 10px 0 0;
}

.tip-common {
  border-bottom: 1px solid #e5e6ec;
  height: 44px;
  line-height: 44px;
}

.functional-group {
  display: flex;
  justify-content: space-around;

  .functional-item {
    display: flex;
    align-items: center;
    cursor: pointer;
  }

}
.no-border{
  border: none;
}
.close {
  font-size: var(--font-size-level1);
  color: #000;
  cursor: pointer;
}

.driver {
  font-size: var(--font-size-level3);
  color: var(--color-text-emphasize1);
  font-weight: 600;

  .circle {
    display: inline-block;
    width: 8px;
    height: 8px;
    background-color: #165dff;
    border-radius: 50%;
    margin-right: 10px;
  }

  .time {
    font-weight: 400;
    font-size: var(--font-size-level4);
    color: var(--color-text-minor);
    margin-left: 20px;
  }
}

.car-info {
  display: flex;

  .item-title {
    width: 50%;
  }
}

.address {
  font-size: var(--font-size-level4);
  color: var(--color-text-emphasize1);
}

.item-title {
  .tip {
    color: var(--color-text-minor);
  }

  .info {
    font-weight: 600;
    font-size: var(--font-size-level4);
    color: var(--color-text-emphasize1);
  }
}

.tip-content {
  display: flex;
  justify-content: space-between;
}
</style>
