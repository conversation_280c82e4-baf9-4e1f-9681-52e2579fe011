<template>
  <div>
    <el-row :gutter="0">
      <el-col :span="5" style="background: white; border-radius: 4px; padding: 8px 8px 10px 8px">
        <el-button @click="clickRoot" size="small" style="width: 100%; margin-bottom: 8px">
          顶级
        </el-button>
        <el-tree style="max-width: 600px" empty-text="" :props="treeProps" :load="loadNode" node-key="id"
          :default-expanded-keys="[]" lazy :highlight-current="true" :expand-on-click-node="false"
          @node-click="handleNodeClick" ref="dictTree" />
      </el-col>
      <el-col :span="19" style="background: #f2f6fc">
        <div class="crud-main-content" style="margin-left: 8px">
          <DictionaryTable :classify="props.classify" @update="reloadItem" ref="dictionaryTable"></DictionaryTable>
        </div>
      </el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import type { Ref } from 'vue'
import type Node from 'element-plus/es/components/tree/src/model/node'
import { reactive, ref, toRefs, onMounted, nextTick } from 'vue'
import dictionaryApi from '@/service/sys/dictionary'
import DictionaryTable from '@/components/DictionaryTable.vue'
import type { ElTree } from 'element-plus'

// 定义组件props类型
const props = defineProps<{
  classify: string 
}>()

// 定义懒加载树形数据的类型
interface Tree {
  id: string
  label: string
  value: string
  parentCode?: string
  leaf?: boolean
}

// 定义响应式数据类型
const dataForm = reactive<{
  rootNode: Node | null
}>({
  rootNode: null
})

const data = reactive<{
  selectedNode: Node | null
  level: number
}>({
  selectedNode: null,
  level: 1
})

const { selectedNode, level } = toRefs(data)

// 创建Ref变量并定义类型
const dictTree = ref<InstanceType<typeof ElTree> | null>(null)
const dictionaryTable = ref<InstanceType<typeof DictionaryTable> | null>(null)

// onMounted生命周期钩子
onMounted(() => {
  _init()
})

function _init() {
  // 初始化逻辑
}

// 设置树组件的props类型
const treeProps = {
  label: 'label',
  children: 'children',
  isLeaf: 'leaf'
}

// 定义懒加载树形结构的加载方法
const loadNode = async (
  node: Node,
  resolve: (data: Tree[]) => void,
  reject: () => void
): Promise<void> => {
  if (node.level === 0) {
    dataForm.rootNode = node
    try {
      const res = await dictionaryApi.queryTreeList({ classify: props.classify, parentCode: null }) as any
      resolve(res.data.data) // 这里假设 API 返回数据结构符合该类型
    } catch (err) {
      reject()
    }
  } else {
    try {
      const res = await dictionaryApi.queryTreeList({ classify: props.classify, parentCode: node.data.value }) as any
      resolve(res.data.data)
    } catch (err) {
      reject()
    }
  }
}

// 顶级按钮点击事件
const clickRoot = (): void => {
  dictionaryTable.value?.setDicCode('', '', '',)
}

// 处理节点点击事件
const handleNodeClick = (nodeData: Tree, node: Node): void => {
  data.level = node.level
  data.selectedNode = node
  nextTick(() => {
    dictionaryTable.value?.setDicCode(nodeData.id, nodeData.label, nodeData.value)
  })
}

// 刷新树节点
function refreshNode(key: string | null): void {
  let node: Node | null = null
  if (key === null) {
    node = dataForm.rootNode
  } else {
    node = dictTree.value?.getNode(key) || null
  }

  if (node) {
    node.loaded = false // 设置节点未加载
    node.expand() // 重新展开节点，间接触发懒加载
  }
}

// 刷新根节点
const reloadRoot = (): void => {
  refreshNode('root')
}

// 刷新指定节点
const reloadItem = (nodeKey: string): void => {
  refreshNode(nodeKey)
}
</script>

<style scoped></style>
