<template>
  <div class="data-list-container">
    <ul class="data-list">
      <li class="data-list-header">
        <div
          class="data-list-header-item"
          :style="{ ...item.style }"
          v-for="(item, index) in tableBody.list"
          :key="index"
        >
          <el-tooltip
            :disabled="!showTooltip"
            class="item"
            effect="dark"
            :content="item.label"
            placement="top-start"
          >
            <span>{{ item.label }}</span>
          </el-tooltip>
        </div>
      </li>
      <!-- 根据prop.roll是否无缝滚动，控制是否有手动滚动条，
        如果prop.roll设为false，则需要设置copyNum: 0，否则有重复数据滚动 -->
      <el-scrollbar :native="prop.roll" :wrap-style="prop.roll ? 'overflow: hidden;' : ''">
        <div class="data-list-body">
          <vue3-seamless-scroll
            ref="scrollRef"
            v-model="prop.roll"
            class="scroll"
            :list="list"
            v-bind="$attrs"
          >
            <li v-for="(event, index) in tableData" :key="event.id" class="data-list-item">
              <div
                class="data-list-item-type"
                :style="{ ...item.style }"
                v-for="(item, idx) in tableBody.list"
                :key="idx"
              >
                <div class="data-list-item-index" v-if="item.type === 'sort'">
                  <span :style="getRowStyle(index)" style="padding: 0.2rem 0.5125rem">{{
                    index + 1
                  }}</span>
                </div>
                <span v-else-if="item.type === 'text'">
                  <template v-if="item.showTooltip && item.showTooltip === true">
                    <el-tooltip
                      class="item"
                      effect="dark"
                      :content="event[item.key]"
                      placement="top-start"
                    >
                      <span>{{ event[item.key] }}</span>
                    </el-tooltip>
                  </template>
                  <template v-else>
                    {{ event[item.key] }}
                  </template>
                </span>
                <span v-else-if="item.type === 'time'">{{
                  formatTimestampToTime(event[item.key])
                }}</span>
                <slot
                  v-else-if="item.type === 'slot'"
                  :name="item.slot"
                  :item="event"
                  :index="index"
                ></slot>
              </div>
            </li>
          </vue3-seamless-scroll>
        </div>
      </el-scrollbar>
    </ul>
  </div>
</template>

<script lang="ts" setup>
import { defineProps, ref, onMounted, watch, withDefaults } from 'vue'
import { Vue3SeamlessScroll } from 'vue3-seamless-scroll'
/*
**@props
  label 名称
  type 展示类型
  slot 插槽
  sort 是否展示序号
  key 数据键名
  style 样式
  roll 是否滚动
  attrs 滚动组件配置项
  list 数据源
  showTooltip 是否显示表头提示
**@event:
    通过内嵌插槽抛出事件
*/

interface TableBody {
  list: Array<any>
}
const prop = withDefaults(
  defineProps<{
    tableBody: TableBody
    tableData: any[]
    roll: boolean
    list: any[]
    showTooltip: boolean
  }>(),
  {
    roll: false,
    showTooltip: false
  }
)

// 根据序号设置背景颜色的函数，返回一个样式对象
const getRowStyle = (index: number): { backgroundColor?: string } => {
  if (index === 0) {
    return { backgroundColor: '#FF4949' } // 特别重大
  } else if (index === 1) {
    return { backgroundColor: '#FF9200' } // 重大
  } else if (index === 2) {
    return { backgroundColor: '#FFC823' } // 较大
  }
  return {} // 默认为空，不改变颜色
}
const formatTimestampToTime = (timestamp: string) => {
  const date = new Date(timestamp)
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}
</script>

<style scoped lang="less">
.data-list {
  height: 100%;
}
.data-list-header {
  padding: 0.625rem 0rem;
}
.data-list-header-item,
.data-list-item-type {
  flex: none;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}
.data-list-item {
  padding: 0.625rem 0rem;
  font-size: 1rem;
}
.data-list-body {
  // height: 100%;
  position: static;
  overflow: hidden !important;
}
</style>
