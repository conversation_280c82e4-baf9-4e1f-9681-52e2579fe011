<template>
  <div>
    <h2>年龄: {{ age }}</h2>
    <button @click="changeAge">修改年龄</button>

    <h2>一辆{{ car.price }}</h2>
    <button @click="changePrice">修改年龄</button>
  </div>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'

let age = ref(40)

function changeAge() {
  age.value += 1
}

let car = reactive({ price: 100 })

function changePrice() {
  car.price += 100
}
</script>

<style></style>
