<template>
  <!-- <ElDialog width="fit-content" v-model="visible" :align-center="true" :close-on-click-modal="false"
    :class="['rounded-lg', props.class]" @opened="handleOpen" @close="handleClose" append-to-body> -->

    <el-dialog
      v-model="visible"
      :align-center="true"
      :close-on-click-modal="false"
      width="50rem"
      style="height: 33rem; padding: 0; background-color: #020e37"
      :show-close="false"
      append-to-body
      @opened="handleOpen"
    >

    <template #header="{ close, titleId, titleClass }">
      <div class="advanced-dialog-title">
        <el-row :gutter="10">
          <el-col :span="23">
            <div class="text-1rem title-text">{{ props.camera?.channelCode }}</div>
          </el-col>
          <el-col :span="1" style="justify-items: center">
            <div class="advanced-dialog-close" @click="handleClose()"></div>
          </el-col>
        </el-row>
      </div>
    </template>

    <div v-loading="isLoading" class="rounded-md overflow-hidden">
      <video autoplay id="camera-video" class="rounded-md overflow-hidden" />
    </div>
  </el-dialog>
</template>

<script lang="ts" setup>
import venus, { isError } from '@/plugins/webrtc/venus'
import type { McuCamera } from '@/store/mcu/camera'
import { useSessionStore } from '@/store/session'
import { ElDialog, ElMessage } from 'element-plus'
import { ref } from 'vue'

// Hooks
const props = defineProps<{
  camera?: McuCamera
  class?: string
}>()
const visible = defineModel<boolean>('visible')
const isLoading = ref<boolean>(false)
const sessionStore = useSessionStore()

// Handlers
const handleOpen = () => {
  callCamera()
}
const handleClose = () => {
  hangUpCamera()
}
const callCamera = () => {
  console.log('--------------------callCamera-------------------------')
  isLoading.value = true
  const venusServer = sessionStore.sessionUser.sysSettingMap.venus_gateway
  const sipServer = sessionStore.sessionUser.sysSettingMap.sip_server ? sessionStore.sessionUser.sysSettingMap.sip_server : "127.0.0.1"
  venus.call_camera(
    venusServer,
    sipServer,
    props.camera?.channelCode ?? '',
    'camera-video',
    (msg) => {
      if (isError(msg)) {
        isLoading.value = false
        ElMessage.error(msg)
        return
      }

      if (msg.result.event === 'accepted') {
        isLoading.value = false
      }
    }
  )
}

const hangUpCamera = () => {
  venus.hangup_camera(props.camera?.channelCode ?? '')
  // 用于页面卡住时清空资源
  isLoading.value = false
  visible.value = false
}
</script>

<style lang="scss">
#camera-video {
  // 防止行内元素内置的空行
  display: block;
  width: 100%;
  height: 32rem;
  // background: #020e37;
}

.advanced-dialog-title {
    background-image: url('@/assets/images/common/dialog-title.png');
    background-repeat: no-repeat;
    height: 3rem;
    background-size: contain;
    margin-top: 2px;

    .title-text {
        padding-top: 8px;
        padding-left: 1rem
    }
}

.advanced-dialog-close {
    background-image: url('@/assets/images/common/tip-remove.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: left;
    position: relative;
    top: 0.7rem;
    right: 6px;
    z-index: 11;
    width: 13px;
    height: 13px;
    cursor: pointer;
}
</style>
