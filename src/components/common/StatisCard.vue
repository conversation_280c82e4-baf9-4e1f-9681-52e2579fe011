<template>
  <div class="num-tags-item">
    <div class="content1">
      <div class="left">
        <span class="count">{{ count }}</span>
        <span class="unit">{{ unit }}</span>
      </div>
      <div class="right">
        <!-- <img :src="mainImg" alt="" /> -->
        <SVGIcon :name="img" :color="'#165DFF'"/>
      </div>
    </div>
    <div class="title">{{ title }}</div>
  </div>
</template>

<script setup lang="ts">

const props = defineProps({
  img: {
    type: String
  },
  title: {
    type: String
  },
  count: {
    type: [String, Number],
    default: () => '-'
  },
  unit: {
    type: String
  },
})
</script>

<style scoped lang="scss">
.num-tags-item {
  margin: 5px 0;
  width: 140px;
  height: 80px;
  border-radius: 4px;
  background: rgba(24,98,255,.05);
  border: 1px solid transparent;
  color: #1D2129;
  //box-shadow: 0 4px 10px 0 rgba(22, 93, 255, 0.1);

  .content1 {
    display: flex;
    padding: 15px 12px 2px 12px;

    .right {
      text-align: right;
      img{
        width: 27px;
        height: 27px;
      }
    }

    .left {
      flex: 1;
      .count {
        font-family: var(--font-family-number-normal);
        font-weight: 600;
        font-size: var(--font-size-level1);
        margin-right: 4px;
      }

      .unit {
        font-weight: 400;
        font-size: var(--font-size-level3);
        color: #86909C;
      }
    }
  }

  .title {
    font-size: var(--font-size-level4);
    font-weight: 500;
    margin-left: 12px;
  }
}
</style>
