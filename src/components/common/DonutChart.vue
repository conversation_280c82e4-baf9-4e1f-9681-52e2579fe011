<template>
    <div ref="donutChart" class="echart-style">
    </div>
</template>

<script setup lang="ts">
import * as echarts from 'echarts';
import {onMounted, onUnmounted, ref} from 'vue';
import type {EChartsType} from "echarts";
import pageViewApi from "@/service/sys/pageView.ts";

const props = defineProps({
  pageLayoutId: {
    type: String,
    default: null
  },
  params: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {
      //title:'巡逻车辆'
    }
  },
  queryParams: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {}
  },
  pageParams: {
    type: Object,
    // eslint-disable-next-line vue/require-valid-default-prop
    default: {}
  },
})

let donutChart = ref()
let myChart: EChartsType
let resizeObserver: ResizeObserver
onMounted(() => {
  try {
    //监听div容器的宽度变化,使容器自适应大小
    resizeObserver = new ResizeObserver(entries => {
      myChart.resize();
    })
    resizeObserver.observe(donutChart.value)
    loaderDonutChartData()
    chart()
  } catch (err) {
    console.error(err)
  }
})

onUnmounted(() => {
  if(resizeObserver){
    resizeObserver.disconnect()
  }
  myChart?.dispose()
})

const loaderDonutChartData = async () => {
  if(props.pageLayoutId){
    let pageComponentSource = {'pageLayoutId': props.pageLayoutId, 'bindParams': props}
    const res:any = await pageViewApi.loaderData(pageComponentSource)
    // console.log('loaderData------------------------------------------------')
    // console.dir(res)
    emit('loaderData', res.data.data)
  }
}

function chart() {
  myChart = echarts.init(donutChart.value);
  setOption([
    {
      value: 335,
      name: '水运建设'
    },
    {
      value: 234,
      name: '民航机场'
    },
    {
      value: 1548,
      name: '旅游公路'
    },
    {
      value: 1548,
      name: '农村公路'
    },
    {
      value: 1548,
      name: '国省干线'
    },
    {
      value: 1548,
      name: '普速铁路'
    },
    {
      value: 1548,
      name: '高速公路'
    },
    {
      value: 1548,
      name: '综合交通枢纽'
    }
  ])
}

function fontSize(res:any){
  const clientWidth = window.innerWidth||document.documentElement.clientWidth||document.body.clientWidth;
  if (!clientWidth) return;
  let fontSize = clientWidth / 1920;
  return res*fontSize;
}

const setOption = (seriesData: ({ name: string; value: number })[]) => {
  myChart.setOption({
    title: {
          text: '134',
          subtext: '总数',
          left: 'center',
          top: '32%',
          textStyle: {
            fontSize: '1.5625rem',
            color: '#2BCAFF',
            align: 'center'
          },
          subtextStyle: {
            fontSize: '0.875rem',
            color: '#fff',
            align: 'center'
          }
        },
    tooltip: {
      trigger: 'item',
      textStyle:{
        fontSize: fontSize(12),
      }
    },
    // legend: {
    //     tooltip: {
    //         show: true,
    //     },
    //     right: 1,
    //     top: 20,
    //     bottom: 20,
    //     orient: 'vertical',
    //     textStyle: {
    //         color: '#fff' // 设置图例字体颜色为红色
    //     }
    // },
    // 中心标题
    // graphic: [
    //     {
    //         type: 'text',
    //         style: {
    //             text: '134',
    //             fill: '#fff',
    //             width: 30,
    //             height: 30,
    //             fontSize: 24
    //         },
    //         left: '36%',
    //         top: '42%'
    //     },
    //     {
    //         type: 'text',
    //         left: '36%',
    //         top: '50%',
    //         style: {
    //             text: '总数',
    //             fill: '#fff',
    //             width: 30,
    //             height: 30,
    //             fontSize: 24
    //         }
    //     }
    // ],
    series: [
      {
        type: 'pie',
        label: {
          normal: {
            show: true,
            position: 'outside', // 在饼图内部显示标签
            formatter: '{b}\n{d}%',
            textStyle: {
              color: '#fff',
              fontSize: '1rem' // 设置文字大小
            }
          }
        },
        labelLine: {
          normal: {
            length: 2, // 调整标签线的长度
          }
        },
        //数据空格
        padAngle: 2,
        avoidLabelOverlap: false,
        data: seriesData,
        radius: ['90%', '75%'],
        // center:['80%','60%']
      }
    ]
  })
  }
defineExpose({ loaderDonutChartData, setOption })
</script>
<style scoped>
.echart-style {
    width: 100%;
    height: 100%;
}
</style>