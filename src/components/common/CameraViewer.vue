<template>
  <ElDialog
    width="fit-content"
    v-model="visible"
    :align-center="true"
    :close-on-click-modal="false"
    :class="['rounded-lg', props.class]"
    @opened="handleOpen"
    @close="handleClose"
    append-to-body
  >
    <div
      v-loading="isLoading"
      class="rounded-md overflow-hidden"
      @mousemove="mousemoveVideo"
      @mouseleave="videoShow = false"
    >
      <div class="videos" v-if="videoShow" @click="handelSwitch">
        {{ !switchShow ? '标清' : '高清' }}
      </div>
      <video
        ref="videoRef"
        autoplay="true"
        controls
        id="camera-video"
        class="rounded-md overflow-hidden"
      />
    </div>
  </ElDialog>
</template>

<script lang="ts" setup>
import venus, { isError } from '@/plugins/webrtc/venus'
import type { McuCamera } from '@/store/mcu/camera'
import { useSessionStore } from '@/store/session'
import { ElDialog, ElMessage } from 'element-plus'
import cameraApi from '@/service/mcu/camera'
import Hls from 'hls.js'
import { ref } from 'vue'
import settingApi from '@/service/sys/setting.ts'

// Hooks
const props = defineProps<{
  camera?: McuCamera
  class?: string
}>()
const visible = defineModel<boolean>('visible')
const isLoading = ref<boolean>(false)
const sessionStore = useSessionStore()
const videoShow = ref(false)
const switchShow = ref(false)
// Handlers
//const handleOpen = () => {
// callCamera()
//}

/**
 正则匹配协议、域名、端口
 ^https?:\/\/ 匹配 http:// 或 https://。
 \S+? 非贪婪匹配主机名和端口，(:\d+)? 可选端口部分
 */
const fullUrlRegex = /^https?:\/\/\S+?(:\d+)?\//

const handleOpen = async () => {
  console.log('-------------handleOpen----------')
  const defaultHost = window.location.protocol + '//' + window.location.host + '/'
  // console.log('handleOpen------------------------------------------')
  // const testVideoSrc = 'http://cmgw-vpc.lechange.com:8888/LCO/9C07128PAG4B9E2/0/1/20250324T021313/openhz0735a494162c4ba6bb287d7b6a5f4bb7.m3u8?source=open';//大华提供地址165
  // const replaceVideoSrc = testVideoSrc.replace(fullUrlRegex, host)
  // console.log(replaceVideoSrc)
  // todo 改成默认可配置的
  const { data: config } = await settingApi.getByCode('mcu-play-addr')
  const host = config?.data?.parmValue ?? defaultHost
  const videoType = switchShow.value ? 1 : 0
  const res = await cameraApi.getVideoUrl(props.camera?.id, videoType)
  //setLiveHls("https://zb08.sxjkv-cloud.com:28307/live08/00175F8193F3777FC4DE6FA1202AB9FC.m3u8?t=6816e728&k=0dc06df2033723ae86cb131f7a5c3329");
  if (res.data.data != '') {
    const replaceVideoSrc = res.data.data.replace(fullUrlRegex, host)
    console.log(replaceVideoSrc)
    setLiveHls(replaceVideoSrc)
  } else {
    callCamera()
  }

  // for (let i in dataForm) {
  //   dataForm[i] = res.data.data[i]
  // }
}

const handleClose = () => {
  if (hls.value) {
    hls.value.destroy()
  }
  hangUpCamera()
}
const callCamera = () => {
  console.log('--------------------callCamera-------------------------')
  isLoading.value = true
  const venusServer = sessionStore.sessionUser.sysSettingMap.venus_gateway
  const sipServer = sessionStore.sessionUser.sysSettingMap.sip_server
    ? sessionStore.sessionUser.sysSettingMap.sip_server
    : '127.0.0.1'
  venus.call_camera(
    venusServer,
    sipServer,
    props.camera?.channelCode ?? '',
    'camera-video',
    (msg) => {
      if (isError(msg)) {
        isLoading.value = false
        ElMessage.error(msg)
        return
      }

      if (msg.result.event === 'accepted') {
        isLoading.value = false
      }
    }
  )
}

const hangUpCamera = () => {
  venus.hangup_camera(props.camera?.channelCode ?? '')
  // 用于页面卡住时清空资源
  isLoading.value = false
  videoShow.value = false
  switchShow.value = false
}
const hls = ref()
const videoRef = ref()
//const hls = ref<Hls>();
const setLiveHls = (url: string) => {
  // 如果给定的MIME类型和（可选的）编解码器可被当前的用户代理支持，则返回 true
  if (Hls.isSupported()) {
    hls.value = new Hls()

    // 将 hls.js 附加到媒体元素
    hls.value.attachMedia(videoRef.value)
    // 绑定成功后 启动并载入 HLS 流
    hls.value.on(Hls.Events.MEDIA_ATTACHED, () => {
      if (hls.value) {
        console.log('hls1:', url)
        hls.value.loadSource(url)
      }
    })
    // 索引文件解析完成后开始播放视频
    hls.value.on(Hls.Events.MANIFEST_PARSED, () => {
      console.log('hls2:', url)
      videoRef.value.play()
    })
    // hls错误处理
    hls.value.on(Hls.Events.ERROR, (event, data) => {
      if (data.fatal && hls) {
        switch (data.type) {
          case Hls.ErrorTypes.NETWORK_ERROR:
            // 恢复网络错误
            hls.value.startLoad()
            break
          case Hls.ErrorTypes.MEDIA_ERROR:
            // 恢复媒体错误
            hls.value.recoverMediaError()
            break
          default:
            // 销毁实例
            hls.value.destroy()
            break
        }
      }
    })
  } else if (videoRef.value.canPlayType('application/vnd.apple.mpegurl')) {
    videoRef.value.src = url
    console.log('hls3:', videoRef.value.src)
    videoRef.value.addEventListener('loadedmetadata', () => {
      console.log('Native HLS playback supported')
      videoRef.value.play()
    })
  }
}

const mousemoveVideo = (e: MouseEvent) => {
  videoShow.value = true
}
const handelSwitch = () => {
  switchShow.value = !switchShow.value
  handleOpen()
}
</script>

<style lang="scss">
#camera-video {
  // 防止行内元素内置的空行
  display: block;
  width: 40rem;
  height: 28rem;
  background: gray;
}
.rounded-md {
  position: relative;
}

.rounded-md .videos {
  position: absolute;
  z-index: 9999;
  color: white;
  bottom: 39px;
  right: 154px;
  cursor: pointer;
}
</style>
