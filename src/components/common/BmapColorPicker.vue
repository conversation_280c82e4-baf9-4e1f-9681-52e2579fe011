<template>
  <el-scrollbar style="width: 100%;">
    <div v-for="item in pageParams.colors" class="demo-color-block">
      <span class="demonstration">{{ item.name }}</span>
      <el-color-picker v-model="item.color" @change="changeTheme" />
    </div>
  </el-scrollbar>
</template>

<script lang="ts" setup>
import { ref, reactive } from 'vue'
const emit = defineEmits(['changeTheme'])

const props = defineProps({
    textFontSize: {
        type: Number,
        default: 17
    },
    pageParams: {
        type: Object,
        default: {
          colors: [
            {name: '高速', featureType: 'highway', color: ''},
            {name: '国道', featureType: 'nationalway', color: ''},
            {name: '省道', featureType: 'provincialway', color: ''},
            {name: '城市快速路', featureType: 'cityhighway', color: ''},
            {name: '城市主干路', featureType: 'arterial', color: ''},
          ]
        }
    }
})

// const colors = reactive([
//   {name: '高速', featureType: 'highway', color: ''},
//   {name: '国道', featureType: 'nationalway', color: ''},
//   {name: '省道', featureType: 'provincialway', color: ''},
// ])

const changeTheme = () => {
  emit('changeTheme', props.pageParams.colors)
}

</script>

<style>
.demo-color-block {
  display: flex;
  align-items: center;
  margin-bottom: 16px;
}
.demo-color-block .demonstration {
  font-size: v-bind("textFontSize+'px' ");
  color: white;
  margin-right: 16px;
}
</style>
