<template>
  <div></div>
</template>

<script lang="ts" setup>
import { h, ref, onMounted, onUnmounted, reactive, onBeforeUnmount } from 'vue'
import { useSessionStore } from '@/store/session'
import { Client, Stomp } from "@stomp/stompjs";
import { ElNotification } from 'element-plus'
import SockJS from 'sockjs-client/dist/sockjs.min.js';
const session = useSessionStore()
const baseURL = import.meta.env.VITE_APP_BASE_API.replace('/api', '/ws')
const token = session.token
let stompClient: Client;

onMounted(() => {
  initWebSocket()
  //测试消息发送与广播
  // setInterval(() => {
  //   sendPrivateMessage('helloWebSocket--' + new Date().getTime());
  // }, 3000);
})

let reconnectFlag = true;
let reconnectAttempts = 0;
const maxReconnectAttempts = Infinity;
const baseReconnectDelay = 5000;
const maxReconnectDelay = 15000;
let timeoutId: number ;

const initWebSocket = () => {
  console.log('initWebSocket----stompClient----------------------------');
  disconnect();
  if(reconnectFlag){
    const sockURL = `${baseURL}?token=${token}`;
    const socket = new SockJS(sockURL);

    stompClient = new Client({
      webSocketFactory: () => socket,
      //debug: (str) => console.log(str),
      reconnectDelay: 0,  // 禁用自动重连(自动重连有问题)，自己实现
      heartbeatIncoming: 4000,
      heartbeatOutgoing: 4000,
    });
    
    stompClient.onConnect = (frame) => {
      console.log('WebSocket已连接: ', frame.command);
      reconnectAttempts = 0;
      subscribe();
    };

    stompClient.onWebSocketClose = () => {
      _handleDisconnection('WebSocket closed');
    }

    stompClient.onUnhandledMessage = (message) => {
      if (message.command === 'ERROR' && 
          message.headers.message?.includes('heartbeat')) {
        console.error('Heartbeat lost, reconnecting...');
        _handleDisconnection('Heartbeat lost');
      }
    }

    stompClient.onDisconnect = () => {
      console.log('onDisconnect...');
    }

    stompClient.onStompError = (frame) => {
      _handleDisconnection(`STOMP error: ${frame.headers.message}`);
    };

    stompClient.onWebSocketError = (error) => {
      _handleDisconnection(`WebSocket error: ${error.message}`);
    };
    
    stompClient.activate();
  }
}

  // 订阅topic
const subscribe = () =>{ 
  stompClient.subscribe('/topic/warnMsg', (message) => {
    //console.log('/topic/warnMsg收到：', message.body);
    handleWarnMessage(message.body)
  });
}

const _handleDisconnection = (reason: any) => {
  if(reconnectFlag){
    console.log(`Disconnected: ${reason}`);
    if (reconnectAttempts >= maxReconnectAttempts) {
      console.error('Max reconnection attempts reached');
      return;
    }
    reconnectAttempts++;
    const delay = _calculateReconnectDelay();
    console.log(`Will reconnect in ${delay}ms (attempt ${reconnectAttempts})`);
    if(timeoutId){
      clearTimeout(timeoutId)
    }
    timeoutId = setTimeout(() => {
      console.log('stompClient.deactivate()----');
      stompClient.deactivate().then(() => {
        initWebSocket()
      });
    }, delay);
  }
}

  const _calculateReconnectDelay = () =>{ 
    // 指数退避算法 + 随机抖动
    const delay = Math.min(
      baseReconnectDelay * Math.pow(2, reconnectAttempts - 1),
      maxReconnectDelay
    );
    return delay * (1 + Math.random() * 0.3); // 添加30%随机抖动
  }

const sendPrivateMessage = (message: string) => {
  if (stompClient && stompClient.connected) {
    stompClient.publish({
      destination: '/app/sendMsg',
      body: message
    });
  }
}


const handleWarnMessage = (message: string) => {
  const msg = JSON.parse(message)
  //console.log(msg)
  ElNotification({
    title: `${msg.warnTopic}-${msg.warnName}`,
    message: h('div', null, [
      h('div', { style: 'font-size: 1.125rem; color: #dedfe4;' }, 
        msg.warnMsg),
      h('div', { style: 'font-size: 1.125rem; color: #3cc1ff;' }, 
        `【预警时间】：${msg.warnTime}`),
    ]),
    type: getWarnType(msg.warnLevel),
    duration: 0,
    customClass: "warn-notification", // 自定义类名
  })
}

const getWarnType = (warnLevel: number) => {
  switch (warnLevel) {
      case 1:
        return 'info'
      case 2:
        return 'warning'
      case 3:
        return 'error'
      default:
        return undefined
    }
}

const disconnect = (closeReconnect: boolean = false) => {
  if (closeReconnect){ reconnectFlag = false };
  stompClient?.forceDisconnect();
  stompClient?.deactivate();
}

onBeforeUnmount(() => {
  if(timeoutId){
    clearTimeout(timeoutId)
  }
  disconnect(true)
});

</script>

<style lang="less" scoped>

</style>

<style>
.warn-notification{
  background-color: rgba(5, 27, 88, 0.9);
  color: #dd7b11 !important;
  border: 2px solid #073f97;
}

.warn-notification .el-notification__title {
  --el-notification-title-color: #ffffff;
}

.warn-notification .el-notification__closeBtn svg{
  display: none;
}

.warn-notification .el-notification__closeBtn{
  background-image: url('@/assets/images/maps/common/tip-remove.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}
</style>

