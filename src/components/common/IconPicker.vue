<template>
  <ElSelect
    popper-class="icon-picker-popper"
    v-model="model"
    :filterable="props.filterable"
    :disabled="props.disabled"
    :clearable="props.clearable"
    :loading="loadingIcons"
    @clear="() => (model = '')"
  >
    <template #label="{ value }">
      <ElSpace>
        <ElIcon><component :is="value" /></ElIcon>
        {{ value }}
      </ElSpace>
    </template>
    <ElOption v-for="key in Object.keys(icons)" class="option" :key="key" :value="key" :title="key">
      <ElIcon>
        <component :is="key" />
      </ElIcon>
    </ElOption>
  </ElSelect>
</template>

<script lang="ts" setup>
import useTransition from '@/hooks/useTransition'
import { ElIcon, ElOption, ElSelect, ElSpace } from 'element-plus'
import { onMounted, ref, type Component } from 'vue'

// Hooks
const props = defineProps<{
  disabled?: boolean
  clearable?: boolean
  filterable?: boolean
}>()
const model = defineModel<string>({ default: '' })

const icons = ref({})
const [loadingIcons, loadIcons] = useTransition()

// Handlers
const fetchIcons = async (): Promise<{ [name: string]: Component }> =>
  await import('@element-plus/icons-vue')

// Mounted
onMounted(() => {
  loadIcons(async () => {
    icons.value = await fetchIcons()
  })
})
</script>

<style lang="scss" scoped></style>
<style lang="scss">
.icon-picker-popper {
  .el-select-dropdown__list {
    width: 350px;
    height: auto;
    max-height: 200px;
    overflow-y: auto;
    padding: 1rem;
    display: grid !important;
    grid-template-columns: repeat(10, 1fr);
  }

  .el-select-dropdown__item:hover {
    background-color: #fff;
  }

  .el-select-dropdown__item.selected {
    color: #606266;
    font-weight: normal;
  }

  .option {
    display: block;
    margin: 0;
    padding: 0;
    text-align: center;
    cursor: pointer;
    width: 30px;
    height: 30px;
    line-height: 30px;
    font-size: 1.5rem;
    transition: 0.1s ease-out;
  }

  .option:hover {
    color: #327edb;
    font-size: 2rem;
  }
}
</style>
