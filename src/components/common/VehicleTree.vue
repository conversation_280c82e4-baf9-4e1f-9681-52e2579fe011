<template>
  <div class="search-bar">
    <el-input v-model="searchValue" size="large" class="search-bar-input" placeholder="搜索车牌或驾驶员" :suffix-icon="Search" />
    <el-button size="large" :icon="Refresh" class="search-bar-button-reset">重置</el-button>
    <el-button size="large" :icon="Crop" class="search-bar-button-area" @click="open()">区域</el-button>
  </div>
  <div class="search-result">
    <el-tree ref="treeRef" class="el-tree-box" :data="props.dataTree" show-checkbox node-key="id" highlight-current
      :props="defaultProps" @check="handleClick">
      <template #default="{ node, data }">
        <div v-if="node.level != 1">
          <span v-if="data.online" class="circle-online"></span>
          <span v-else class="circle-offline"></span>
        </div>
        {{ node.label }}
      </template>
    </el-tree>
  </div>
  <car-marker ref="carMarkerRef" :map="map"/>
</template>

<script setup lang="ts">
import { Crop, Refresh, Search } from '@element-plus/icons-vue'
import { ref } from 'vue'
import { useMapPolygonDraw } from "@/hooks/map-draw/useMapPolygonDraw";
import { useBaseMap } from "@/hooks/useBaseMap";
import { MAP_TYPE } from "@/constant/map";
import { useMapMarker } from '@/hooks/useMapMarker'
import TrackCard from "./TrackCard.vue";

import CarMarker from '@/components/map/markers/CarMarker.vue';
const carMarkerRef = ref<InstanceType<typeof CarMarker> | null>(null)

const searchValue = ref('')
const treeRef = ref<any>()

const defaultProps = {
  children: 'children',
  label: 'label',
}

interface VehicleTreeProps {
  dataTree: VehicleTree
  check?: Function
}

const props = defineProps<VehicleTreeProps>()
const { map } = useBaseMap(MAP_TYPE.BASE)

/** 如果父组件传了check方法，走父组件的自身的业务逻辑 */
const handleClick = (data: any, data1: any) => {
  console.log('--------sub.handleClick-----------')
  
  if(props.check) {
    props.check(treeRef)
    return
  } 

  const dataList: Array<any> = []
  carMarkerRef.value?.removeMarker()
  const checkedNodes = treeRef.value.getCheckedNodes(true, true)
  checkedNodes.forEach((element: any) => {
    dataList.push({ lng: element.lng, lat: element.lat })
  })
  carMarkerRef.value?.addMarker(dataList)
  //trackVisible.value = true
}

// 区域选择-api方式
let {open, hidden} = useMapPolygonDraw(useBaseMap(MAP_TYPE.BASE)?.map, (overlay) => {
  console.log('overlay', overlay)// todo 1、拿到数据做业务处理
  hidden()// 2、处理完成关闭范围选择
})


</script>

<style>
.el-input__inner::placeholder {
  font-size: var(--font-size-level3)
}
</style>

<style lang="css" scoped>
::v-deep .el-tree-node__content {
  padding-top: 17px;
  /* 上间距 */
  padding-bottom: 17px;
  /* 下间距 */
}
</style>

<style scoped>
/* ::v-deep .el-input__inner::placeholder {
  font-size: 16px; 
} */

/* ::v-deep .el-tree .el-checkbox__inner {
    background-color: #165DFF !important;
    border-color: #165DFF !important;
}

::v-deep .el-tree .is-checked .el-checkbox__inner {
    background-color: #165DFF !important;
    border-color: #165DFF !important;
}
 
::v-deep .el-tree .is-checked .el-checkbox__inner::after {
    border-color: #fff !important;
} */
</style>