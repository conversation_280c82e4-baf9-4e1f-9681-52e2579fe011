<template>
    <div style="width: 100%;height: 100%;">
        <div class="header-bg"></div>
        <el-row class="header">
            <el-col :span="6">
                <el-row style="margin-bottom: 16px">
                    <el-col :span="8">2024/10/10</el-col>
                    <el-col :span="8">星期六</el-col>
                    <el-col :span="8">09:21</el-col>
                </el-row>
            </el-col>
            <el-col :span="12">
                <el-row class="title">
                    <el-col :span="24" style="align-items: center;display: flex;justify-content: center;">
                        <el-image style="width: 50px;height: 48px;margin-right: 6px;" :src="logoURL" />
                        {{title}}
                    </el-col>
                </el-row>
            </el-col>
            <el-col :span="6">
                <el-row style="margin-bottom: 16px">
                    <el-col :span="8">
                        <el-text v-show="isWeather" class="text-icon">
                            <el-icon>
                                <Pouring />
                            </el-icon>
                            23°C
                        </el-text>
                    </el-col>
                    <el-col :span="8" class="text-icon"
                        style="align-items: center;display: flex;justify-content: center;">
                        <el-dropdown trigger="click">
                            <span class="el-dropdown-link text-icon">
                                管理员<el-icon class="el-icon--right"><arrow-down /></el-icon>
                            </span>
                            <template #dropdown>
                                <el-dropdown-menu style="background-color: rgba(255, 255, 255, 0.5)">
                                    <el-dropdown-item icon="Setting" @click="">个人设置</el-dropdown-item>
                                    <el-dropdown-item icon="SwitchButton" divided
                                        @click="loginOut">退出系统</el-dropdown-item>
                                </el-dropdown-menu>
                            </template>
                        </el-dropdown>
                    </el-col>
                    <el-col :span="8">
                        <el-text class="text-icon">
                            <el-dropdown trigger="click">
                                <span class="el-dropdown-link text-icon">
                                    <el-icon><Help /></el-icon>
                                    <el-icon class="el-icon--right"><arrow-down /></el-icon>
                                </span>
                                <template #dropdown>
                                    <el-dropdown-menu style="background-color: rgba(255, 255, 255, 0.5)">
                                        <el-dropdown-item @click="changeTheme('normal')">标准</el-dropdown-item>
                                        <el-dropdown-item @click="changeTheme('dark')">暗色</el-dropdown-item>
                                        <el-dropdown-item @click="changeTheme('light')">亮色</el-dropdown-item>
                                    </el-dropdown-menu>
                                </template>
                            </el-dropdown>
                        </el-text>
                    </el-col>
                </el-row>
            </el-col>
        </el-row>

    </div>

    
</template>

<script setup lang="ts">
import { onMounted, onUnmounted, ref } from "vue";
import { useRouter } from 'vue-router'
import { useSessionStore } from '@/store/session'
import logoURL from '@/assets/images/common/login-badge.png';


import loginApi from '@/service/login'
const router = useRouter()
const session = useSessionStore()

const emit = defineEmits(['changeTheme'])
const props = defineProps({
    title: {
        type: String,
        default: '交通运行监控调度指挥平台'
    },
    isWeather: {
        type: Boolean,
        default: true
    },
    titleFontSize: {
        type: Number,
        default: 30
    },
    textFontSize: {
        type: Number,
        default: 17
    },
    pageParams: {
        type: Object,
        default: {}
    },
})


async function loginOut() {
    await loginApi.loginOut({})
    session.$reset()
    router.replace({ path: '/' })
}

const changeTheme = (theme: string) => {
    emit('changeTheme', theme)
    // console.dir(props.pageParams)
    // if(props.pageParams.arr){
    //     props.pageParams.arr.push(78)
    // }
}

</script>

<style scoped>

.title{
    align-items: center;
    font-size: v-bind("titleFontSize+'px'");
    font-family: '黑体';
    text-align: center;
}

.header {
    position: absolute;
    width: 100%;
    height: 85%; 
    background-color: rgba(255, 255, 255, 0);
    z-index: 99999;
    align-items: center;
    color: white;
    text-align: center;
    font-size: v-bind("textFontSize+'px' ");
}

.text-icon {
    font-size: v-bind("textFontSize+'px'");
    color: white;
}

.el-dropdown-link {
    cursor: pointer;
    color: white;
    display: flex;
    align-items: center;
}

.header-bg {
    background-image: url('@/assets/images/common/header.png');
    /* height: 70px; */
    height: 90%; 
    position: absolute;
    width: 100%;
    z-index: 99999;
    background-repeat: round;
    background-size: cover;
}
</style>