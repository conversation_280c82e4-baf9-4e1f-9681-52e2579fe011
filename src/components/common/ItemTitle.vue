<template>
  <div class="item-title-container">
    <div class="item-title-header">
      <div class="item-title-section">
        <div class="item-title-text">{{ title }}</div>
      </div>

      <div class="item-title-actions">
        <slot name="actions">
          <el-button
            v-if="props.moreClick"
            class="item-title-more-btn"
            link
            size="mini"
            type="primary"
            @click="handleMoreClick"
          >
            {{ moreText }}
          </el-button>
        </slot>
      </div>
    </div>

    <div class="item-title-content">
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { defineProps, defineEmits, useSlots, computed, useAttrs } from 'vue'

const props = defineProps<{
  title: string // 标题内容
  moreText?: string // 更多按钮文本
  showMore?: boolean // 手动控制是否显示更多按钮（备选方案）
  moreClick?: () => void
}>()

const slots = useSlots()

// 处理更多按钮点击
const handleMoreClick = () => {
  props.moreClick!()
}

const moreText = ( props.moreText ?? '更多') + '>'

// // 简化的显示逻辑
// const shouldShowMoreButton = computed(() => {
//   // 检查 actions 插槽是否有内容
//   const hasActionsSlot = !!slots.actions
//   if (hasActionsSlot) {
//     return false
//   }
//   // 如果传入 moreText，显示按钮
//   if (props.moreText) return true
//   //   如果传入 showMore，显示按钮
//   if (props.showMore !== undefined) return props.showMore
// })
</script>

<style scoped>
.item-title-container {
  width: 100%;
  height: auto;
  overflow: hidden;
}

.item-title-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
  padding: 10px 4px 0 4px;
  gap: 16px;
}

.item-title-section {
  flex: 1;
  min-width: 0; /* 防止溢出 */
}

.item-title-text {
  font-weight: 600;
  font-size: var(--font-size-level3);
  color: #1d2129;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.item-title-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

.item-title-more-btn {
  font-family: var(--font-family-normal);
  font-weight: 500 !important;
  cursor: pointer !important;
  font-size: 16px !important;
  color: #165dff !important;
  padding: 4px 12px !important;
  background: rgba(22, 93, 255, 0.1) !important;
  border-radius: 4px !important;
  transition: all 0.2s ease !important;
  display: inline-flex !important;
  align-items: center !important;
  white-space: nowrap !important;
  border: none !important;
}

.item-title-more-btn:hover,
.item-title-more-btn:focus {
  background: rgba(22, 93, 255, 0.1) !important;
  box-shadow: none !important;
  color: #165dff !important;
  border-color: transparent !important;
}

.item-title-content {
  width: 100%;
  margin-top: 10px;
  height: auto;
  overflow: hidden;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .item-title-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .item-title-actions {
    width: 100%;
    justify-content: flex-end;
  }

  .item-title-more-btn {
    font-size: 14px !important;
    padding: 3px 10px !important;
  }
}
</style>
