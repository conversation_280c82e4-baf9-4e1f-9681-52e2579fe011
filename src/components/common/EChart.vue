<!--通用echarts组件-->
<template>
  <div class="echart-style" ref="chartRef"></div>
</template>

<script lang="ts" setup>
import { useECharts } from '@/hooks/useECharts'
import { onMounted, ref, watch } from 'vue'
import { EChartsCoreOption } from 'echarts'

// props
const props = withDefaults(
  defineProps<{
    data: any
  }>(),
  { data: () => [] }
)
const chartRef = ref(null)
const chart = useECharts(props.data, chartRef)

onMounted(() => {
  loadData(props.data)
})
// watch
watch(
  () => props.data,
  (data) => {
    loadData(data)
  }
)

// Handlers
const loadData = (data: OptionDataItemObject[]) => {
  chart.emptyDataCheck(data)
  chart.updateChart(data as EChartsCoreOption)
}

// export
defineExpose({
  loadData
})
</script>

<style lang="scss" scoped>
//.echart-style {
//  width: 100%;
//height: 100%;
//}
</style>
