<template>
  <!-- 抽离tooltip实现单例，避免因为循环表格产生多个组件导致的内存增加 -->
  <el-tooltip
    effect="dark"
    :placement="placement"
    virtual-triggering
    :visible="tipVisible"
    :virtual-ref="tipMouseRef"
    :content="tipContent"
  />
</template>

<script setup lang="ts">
import { ref } from 'vue'

const props = defineProps({
  placement: {
      type: String,
      default: 'top-start'
  }
})

const tipVisible = ref(false)
const tipContent = ref('')
const tipMouseRef = ref()

const showTooltip = (e: Event, content: string) => {
  console.log(e, content)
  tipMouseRef.value = e.currentTarget
  tipContent.value = content
  tipVisible.value = true
}

const closeTooltip = () => {
  tipVisible.value = false
  tipMouseRef.value = null
  tipContent.value = ''
}

defineExpose({
  showTooltip,
  closeTooltip
})
</script>
