<template>
  <template v-for="m in props.menu">
    <ElMenuItem v-if="!m.children && m.menuType === '1'" :key="m.path" :index="m.id" :route="m.path">
      <ElIcon v-if="m.icon">
        <component :is="m.icon" />
      </ElIcon>
      <span>{{ m.label }}</span>
    </ElMenuItem>
    <ElSubMenu v-if="m.children && m.menuType === '1'" :key="`${m.path}-sub`" :index="m.id">
      <template #title>
        <ElIcon v-if="m.icon">
          <component :is="m.icon" />
        </ElIcon>
        <span>{{ m.label }}</span>
      </template>
      <MenuRenderer :menu="m.children" />
    </ElSubMenu>
    
  </template>
</template>

<script lang="tsx" setup>
import { ElIcon, ElMenuItem, ElSubMenu } from 'element-plus'

const props = defineProps<{
  menu: MenuNode[]
}>()
</script>
