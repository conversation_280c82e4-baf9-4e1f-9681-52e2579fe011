<template>
    <div class="components-container">
        <div ref="lineChart" class="echart-container"></div>
    </div>
</template>

<script setup lang="ts">
import { ref, reactive, watch, watchEffect, nextTick, onMounted } from 'vue';

const props = defineProps({
    basicLineModel: {
        type: Object
    }
})

watch(() => props.basicLineModel, (newVal: any) => {
    console.log('------------------BasicLineChart---------------------', newVal)
    nextTick(()=> {
        showCharts(newVal)
    })
},{ deep: true, immediate: true})


import { useECharts } from '@/hooks/useECharts';  // 引入 useECharts hook
import { baseLineConfig } from '@/assets/echarts/baseConfig/lineConfig';

const lineChart = ref<HTMLElement | null>(null);
const chartConfig = baseLineConfig();
const { updateChart, emptyDataCheck } = useECharts(chartConfig, lineChart);
chartConfig.yAxis.name = '单位/条'
chartConfig.grid.top = 32
chartConfig.grid.left = 4
chartConfig.yAxis.splitNumber = 2
chartConfig.tooltip.formatter = function (params: any) {
    return params[0].name + ': ' + params[0].value + '条';
}

const showCharts = async (basicLineModel: BasicLineModel) => {
    chartConfig.xAxis.data = basicLineModel.seriesData
    chartConfig.series[0].data = basicLineModel.seriesData
    emptyDataCheck(chartConfig.series[0].data)
    updateChart(chartConfig)
}
</script>