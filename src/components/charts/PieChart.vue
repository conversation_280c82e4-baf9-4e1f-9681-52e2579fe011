<template>
    <div style="width: 100%;height:100%;border-radius: 4px;border: 1px solid #346667;">
        <div style="height: 15%;background-color: #346667;align-items: center;display: flex;padding-left: 10px;color: white;font-size: 14px;">
            {{title}}
        </div>
        <div ref="chartInstance" style="width: 100%;height:85%;"></div>
    </div>  
</template>

<script setup lang="ts" name="PieChart">

import { onMounted, onUnmounted, ref } from "vue";
import * as echarts from 'echarts';
import type { EChartsType } from 'echarts'

const props = defineProps({
    title: {
    type: String,
    default: '巡逻人员'
  }
})

onMounted(() => {
    chart()
    _init()
});

const chartInstance = ref()
let myChart: EChartsType
let resizeObserver: ResizeObserver

function _init() {
  try {
    //监听div容器的宽度变化,使容器自适应大小
    resizeObserver = new ResizeObserver(entries => {
        myChart.resize();
    })
    resizeObserver.observe(chartInstance.value)
  } catch (err) {
    console.error(err)
  }
}

onUnmounted(() => {
  if(resizeObserver){
    resizeObserver.disconnect()
  }
  myChart?.dispose()
})

function chart() {
    myChart = echarts.init(chartInstance.value);
    myChart.setOption({
        backgroundColor: '#2A2A2A', // 设置深色背景
        tooltip: {
            trigger: 'item'
        },
        legend: {
            top: '5%',
            left: 'center',
            textStyle: {
                "fontSize": 13,
                color: '#FFFFFF'
            }
        },
        series: [
            {
                name: 'Access From',
                type: 'pie',
                radius: ['40%', '70%'],
                center: ['50%', '58%'],
                avoidLabelOverlap: false,
                itemStyle: {
                    borderRadius: 0,
                    borderColor: '#FFFFFF',
                    borderWidth: 1
                },
                label: {
                    show: false,
                    position: 'center'
                },
                emphasis: {
                    label: {
                        show: true,
                        fontSize: 18,
                        fontWeight: 'bold'
                    }
                },
                labelLine: {
                    show: false
                },
                data: [
                    { value: 1048, name: '单兵' },
                    { value: 735, name: '武警' },
                    { value: 580, name: '车辆' },
                    { value: 484, name: '其他' },
                ],
                axisLabel: {
                    color: '#FFFFFF'
                }
            }
        ]
    });
}
</script>