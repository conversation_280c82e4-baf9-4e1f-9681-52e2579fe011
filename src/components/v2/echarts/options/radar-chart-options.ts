import type {EChartsOption} from "echarts";

/**
 * @name: 名称
 * @description: TODO 折线图options
 * @author: liudingbang
 * @date: 2025/5/30 09:35
 */
export type SeriesData = Array<number | Record<string, any> | string>

interface ChartParams {
    legendData: string[]
    indicator: {name: string, max: number}[]
    data: SeriesData[]
    colors?: Array<string>
}

/**
 *雷达图
 * @param legendData 图例
 * @param xAxisData x轴数据
 * @param data y轴数据-注意，是二维数组，每个图例对应的一组一维数据
 */
const colors = ['19, 173, 255', '245, 166, 35']
export function stackedRadarChart({legendData, indicator, data}: ChartParams): EChartsOption {
    let seriesData: Array<Record<string, any>> = []
    legendData.forEach((item, index) => {
        const color = colors[index]
        seriesData.push({
            name: item,
            type: "radar",
            symbol: "circle",
            symbolSize: 5,
            itemStyle: {
                normal: {
                color: `rgba(${color}, 1)`,
                borderColor: `rgba(${color}, 0.4)`,
                borderWidth: 5,
                },
            },
            areaStyle: {
                normal: {
                color: `rgba(${color}, 0.5)`,
                },
            },
            lineStyle: {
                normal: {
                color: `rgba(${color}, 1)`,
                width: 1,
                type: "dashed",
                },
            },
            data: data[index]
        })
    })
    return {
        normal: {
            top: 200,
            left: 300,
            width: 500,
            height: 400,
            zIndex: 6,
            backgroundColor: "",
        },
        color: ["rgba(245, 166, 35, 1)", "rgba(19, 173, 255, 1)"],
        title: {
            show: false,
            text: "基础雷达图",
            left: "40%",
            top: "1%",
            textStyle: {
            fontSize: 18,
            color: "#fff",
            fontStyle: "normal",
            fontWeight: "normal",
            },
        },
        tooltip: {
            show: true,
            trigger: 'item',
        },
        legend: {
            show: false,
            icon: "circle",
            left: "35%",
            top: "90%",
            orient: "horizontal",
            textStyle: {
            fontSize: 14,
            color: "#fff",
            },
            data: legendData,
        },
        radar: {
            center: ["50%", "50%"],
            radius: "70%",
            startAngle: 90,
            splitNumber: 4,
            shape: "circle",
            splitArea: {
            areaStyle: {
                color: ["transparent"],
            },
            },
            axisLabel: {
            show: false,
            fontSize: 18,
            color: "#fff",
            fontStyle: "normal",
            fontWeight: "normal",
            },
            axisLine: {
            show: true,
            lineStyle: {
                color: "grey", //
            },
            },
            splitLine: {
            show: true,
            lineStyle: {
                color: "grey", //
            },
            },
            indicator: indicator,
        },
        series: seriesData
        }
}
