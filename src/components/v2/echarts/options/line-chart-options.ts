import type {EChartsOption} from "echarts";

/**
 * @name: 名称
 * @description: TODO 折线图options
 * @author: liudingbang
 * @date: 2025/5/30 09:35
 */
export type SeriesData = Array<number | Record<string, any> | string>

interface ChartParams {
    legendData: string[]
    xAxisData: string[] | number[]
    data: SeriesData[]
    colors?: Array<string>
}

/**
 * 堆叠折线图
 * @param legendData 图例
 * @param xAxisData x轴数据
 * @param data y轴数据-注意，是二维数组，每个图例对应的一组一维数据
 */
export function stackedLineChart({legendData, xAxisData, data}: ChartParams): EChartsOption {
    let seriesData: Array<Record<string, any>> = []
    legendData.forEach((item, index) => {
        seriesData.push({
            name: item,
            type: 'bar',
            stack: 'two',
            emphasis: {
                itemStyle: {
                    shadowBlur: 10,
                    shadowColor: 'rgba(0,0,0,0.3)',
                },
            },
            data: data[index]
        })
    })
    return {
        legend: {
            data: legendData,
            itemWidth: 8,
            itemHeight: 8,
        },
        tooltip: {},
        xAxis: {
            data: xAxisData,
            name: '',
            axisLine: {onZero: true},
            splitLine: {show: false},
            splitArea: {show: false},
            axisLabel: {
                interval: 0,
                rotate: -40,
            },
        },
        yAxis: {
            // 设置分割线数量
            splitNumber: 3,
        },
        grid: {
            bottom: 70,
        },
        series: seriesData,
    }
}

/**
 * 带边框的普通折线图
 * @param legendData 图例
 * @param xAxisData x轴数据
 * @param data y轴数据-注意，是二维数组，每个图例对应的一组一维数据
 */
export function borderLineChart({legendData, xAxisData, data, colors}: ChartParams): EChartsOption {
    let seriesData: Array<Record<string, any>> = []
    legendData.forEach((item, index) => {
        seriesData.push(
            {
                name: item,
                data: data[index],
                type: 'line',
                showSymbol: false, // 隐藏圆点
                smooth: false, //默认是false,判断折线连线是平滑的还是折线
                areaStyle: {
                    // 设置阴影颜色
                    color: colors![index], // 这里使用 RGBA 格式定义颜色和透明度
                },
            }
        )
    })
    return {
        tooltip: {
            trigger: 'axis',
        },
        legend: {
            data: legendData,
            itemWidth: 8, // 图例标记的图形宽度
            itemHeight: 8, // 图例标记的图形高度
            right: 20, //调整图例位置
            icon: 'rect', //图例前面的图标形状
            textStyle: {
                //图例文字的样式
                fontSize: 12, //图例文字大小
            },
        },
        grid: {
            left: '8%',
            right: '5%',
            bottom: '10%',
            containLabel: true,
        },
        xAxis: {
            type: 'category',
            data: xAxisData,
            axisLabel: {
                // color: '#D8F0FF',
            },
        },
        yAxis: {
            type: 'value',
            // 副标题颜色
            nameTextStyle: {
                // color: '#D8F0FF',
            },
            axisLabel: {
                // color: '#D8F0FF',
            },
        },
        series: seriesData
    }
}