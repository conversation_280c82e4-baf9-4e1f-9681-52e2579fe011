import type {EChartsOption} from "echarts";
import {SeriesData} from "@/components/v2/echarts/options/line-chart-options";

/**
 * @name: 名称
 * @description: TODO 饼图配置
 * @author: liudingbang
 * @date: 2025/6/4 17:18
 */
export function pieChartOption(data: SeriesData): EChartsOption {
    return {
        tooltip: {
            trigger: 'item',
            formatter: '{b}: {c} ({d}%)'
        },
        legend: {
            orient: 'horizontal',
            bottom: -5,
            left: 'center',
            itemWidth: 10,
            itemHeight: 10
        },
        series: [
            {
                name: '',
                type: 'pie',
                radius: ['50%', '70%'],
                label: {
                    // formatter: '{b|{b}}\n{c}元',
                    rich: {
                        b: {
                            fontSize: 12,
                            lineHeight: 20
                        }
                    }
                },
                data
            }
        ]
    } as EChartsOption
}
