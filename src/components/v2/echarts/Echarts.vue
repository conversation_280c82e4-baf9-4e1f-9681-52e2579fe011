<!--
@description: TODO 通用echarts组件
@author: liudingbang
@date: 2025/5/29 17:03
-->
<template>
  <div class="echart-style" ref="chartRef" :class="className"></div>
</template>

<script lang="ts" setup>
import {useECharts} from '@/hooks/useECharts'
import {onMounted, onUnmounted, ref, watch} from 'vue'

// props
const props = withDefaults(
    defineProps<{
      // options数据
      options: any,
      // 默认加载中配置
      defaultLoadingConfig: object
      // 样式类
      className?: string
    }>(),
    {
      options: () => {
        return {}
      },
      defaultLoadingConfig: () => {
        return {
          text: '数据接入中',
          showSpinner: false,
          textColor: '#1862ff',
          maskColor: 'rgba(255,255,255,0.9)',
          fontSize: '1rem',
          fontWeight: 'bold',
          zlevel: 0
        }
      }
    }
)
const chartRef = ref(null)
const chart = useECharts(props.options, chartRef)

const handleResize = (entries: any) => {
  for (const entry of entries) {
    chart.resizeChart()
  }
}
let resizeObserver: ResizeObserver | null = null
onMounted(() => {
  loadData(props.options)
  // 监听父容器尺寸变化
  const parent = (chartRef.value as unknown as HTMLElement)!.parentElement
  resizeObserver = new ResizeObserver(handleResize)
  if (parent) {
    resizeObserver.observe(parent)
  }
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
})


watch(
    () => props.options,
    (data) => {
      loadData(data)
    }
)

const loadData = (data: Record<string, any>) => {
  if (!data) {
    chart.emptyDataCheck(false)
    return
  }
  if (data.series) {
    if (Array.isArray(data.series)) {
      if (data.series.length == 0) {
        let isSuC = chart.emptyDataCheck(false, props.defaultLoadingConfig)
        if (!isSuC) {
          return
        }
      }
    } else if (typeof data.series == 'object') {
      let isSuC = chart.emptyDataCheck(data.series?.data ?? [], props.defaultLoadingConfig)
      if (!isSuC) {
        return
      }
    }
  } else {
    let isSuC = chart.emptyDataCheck(data?.data ?? [], props.defaultLoadingConfig)
    if (!isSuC) {
      return
    }
  }
  chart.updateChart(data)
  chart.emptyDataCheck(true)
}

defineExpose({
  loadData,
  emptyDataCheck: chart.emptyDataCheck,
  instance: chartRef,
  chart
})
</script>

<style lang="scss" scoped>
.echart-style {
  width: 100%;
  height: 100%;
}
</style>
