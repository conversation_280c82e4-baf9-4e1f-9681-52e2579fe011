<script setup lang="ts">
import { ref } from 'vue'
import imgView from '@/components/v2/common/img-view.vue'
const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: '报警详情'
  },
  // 预留字段
  type:{
    type: String,
    default: 'alertor'
  }
})
const tabs_active_index = ref(0)
const mockData = [
  {
    name: '车牌号',
    value: '糊B1231232'
  },
  {
    name: '企业名称',
    value: '广东飞机投有限公司'
  },
  {
    name: '驾驶员',
    value: '王一土'
  },
  {
    name: '预警时间',
    value: '2025年6月5日 10:31:24'
  },
  {
    name: '预警位置',
    value: '湖南省娄底市娄星区石井镇边上'
  },
  {
    name: '预警车速',
    value: '80km/h'
  },
  {
    name: '预警信息',
    value: '车胎爆炸'
  },
]
</script>

<template>
  <el-dialog :modelValue="dialogVisible" :title="title" width="80%">
   <div class="dialog-container">
     <div class="alertor-info">
       <div class="table">
         <div v-for="(item, index) in mockData" :key="index" class="table-tr" >
           <div class="table-td-caption">{{item.name}}</div>
           <div class="table-td-content">{{item.value}}</div>
         </div>
       </div>
     </div>
     <div class="alertor-map">
       <div class="tabs">
          <div class="tabs-item" :class="tabs_active_index === 0 ? 'tabs-item-active' : '' " @click="()=>tabs_active_index = 0 ">报警信息</div>
          <div class="tabs-item" :class="tabs_active_index === 1 ? 'tabs-item-active' : '' " @click="()=>tabs_active_index = 1 ">证据图片</div>
          <div class="tabs-item" :class="tabs_active_index === 2 ? 'tabs-item-active' : '' " @click="()=>tabs_active_index = 2 ">证据视频</div>
       </div>
       <div class="content">
        <div v-if="tabs_active_index === 0" class="map" style="width: 100%;height: 100%" ref="map_container"></div>
        <div v-if="tabs_active_index === 1" class="img-container" >
          <imgView />
        </div>
        <div v-if="tabs_active_index === 2" class="video-container" >
          <video src="" style="width: 100%;height: 100%;" controls></video>
        </div>
       </div>
     </div>
   </div>
  </el-dialog>
</template>

<style scoped>
.dialog-container{
  display: flex;
  justify-content: space-between;
  .alertor-info{
    width: 29.5%;
    height: calc(100vh * 0.7);
    .table{
      .table-tr{
        height: 50px;
        line-height: 50px;
        display: flex;
        font-size: var(--font-size-level4);

        .table-td-caption{
          width: 30%;
          text-align: center;
          background: var(--bg-fill-1);
          border: 1px solid var(--table-item-border-color);
        }
        .table-td-content{
          width: 70%;
          border: 1px solid var(--table-item-border-color);
          padding-left: 12px;
        }
      }
    }
  }
  .alertor-map{
    width: 69.5%;
    height: calc(100vh * 0.7);
    overflow: hidden;
    background-color: #ccc;
    border-radius: var(--radius-card);
    .tabs{
      height: 42px;
      line-height: 42px;
      padding-top: 4px;
      background: #f3f3f3;
      padding-left: 4px;
      display: flex;
      .tabs-item{
        width: 90px;
        height: 38px;
        text-align: center;
      }
      .tabs-item-active{
        background: #ffffff;
        color: var(--left-menu-item-color);
      }
      .tabs-item:hover{
        background: #ffffff;
        color: var(--left-menu-item-color);
      }
    }
    .content{
      height: calc(100vh * 0.7 - 42px);
      .img-container,.video-container{
        width: 100%;
        height: 100%;
      }

    }
  }
}
</style>
