/**
 * @name: 名称
 * @description: TODO 全局控制器
 * @author: liudingbang
 * @date: 2025/6/5 17:53
 */
// PopupController.ts
type PopupInstance = {
    id: string
    hide: () => void
}

const popupInstances = new Map<string, PopupInstance>()

export function registerPopup(id: string, hide: () => void) {
    popupInstances.set(id, { id, hide })
}

export function unregisterPopup(id: string) {
    popupInstances.delete(id)
}

export function notifyPopupOpen(currentId: string) {
    for (const [id, instance] of popupInstances) {
        if (id !== currentId) {
            instance.hide()
        }
    }
}
