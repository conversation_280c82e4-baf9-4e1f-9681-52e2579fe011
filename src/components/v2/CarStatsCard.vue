<template>
    <el-card class="stats-card" shadow="hover">
      
      <!-- 数据统计区域 -->
      <div class="stats-content">
        <div class="stats-item" v-for="item in statsData" :key="item.type">
          <span class="stats-label">{{ item.label }}</span>
          <span class="stats-value">{{ item.value }}
            <span v-if="item.unit" class="unit">{{ item.unit }}</span>
          </span>
        </div>
      </div>
      
      <!-- 自定义颜色进度条 -->
      <div class="color-bars">
        <div 
          v-for="(item, index) in statsData" 
          :key="'bar-'+index"
          class="color-bar" 
          :style="{
            width: `${item.percentage}%`,
            backgroundColor: item.color || defaultColors[index]
          }"
        >
          <span class="percentage-label">{{ item.label + item.percentage }}%</span>
        </div>
      </div>
    </el-card>
  </template>
  
  <script lang="ts" setup>
  import { computed,defineProps } from 'vue'
  
  const props = defineProps({
    statsData: {
      type: Array,
      required: true,
      validator: (value) => {
        return value.every(item => {
          return 'label' in item && 'value' in item && 'percentage' in item
        })
      }
    },
    defaultColors: {
      type: Array,
      default: () => ['#FF9A23', '#00BFFF', '#32CD32'] // 默认颜色数组
    }
  })

  </script>
  
  <style scoped>
  .stats-card {
    width: 100%;
    border-radius: 8px;
    padding: 4px;
  }
  
  .stats-content {
    display: flex;
    justify-content: space-between;
    margin-bottom: 12px;
  }
  
  .stats-item {
    display: flex;
    flex-direction: column;
    align-items: center;
  }
  
  .stats-label {
    color: #888;
    margin-bottom: 4px;
  }
  
  .stats-value {
    font-size: 20px;
    font-weight: bold;
  }
  .unit {
    font-size: 14px;
    margin-left: 2px;
    color: #666;
    }
  
  .color-bars {
    display: flex;
    height: 32px;
    border-radius: 4px;
    overflow: hidden;
  }
  
  .color-bar {
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    transition: width 0.3s ease;
  }
  
  .percentage-label {
    color: white;
    font-weight: bold;
    text-shadow: 0 0 2px rgba(0,0,0,0.5);
  }
  </style>