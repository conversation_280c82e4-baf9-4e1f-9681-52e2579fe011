<!--
@description: TODO 右侧详情对话框
@author: liudingbang
@date: 2025/5/30 16:49
-->
<template>
  <div class="pop" v-if="visible" :style="{ width }">
    <div class="detail">
      <div class="title">
        <div class="left">
          <span>{{ title }}</span>
        </div>
        <span class="close" @click="close">×</span>
      </div>
      <slot />
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, watch, onMounted, onBeforeUnmount } from 'vue'
import { registerPopup, unregisterPopup, notifyPopupOpen } from './popup-controller'

// 组件参数
const props = withDefaults(defineProps<{
  title: string
  visible: boolean
  width?: string
}>(), {
  title: '',
  visible: false
})
const emit = defineEmits<{
  (e: 'update:visible', value: boolean): void
}>()

// 本组件唯一 ID
const id = Symbol('popup_' + Math.random()).toString()

// 控制关闭
const close = () => {
  emit('update:visible', false)
}

// 监听 visible，如果变 true，通知控制器隐藏其他组件
watch(() => props.visible, (val) => {
  if (val) {
    notifyPopupOpen(id)
  }
})

// 注册到控制器
onMounted(() => {
  registerPopup(id, () => emit('update:visible', false))
})
onBeforeUnmount(() => {
  unregisterPopup(id)
})
</script>

<style scoped>
/* 同你之前的样式 */
.detail {
  width: 100%;
  height: 100%;
}

.title {
  display: flex;
  justify-content: space-between;
  margin-bottom: 20px;

  .close {
    cursor: pointer;
    font-weight: 900;
  }
}

.pop {
  position: fixed;
  top: 80px;
  right: 10px;
  width: auto;
  height: calc(100vh - 90px);
  background: #ffffff;
  box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.1);
  border-radius: 4px;
  padding: 20px;
  z-index: 1000;
}</style>
