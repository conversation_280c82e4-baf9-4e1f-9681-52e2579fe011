<!--
@description: 增强型时间范围搜索组件，支持日期选择和多输入框搜索
@author: liudingbang
@date: 2025/5/30 17:32
@update: 添加多输入框搜索功能，支持高度配置化展示
-->

<template>
  <div class="date-range-search-container">
    <div class="search-row">
      <!-- 日期选择部分 -->
      <div class="date-selection" v-if="showDateSelection">
        <div class="date-buttons">
          <template v-if="dateButtonsConfig.length > 0">
            <el-button 
              v-for="(btn, index) in dateButtonsConfig" 
              :key="index"
              :class="[select_data === btn.value ? 'active-btn' : '', btn.class || '']"
              @click="() => (select_data = btn.value)"
            >
              {{ btn.label }}
            </el-button>
          </template>
          <template v-else>
            <el-button :class="select_data === 1 ? 'active-btn' : ''" @click="() => (select_data = 1)"
            >今日
            </el-button>
            <el-button :class="select_data === 2 ? 'active-btn' : ''" @click="() => (select_data = 2)"
            >近7日
            </el-button>
            <el-button :class="select_data === 3 ? 'active-btn' : ''" @click="() => (select_data = 3)"
            >近30日
            </el-button>
            <el-button
                class="margin10"
                :class="select_data === 4 ? 'active-btn' : ''"
                @click="() => (select_data = 4)"
            >
              自定义
            </el-button>
          </template>
        </div>
        <div class="picker" v-if="showDatePicker && select_data === customDateValue">
          <el-date-picker
              v-model="date"
              :type="datePickerType"
              :range-separator="dateRangeSeparator"
              :start-placeholder="startPlaceholder"
              :end-placeholder="endPlaceholder"
              :disabled-date="disabledDate"
              :shortcuts="dateShortcuts"
              :style="{ width: '100%' }"
          />
        </div>
      </div>
      
      <!-- 搜索输入框部分 -->
      <div class="search-inputs-container" v-if="showSearchInput">
        <!-- 单个搜索框模式 -->
        <div class="search-input" v-if="!searchFields || searchFields.length === 0">
          <el-input
            v-model="searchKeyword"
            :placeholder="searchPlaceholder"
            clearable
            @input="handleSearchInput"
            @clear="handleClear"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
            <template #append v-if="showSearchButton">
              <el-button @click="handleSearch">{{ searchButtonText }}</el-button>
            </template>
          </el-input>
        </div>
        
        <!-- 多输入框模式 -->
        <template v-else>
          <div 
            v-for="(field, index) in searchFields" 
            :key="index"
            class="search-field-item"
            :style="{ width: field.width || 'auto' }"
          >
            <div v-if="field.label && field.labelPosition !== 'prepend'" class="field-label">
              {{ field.label }}
            </div>
            <el-input 
              v-if="field.type === 'input' || !field.type"
              v-model="searchValues[field.name]"
              :placeholder="field.placeholder || '请输入' + field.label"
              :clearable="field.clearable !== false"
              @input="() => handleFieldInput(field.name)"
              @clear="() => handleFieldClear(field.name)"
            >
              <template #prefix v-if="field.prefixIcon">
                <el-icon><component :is="field.prefixIcon" /></el-icon>
              </template>
              <template #prefix v-else-if="field.showIcon !== false">
                <el-icon><Search /></el-icon>
              </template>
              <template #prepend v-if="field.label && field.labelPosition === 'prepend'">
                {{ field.label }}
              </template>
            </el-input>
            
            <el-select
              v-else-if="field.type === 'select'"
              v-model="searchValues[field.name]"
              :placeholder="field.placeholder || '请选择' + field.label"
              :clearable="field.clearable !== false"
              @change="() => handleFieldInput(field.name)"
              @clear="() => handleFieldClear(field.name)"
            >
              <el-option 
                v-for="(option, optIndex) in field.options" 
                :key="optIndex"
                :label="option.label"
                :value="option.value"
              />
            </el-select>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, onMounted, reactive, ref, watch } from "vue";
import dayjs, { Dayjs } from "dayjs";
import { Search } from '@element-plus/icons-vue';

// 日期按钮配置接口
interface DateButtonConfig {
  label: string;
  value: number;
  class?: string;
}

// 搜索字段配置接口
interface SearchField {
  name: string;
  label?: string;
  type?: 'input' | 'select' | string;
  placeholder?: string;
  width?: string;
  clearable?: boolean;
  prefixIcon?: string;
  showIcon?: boolean;
  labelPosition?: 'prepend' | 'top' | 'left';
  options?: Array<{ label: string; value: any }>;
  defaultValue?: any;
}

// 日期快捷选项接口
interface DateShortcut {
  text: string;
  value: () => Date[];
}

// 日期选择相关
const select_data = ref<number>(1);
const today = dayjs();
const todayStart = dayjs().startOf('day');
const todayEnd = dayjs().endOf('day');
const sevenDaysAgo = dayjs().subtract(6, 'day');
const thirtyDaysAgo = dayjs().subtract(29, 'day');
const date = ref([]);
let todayRange = ref<Array<Dayjs>>([todayStart, todayEnd]);
let near7 = ref<Array<Dayjs>>([]);
let near30 = ref<Array<Dayjs>>([]);

// 搜索输入框相关
const searchKeyword = ref('');
const searchValues = reactive<Record<string, any>>({});

const props = withDefaults(defineProps<{
  // 默认时间
  date?: Array<Dayjs>;
  // 默认类型
  type?: number;
  // 是否显示日期选择
  showDateSelection?: boolean;
  // 是否显示日期选择器
  showDatePicker?: boolean;
  // 日期选择器类型
  datePickerType?: string;
  // 日期范围分隔符
  dateRangeSeparator?: string;
  // 开始日期占位符
  startPlaceholder?: string;
  // 结束日期占位符
  endPlaceholder?: string;
  // 自定义日期值
  customDateValue?: number;
  // 日期按钮配置
  dateButtonsConfig?: DateButtonConfig[];
  // 日期快捷选项
  dateShortcuts?: DateShortcut[];
  // 禁用日期函数
  disabledDate?: (date: Date) => boolean;
  
  // 是否显示搜索输入框
  showSearchInput?: boolean;
  // 搜索框占位符
  searchPlaceholder?: string;
  // 是否显示搜索按钮
  showSearchButton?: boolean;
  // 搜索按钮文本
  searchButtonText?: string;
  // 是否显示重置按钮
  showResetButton?: boolean;
  // 重置按钮文本
  resetButtonText?: string;
  // 是否在输入时实时搜索
  searchOnInput?: boolean;
  // 搜索防抖延迟(ms)
  debounceDelay?: number;
  // 多字段搜索配置
  searchFields?: SearchField[];
}>(), {
  date: () => [dayjs().subtract(6, 'day'), dayjs()],
  type: 1,
  showDateSelection: true,
  showDatePicker: true,
  datePickerType: 'daterange',
  dateRangeSeparator: '-',
  startPlaceholder: '开始日期',
  endPlaceholder: '结束日期',
  customDateValue: 4,
  dateButtonsConfig: [
    { label: '今日', value: 1 },
    { label: '近7日', value: 2 },
    { label: '近30日', value: 3 },
    { label: '自定义', value: 4 },
  ],
  dateShortcuts: [],
  showSearchInput: false,
  searchPlaceholder: '请输入关键词搜索',
  showSearchButton: true,
  searchButtonText: '搜索',
  showResetButton: true,
  resetButtonText: '重置',
  searchOnInput: false,
  debounceDelay: 300,
  searchFields: []
});

// 初始化搜索字段默认值
onMounted(() => {
  // 初始化搜索字段默认值
  if (props.searchFields && props.searchFields.length > 0) {
    props.searchFields.forEach(field => {
      if (field.defaultValue !== undefined) {
        searchValues[field.name] = field.defaultValue;
      } else {
        searchValues[field.name] = '';
      }
    });
  }
  
  // 初始化时间
  todayRange.value = [todayStart, todayEnd];
  near7.value = [sevenDaysAgo, today];
  near30.value = [thirtyDaysAgo, today];
  select_data.value = props.type;
  // 初始化赋值一次
  emit('update:date', formatDateRange(activeDate.value));
});

// 防抖函数
let debounceTimer: number | null = null;
const debounce = (fn: Function, delay: number) => {
  if (debounceTimer) {
    clearTimeout(debounceTimer);
  }
  debounceTimer = setTimeout(() => {
    fn();
  }, delay) as unknown as number;
};

const activeDate = computed(() => {
  if (select_data.value === 1) {
    return todayRange.value;
  } else if (select_data.value === 2) {
    return near7.value;
  } else if (select_data.value === 3) {
    return near30.value;
  } else {
    return date.value;
  }
});

// 监听日期选择变化
watch(select_data, () => {
  emit('update:date', formatDateRange(activeDate.value));
});

watch(date, () => {
  emit('update:date', formatDateRange(date.value));
});

// 单字段搜索相关方法
const handleSearchInput = () => {
  if (props.searchOnInput) {
    debounce(() => {
      emit('search', searchKeyword.value);
    }, props.debounceDelay);
  }
};

const handleSearch = () => {
  emit('search', searchKeyword.value);
};

const handleClear = () => {
  searchKeyword.value = '';
  emit('search', '');
};

// 多字段搜索相关方法
const handleFieldInput = (fieldName: string) => {
  // 无论searchOnInput属性如何，都触发搜索事件
  debounce(() => {
    emit('fieldSearch', { [fieldName]: searchValues[fieldName] });
    emit('searchChange', { ...searchValues });
  }, props.debounceDelay);
};

const handleFieldClear = (fieldName: string) => {
  searchValues[fieldName] = '';
  // 无论searchOnInput属性如何，都触发搜索事件
  emit('fieldSearch', { [fieldName]: '' });
  emit('searchChange', { ...searchValues });
};

const handleMultiFieldSearch = () => {
  emit('searchChange', { ...searchValues });
};

const handleReset = () => {
  // 重置所有搜索字段
  if (props.searchFields && props.searchFields.length > 0) {
    props.searchFields.forEach(field => {
      searchValues[field.name] = field.defaultValue !== undefined ? field.defaultValue : '';
    });
  } else {
    searchKeyword.value = '';
  }
  
  emit('searchReset');
  emit('searchChange', { ...searchValues });
};

// 格式化日期范围为年月日格式
const formatDateRange = (dateRange: Array<Dayjs>) => {
  console.log(dateRange)
  if (!dateRange || dateRange.length !== 2) return dateRange;
  const start = dayjs(dateRange[0]);
  const end = dayjs(dateRange[1]);

  return [
    start.isValid() ? start.format('YYYY-MM-DD') : null,
    end.isValid() ? end.format('YYYY-MM-DD') : null,
  ];
};

const emit = defineEmits<{
  (e: 'update:date', date: Array<string | null>): void;
  (e: 'search', keyword: string): void;
  (e: 'fieldSearch', fieldValue: Record<string, any>): void;
  (e: 'searchChange', values: Record<string, any>): void;
  (e: 'searchReset'): void;
}>();
</script>

<style scoped>
.date-range-search-container {
  display: flex;
  flex-direction: row;
  gap: 10px;
  width: 100%;
}

.search-row {
  display: flex;
  /* flex-wrap: nowrap; */
  align-items: center;
  gap: 15px;
  width: 100%;
}

.date-selection {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: 10px;
}

.date-buttons {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  gap: 8px;
}

.picker {
  margin-left: 10px;
  width: 280px;
  height: auto;
  display: inline-flex;
  align-items: center;
}

.search-inputs-container {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  flex: 1;
  margin-left: 20px;
}

.search-input {
  flex: 1;
  min-width: 150px;
}

.search-field-item {
  display: flex;
  flex-direction: row;
  align-items: center;
  min-width: 150px;
  flex: 1;
  margin-right: 15px;
}

.field-label {
  margin-right: 8px;
  white-space: nowrap;
  color: var(--el-text-color-regular);
}

.search-button-container {
  display: flex;
  align-items: flex-end;
  margin-left: auto;
  gap: 10px;
}

.active-btn {
  color: var(--el-color-primary);
  border-color: var(--el-color-primary);
}

.margin10 {
  margin-left: 10px;
}

.search-row {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

@media screen and (max-width: 992px) {
  .search-row {
    flex-wrap: wrap;
  }
  
  .date-selection {
    width: auto;
    margin-right: 10px;
  }
  
  .date-buttons {
    margin-bottom: 10px;
  }
  
  .picker {
    margin-left: 0;
    width: 100%;
    max-width: 350px;
  }
  
  .search-inputs-container {
    flex: 1;
    min-width: 250px;
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
  
  .search-field-item {
    margin-right: 10px;
    margin-bottom: 10px;
    min-width: 140px;
  }
}

@media screen and (max-width: 768px) {
  .search-row {
    flex-direction: column;
    align-items: stretch;
  }
  
  .date-selection {
    width: 100%;
    margin-bottom: 10px;
    margin-right: 0;
  }
  
  .date-buttons {
    flex-wrap: wrap;
  }
  
  .picker {
    margin-left: 0;
    margin-top: 10px;
    width: 100%;
  }
  
  .search-inputs-container {
    width: 100%;
    margin-left: 0;
    margin-top: 15px;
    gap: 10px;
  }
  
  .search-input {
    width: 100%;
    min-width: 100%;
  }
  
  .search-field-item {
    width: 100% !important;
    flex-direction: row;
    align-items: center;
    margin-right: 0;
    margin-bottom: 15px;
    min-width: 100%;
  }
  
  .field-label {
    min-width: 70px;
  }
  
  .search-button-container {
    width: 100%;
    margin-left: 0;
    justify-content: flex-end;
  }
}
</style>