<!--
@description: TODO 选项卡组件
@author: liudingbang
@date: 2025/5/30 14:29
-->

<template>
  <div class="tabs-container">
    <div class="tabs">
        <span
            v-for="(item, index) in menus"
            :key="index"
            class="tabs-item"
            :class="activeIndex === index ? 'tabs-item-active' : ''"
            @click="change_tabs_handel(index)"
        >
          {{ item.name }}
        </span>
    </div>
    <div class="tabs-inner">
      <slot :active="activeIndex"/>
    </div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, watch} from "vue";

const props = withDefaults(defineProps<{
  menus: Array<{
    // 菜单项名称
    name: string
  }>,
  // 默认选中的菜单项
  active?: number
}>(), {
  menus: () => [],
  active: 0
})

onMounted(() => {
  activeIndex.value = props.active
})
watch(() => props.active, () => {
  activeIndex.value = props.active
})
const activeIndex = ref(0)
const change_tabs_handel = (index: number) => {
  activeIndex.value = index
}
</script>

<style scoped>
.tabs-container {
  width: 100%;
  height: 100%;

  .tabs-item {
    display: inline-block;
    margin: 0 10px 10px 10px;

    cursor: pointer;
    font-size: var(--font-size-level2);
    color: #86909c;
    height: 50px;
    line-height: 50px;
    border-bottom: 3px solid transparent;

    &:hover {
      color: #165dff;
    }
  }

  .tabs-item-active {
    border-bottom: 3px solid #165dff;
    color: #165dff;
    font-weight: 600;
  }

  .tabs-inner {
    padding: 0 8px;
  }
}

</style>
