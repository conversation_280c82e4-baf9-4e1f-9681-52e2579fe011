<!--
@description: TODO 左侧抽屉组件
@author: liudingbang
@date: 2025/5/30 14:01
-->

<template>
  <div class="left-panel" :class="show_left_panel ? '' : 'left-panel-hide'">
    <!-- 控制显示隐藏的按钮-->
    <div class="control" @click="change_panel_status">
      <img v-if="show_left_panel" src="@/assets/images/layout/close.png" alt=""/>
      <img v-else src="@/assets/images/layout/open.png" alt=""/>
    </div>
    <slot/>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref, watch} from "vue";

const show_left_panel = ref(true)
const props = defineProps({
  // 显示隐藏
  show: {
    type: Boolean,
    default: true
  }
})
onMounted(() => {
  show_left_panel.value = props.show
})
watch(() => props.show, (newValue) => {
  show_left_panel.value = newValue
})
const change_panel_status = () => {
  show_left_panel.value = !show_left_panel.value
}
</script>

<style scoped lang="scss">


.left-panel {
  position: absolute;
  width: 480px;
  height: calc(100vh - 90px);
  left: 10px;
  top: 10px;
  padding: 10px 8px 14px;
  border-radius: var(--radius-card);
  background: #ffffff;
  box-shadow: 0 10px 20px 0 rgba(0, 0, 0, 0.1);
  z-index: 999;
  transition: all 1s ease; /* 添加动画效果 */
}

.left-panel-hide {
  left: -480px;
}


.control {
  position: absolute;
  cursor: pointer;
  top: calc(50% - 42px);
  right: -15px;
  width: 16px;
  height: 84px;

  img {
    width: 16px;
    height: 84px;
  }
}
</style>
