<template>
  <div class="carousel-container">
    <!-- 轮播图容器 -->
    <div class="carousel-wrapper" :style="{ transform: `translateX(-${currentIndex * 100}%)` }">
      <div
        v-for="(item, index) in carouselList"
        :key="index"
        class="carousel-item"
      >
        <img :src="item.imageUrl" alt="carousel" class="carousel-img" />
      </div>
    </div>
    <!-- 左右切换按钮 -->
    <button
      class="carousel-btn prev-btn"
      @click="handlePrev"
    >
      &lt;
    </button>
    <button
      class="carousel-btn next-btn"
      @click="handleNext"
    >
      &gt;
    </button>
    <!-- 指示器 -->
    <div class="indicator-container">
      <span
        v-for="(item, index) in carouselList"
        :key="index"
        class="indicator"
        :class="{ active: index === currentIndex }"
        @click="currentIndex = index"
      ></span>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from 'vue';

// 轮播图数据，可替换成实际接口返回或本地资源
const carouselList = ref([
  { imageUrl: 'https://example.com/image1.jpg' }, // 示例图，替换成你实际的山水图地址
  { imageUrl: 'https://example.com/image2.jpg' },
  { imageUrl: 'https://example.com/image3.jpg' },
]);
const currentIndex = ref(0);

// 切换上一张
const handlePrev = () => {
  currentIndex.value = (currentIndex.value - 1 + carouselList.value.length) % carouselList.value.length;
};

// 切换下一张
const handleNext = () => {
  currentIndex.value = (currentIndex.value + 1) % carouselList.value.length;
};
</script>

<style scoped>
.carousel-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  margin: 0 auto;
}

.carousel-wrapper {
  display: flex;
  width: 100%;
  height: 100%;
  transition: transform 0.5s ease-in-out;
}

.carousel-item {
  flex: 0 0 100%;
  width: 100%;
  height: 100%;
}

.carousel-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.carousel-btn {
  position: absolute;
  top: 50%;
  transform: translateY(-50%);
  background: rgba(0, 0, 0, 0.3);
  color: #fff;
  border: none;
  padding: 10px 15px;
  cursor: pointer;
  font-size: 20px;
  z-index: 10;
}

.prev-btn {
  left: 10px;
}

.next-btn {
  right: 10px;
}

.indicator-container {
  position: absolute;
  bottom: 20px;
  left: 50%;
  transform: translateX(-50%);
  display: flex;
  gap: 8px;
}

.indicator {
  width: 40px;
  height: 5px;
  background: #ffffff;
  cursor: pointer;
}

.indicator.active {
  background: #1890ff;
}
</style>
