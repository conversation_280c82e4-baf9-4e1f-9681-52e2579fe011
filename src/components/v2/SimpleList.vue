<template>
    <div class="simple-list">
      <!-- 表头 -->
      <div v-if="showHeader && hasHeaders" class="list-header">
        <span v-for="(field, i) in fields" :key="i">{{ headers[field] || field }}</span>
      </div>
  
      <!-- 列表体 -->
      <div class="list-body" :style="{ maxHeight: scrollHeight }">
        <div
          v-for="(item, index) in pagedData"
          :key="itemKey ? item[itemKey] : index"
          class="list-row"
          :class="{ 'is-odd': index % 2 === 1 }"
          @click="handleRowClick(item)"
        >
          <span
            v-for="(field, i) in fields"
            :key="i"
          >
            <slot name="cell" :item="item" :field="field" :index="index">
              {{ item[field] }}
            </slot>
          </span>
        </div>
      </div>
  
      <!-- 分页 -->
      <div v-if="pagination" class="list-pagination">
        <el-pagination
          layout="prev, pager, next"
          :total="data.length"
          :page-size="pageSize"
          :current-page="currentPage"
          @current-change="handlePageChange"
          background
          small
        />
      </div>
    </div>
  </template>
  
  <script setup>
  import { computed, ref, defineProps, defineEmits } from 'vue'
  
  const props = defineProps({
    data: {
      type: Array,
      required: true
    },
    fields: {
      type: Array,
      required: true
    },
    itemKey: {
      type: String,
      default: ''
    },
    headers: {
      type: Object,
      default: () => ({}) // 显示字段别名，如 { plate: '车牌号' }
    },
    scrollHeight: {
      type: String,
      default: 'auto'
    },
    pagination: {
      type: Boolean,
      default: false
    },
    pageSize: {
      type: Number,
      default: 10
    },
    showHeader: {
      type: Boolean,
      default: true
    }
  })
  
  const emit = defineEmits(['row-click', 'page-change'])
  
  const currentPage = ref(1)
  
  const pagedData = computed(() => {
    if (!props.pagination) return props.data
    const start = (currentPage.value - 1) * props.pageSize
    return props.data.slice(start, start + props.pageSize)
  })
  
  const handlePageChange = (page) => {
    currentPage.value = page
    emit('page-change', page)
  }
  
  const handleRowClick = (item) => {
    emit('row-click', item)
  }
  </script>
  
  <style scoped>
  .simple-list {
    width: 100%;
    /* border-radius: 12px; */
    overflow: hidden;
    background: white;
    font-size: var(--font-size-level3);
  }
  
  .list-header,
  .list-row {
    display: flex;
    justify-content: space-between;
    padding: 12px 16px;
  }
  
  .list-header {
    background-color: #f0f0f0;
    font-weight: bold;
    border-bottom: 1px solid #e0e0e0;
  }
  
  .list-body {
    overflow-y: auto;
  }
  
  .list-row {
    background-color: #f9f9f9;
    cursor: pointer;
    transition: background 0.2s;
  }
  
  .list-row.is-odd {
    background-color: #eeeeee;
  }
  
  .list-row:hover {
    background-color: #e6f7ff;
  }
  
  .list-row span,
  .list-header span {
    flex: 1;
    text-align: center;
    word-break: break-all;
  }
  
  .list-pagination {
    margin-top: 10px;
    padding-right: 16px;
    text-align: right;
  }
  </style>
  