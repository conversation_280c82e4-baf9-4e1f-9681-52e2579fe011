<!--
@description: TODO 基础表格组件 核心功能（顶部数据查询、列表、分页） https://element.eleme.cn/#/zh-CN/component/table
@author: liudingbang
@date: 2025/6/3 14:38
-->

<template>
  <!--查询筛选控件-->
  <div class="pop-content" v-show="isHasSearch">
    <div class="search-bar" :style="actionFixedRight ? { justifyContent: 'space-between'} : {}">
      <div class="left-btn">
        <slot name="search"/>
      </div>
      <!--按钮操作控件-->
      <div class="right-btn">
        <el-button type="primary" v-show="isSearch" @click="handlerSearch">查询</el-button>
        <el-button v-show="isExport" @click="handlerExport">导出</el-button>
        <slot name="action"/>
      </div>
    </div>
  </div>
  <!--表格控件-->
  <div class="table-box">
    <slot name="table-top"/>
    <el-table
        :data="strategyList"
        stripe
        style="width: 100%; height: 100%"
        v-bind="props?.extraTableConfig"
    >
      <slot/>
    </el-table>
  </div>
  <!--分页控件-->
  <div class="page" v-show="isPage">
    <el-pagination
        size="small"
        background
        layout="total, sizes, prev, pager, next, jumper"
        :total="pageConfig.total"
        class="mt-4"
        v-bind="extraPageConfig"
        @size-change="handleSizeChange"
        @current-change="handleCurrentChange"
    />
  </div>
</template>

<script setup lang="ts">

import {computed, onMounted, reactive} from "vue";

const props = withDefaults(defineProps<{
  // 是否需要对查询进行操作
  isHasSearch: boolean
  // 是否显示查询按钮
  isSearch: boolean

  // 表格高度（表格不占抽屉全部高度时，允许自定义）
  tableHeight: string

  // 是否立即分页
  isImmediatelyPage: boolean
  // 是否需要分页
  isPage: boolean
  // 分页请求api
  api?: Function


  // 是否需要导出
  isExport: boolean
  // 导出请求api
  exportApi?: Function
  // 导出文件名称
  exportFileName?: string

  // 按钮是否固定在右边
  actionFixedRight?: boolean

  // 初始请求参数
  params?: object
  // 列表数据，如果只是想显示数据，不走内部自动分页，则直接设置data属性
  data?: Array<any>

  // 扩展表格配置
  extraTableConfig?: object
  // 扩展分页配置
  extraPageConfig?: object

}>(), {
  isImmediatelyPage: true,
  isHasSearch: true,
  isSearch: true,
  isPage: true,
  isExport: true,
  exportFileName: 'demo',
  tableHeight: 'calc(100vh - 14rem)'
})

// 事件列表
const emit = defineEmits<{
  // 分页之前-允许对请求参数进行修改
  (e: 'pageBefore', params: object): void
  // 分页大小改变-允许外部自主分页
  (e: 'sizeChange', params: { requestParams: object, pageConfig: object }): void
  // 分页页数改变-允许外部自主分页
  (e: 'currentChange', params: { requestParams: object, pageConfig: object }): void
  // 获取到列表之后-允许对列表数据进行二次处理
  (e: 'getListAfter', list: Array<any>): void
}>()

// 请求参数
let requestParams = reactive({
  // 大数据请求参数
  s: 0,
  n: 10,
  filter: '',
  // 内部业务系统请求参数
  pageNum: 1,
  pageSize: 10,
  actionFixedRight: false
})

// 表格数据
let tableList = reactive<Array<any>>([])

// 最终显示的列表数据
const strategyList = computed<Array<any>>(() => {
  if (props.data) {
    return props.data
  }
  return tableList
})

// 分页配置
let pageConfig = reactive({
  total: 0,
  pageNum: 1,
  pageSize: 10
})

onMounted(() => {
  if (props.params) {
    requestParams = {
      ...requestParams,
      ...props.params
    }
  }
  // 分页参数同步
  if (requestParams.pageNum) {
    pageConfig.pageNum = requestParams.pageNum
  }
  if (requestParams.pageSize) {
    pageConfig.pageSize = requestParams.pageSize
  }
  if (requestParams.s && requestParams.n) {
    pageConfig.pageSize = requestParams.n
    pageConfig.pageNum = requestParams.s / requestParams.n + 1
  }
  if (props.isImmediatelyPage) {
    pageLoadData()
  }
})

// 分页加载数据
async function pageLoadData() {
  emit('pageBefore', requestParams)
  if (!props.data) {
    props.api(requestParams).then((res: any) => {
      // 列表数据处理
      if (Array.isArray(res)) {
        tableList.splice(0, tableList.length, ...res)
      } else {
        const list = res?.data?.data?.list ?? res?.data?.list ?? res?.list ?? [];
        tableList.splice(0, tableList.length, ...(Array.isArray(list) ? list : []));
      }
      // 总数更新
      pageConfig.total = res?.data?.data?.total ?? res?.data?.total ?? res?.total ?? 0
      // console.log(tableList)
      emit('getListAfter', tableList)
    })
  }
}

/**
 *  分页大小改变
 * @param val 改变后的页大小
 */
function handleSizeChange(val: number) {
  // console.log(`每页 ${val} 条`);
  requestParams.pageSize = pageConfig.pageSize = val
  requestParams.n = val
  pageLoadData()
  emit('sizeChange', {requestParams, pageConfig})
}

/**
 *  分页当前页改变
 * @param val 当前页
 */
function handleCurrentChange(val: number) {
  // console.log(`当前页: ${val}`);
  requestParams.pageNum = pageConfig.pageNum = val
  requestParams.s = (pageConfig.pageNum - 1) * pageConfig.pageSize
  pageLoadData()
  emit('currentChange', {requestParams, pageConfig})
}

/**
 *  查询按钮点击事件
 */
function handlerSearch() {
  pageLoadData()
}

/**
 *  导出按钮点击事件
 */
async function handlerExport() {
  props.exportApi!(requestParams).then((res: any) => {
    try {
      if (res instanceof Blob) {
        const blob = new Blob([res], {type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'});
        const link = document.createElement('a');
        link.href = URL.createObjectURL(blob);
        link.download = props.exportFileName + '.xlsx';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
      } else if (typeof res === 'string') {
        // 文件地址,直接下载
        window.open(res, '_blank');
      } else {
        console.warn('未知类型的响应数据：', res);
      }
    } catch (error) {
      console.error('导出失败：', error);
    }
  })
}


// 暴露接口
defineExpose({
  tableList,
  pageLoadData
})
</script>

<style scoped>
.pop-content {
  position: relative;
  .search-bar {
    display: flex;
    flex-wrap: wrap;
    gap: 10px;
  }

  .left-btn {
    display: flex;
    flex-wrap: wrap;

    .el-button {
      margin: 0;
      border-radius: 0;
    }

    .margin10 {
      margin-right: 10px;
    }
  }
  .right-btn {
    margin-left: 10px;
  }
}

.table-box {
  width: 100%;
  height: v-bind(tableHeight);
  overflow: hidden;
}

.page {
  display: flex;
  justify-content: end;
  align-items: center;
  height: 60px;
}
</style>
