import * as echarts from 'echarts'

/**
 * @name 基础柱状图配置
 * @returns
 */
export const baseBarConfig = (): any => ({
  tooltip: {
    show: true,
    trigger: 'axis',
    backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
    textStyle: {
      color: '#fff', // 字体颜色
      fontSize: 12
    },
    borderColor: '#ccc', // 边框颜色
    borderWidth: 1, // 边框宽度
    padding: 10, // 内边距
    axisPointer: {
      type: 'none' // 设置为 'none' 来隐藏辅助线
    },
    formatter: function (params: any) {
      let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
      for (let i = 0; i < params.length; i++) {
        result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
      }
      return result
    }
  },
  grid: {
    left: '2%',
    right: '10%',
    bottom: '2%',
    top: '0',
    containLabel: true
  },
  xAxis: {
    axisLine: {
      show: false,
      lineStyle: {
        color: '#A2B0C7' // y轴线颜色
      }
    },
    nameTextStyle: {
      color: '#D2D4D9',
      align: 'center'
    },
    type: 'value',
    axisLabel: {
      show: false,
      textStyle: {
        color: '#FFFFFF' // y轴标签文字颜色
      }
    },
    splitLine: {
      show: false,
      lineStyle: {
        type: 'dashed',
        color: '#293357'
      }
    }
  },
  yAxis: {
    type: 'category',
    data: [],
    inverse: true,
    axisLabel: {
      textStyle: {
        color: '#FFFFFF'
      }
    },
    nameTextStyle: {
      color: '#D2D4D9',
      align: 'center'
    },
    axisTick: {
      show: false
    },
    axisLine: {
      show: false
    }
    // splitLine: {
    //     show: true,
    //     lineStyle: {
    //         type: 'dashed',
    //         color: '#293357'
    //     }
    // },
  },
  series: [
    {
      name: '值',
      type: 'bar',
      data: [],
      barWidth: 10,
      showBackground: true,
      backgroundStyle: { color: '#1E2543' },
      label: {
        normal: {
          show: true,
          position: 'right',
          formatter: '{c}',
          textStyle: {
            color: 'white'
          }
        }
      },
      itemStyle: {
        normal: {
          show: true,
          color: new echarts.graphic.LinearGradient(
            0,
            0,
            1,
            0,
            [
              {
                offset: 0,
                color: '#02419A' // 0% 处的颜色
              },
              {
                offset: 1,
                color: '#46F9FF' // 100% 处的颜色
              }
            ],
            false
          )
          // color: (params: any) => {
          //     const colorArray = [
          //         { top: '#ffa800', bottom: 'rgba(11,42,84,.3)' },
          //         { top: '#1ace4a', bottom: 'rgba(11,42,84, 0.3)' },
          //         { top: '#4bf3ff', bottom: 'rgba(11,42,84,.3)' },
          //         { top: '#4f9aff', bottom: 'rgba(11,42,84,.3)' },
          //         { top: '#b250ff', bottom: 'rgba(11,42,84,.3)' }
          //     ]
          //     const num = colorArray.length
          //     return {
          //         type: 'linear',
          //         colorStops: [
          //             { offset: 0, color: colorArray[params.dataIndex % num].bottom },
          //             { offset: 1, color: colorArray[params.dataIndex % num].top }
          //         ]
          //     }
          // },
          // barBorderRadius: 70,
          // borderWidth: 0,
          // borderColor: '#333'
        }
      }
    }
  ]
})

/**
 * @name
 * @description 竖向柱状图
 */
export const verticalBarChartConfig: any = () => {
  return {
    grid: {
      left: '1%',
      right: '2%',
      bottom: '5%',
      top: '40%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      axisPointer: {
        type: 'none' //隐藏辅助线
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      formatter: function (params) {
        if (!Array.isArray(params) || params.length === 0) {
          return ''
        }
        // 用 Map 记录已添加的 seriesName，防止重复
        const seenSeries = new Map()
        return (
          `${params[0].axisValue}<br/>` +
          params
            .filter((p) => p.seriesName !== undefined && p.data !== undefined)
            .map((p) => {
              if (!seenSeries.has(p.seriesName) && p.seriesName != '') {
                seenSeries.set(p.seriesName, true)
                return `${p.seriesName}：${p.data}`
              }
              return '' // 避免重复
            })
            .filter(Boolean) // 过滤掉空字符串
            .join('<br/>')
        ) // 直接拼接，无需手动去掉 `<br/>`
      }
    },
    xAxis: {
      type: 'category',
      //data: ['优', '良', '中', '次', '差'],
      data: [],
      axisLabel: {
        textStyle: {
          color: '#606266' // x轴标签文字颜色
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#274771' // x轴线颜色
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    yAxis: {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9',
        align: 'center'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#606266' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#D4D7DE'
        }
      }
    },
    series: [
      {
        name: '',
        stack: '1',
        type: 'bar',
        data: [],
        barWidth: 20, // 设置柱状图宽度
        showBackground: false,
        label: {
          show: false,
          position: 'top', // 在柱顶显示
          formatter: '{c}', // 显示数字
          textStyle: {
            color: 'white' // 标签文字颜色
          }
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#67C23A' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#67C23A' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      },
      
    ]
  }
}

/**
 * @name
 * @description 竖向柱状折线图
 */
export const barLineChartConfig: any = () => {
  return {
    grid: {
      left: '1%',
      right: '2%',
      bottom: '5%',
      top: '40%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      textStyle: {
        fontSize: 12
      },
      axisPointer: {
        type: 'none' //隐藏辅助线
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
    },
    legend: {
        data: [],
        itemWidth: 8, // 图例标记的图形宽度
        itemHeight: 8, // 图例标记的图形高度
        right: 20, //调整图例位置
        icon: 'rect', //图例前面的图标形状
        textStyle: {
            //图例文字的样式
            fontSize: 12, //图例文字大小
        },
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        textStyle: {
          color: '#606266' // x轴标签文字颜色
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#274771' // x轴线颜色
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    yAxis: [{
      name: '',
      nameTextStyle: {
        color: '#D2D4D9',
        align: 'center'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#606266' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#D4D7DE'
        }
      }
    },
    {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9',
        align: 'center'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#606266' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#D4D7DE'
        }
      }
    }],
    series: [
      {
        name: 'a',
        stack: '1',
        yAxisIndex: 0,
        type: 'bar',
        data: [],
        barWidth: 20, // 设置柱状图宽度
        showBackground: false,
        label: {
          show: false,
          position: 'top', // 在柱顶显示
          formatter: '{c}', // 显示数字
          textStyle: {
            color: 'white' // 标签文字颜色
          }
        },
        itemStyle: {
          color: '#409EFF'
        }
      },
      {
        name: 'b',
        stack: '2',
        yAxisIndex: 1,
        data: [],
        type: 'line',
        showSymbol: false,
        symbol: 'none',
        smooth: false,
        lineStyle: {
          color: '#F56C6C',
          width: 1.2
        },
        itemStyle: {
          color: '#F56C6C'
        }
      },
      
    ]
  }
}

/**
 * 3D柱状图
 */
export const vertical3DBarChartConfig: any = () => {
  const offsetX = 8
  const offsetY = 4
  // 绘制左侧面
  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx, shape) {
      // 会canvas的应该都能看得懂，shape是从custom传入的
      const xAxisPoint = shape.xAxisPoint
      const c0 = [shape.x, shape.y]
      const c1 = [shape.x - offsetX, shape.y - offsetY]
      const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY]
      const c3 = [xAxisPoint[0], xAxisPoint[1]]
      ctx
        .moveTo(c0[0], c0[1])
        .lineTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .closePath()
    }
  })

  // 绘制右侧面
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint
      const c1 = [shape.x, shape.y]
      const c2 = [xAxisPoint[0], xAxisPoint[1]]
      const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY]
      const c4 = [shape.x + offsetX, shape.y - offsetY]
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath()
    }
  })

  // 绘制顶面
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y]
      const c2 = [shape.x + offsetX, shape.y - offsetY] //右点
      const c3 = [shape.x, shape.y - offsetX]
      const c4 = [shape.x - offsetX, shape.y - offsetY]
      ctx
        .moveTo(c1[0], c1[1])
        .lineTo(c2[0], c2[1])
        .lineTo(c3[0], c3[1])
        .lineTo(c4[0], c4[1])
        .closePath()
    }
  })
  // 注册三个面图形
  echarts.graphic.registerShape('CubeLeft', CubeLeft)
  echarts.graphic.registerShape('CubeRight', CubeRight)
  echarts.graphic.registerShape('CubeTop', CubeTop)
  return {
    grid: {
      left: '2%',
      right: '4%',
      bottom: '5%',
      top: '25%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      axisPointer: {
        type: 'none' // 设置为 'none' 来隐藏辅助线
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
        for (let i = 0; i < params.length; i++) {
          result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
        }
        return result
      }
    },
    legend: {
      data: [], // 图例项的名称
      right: '40%',
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // x轴标签文字颜色
        }
        // padding: [0, 0, 0, -20]
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#A2B0C7' // x轴线颜色
        }
      },
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9'
      },
      min: 0,
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
      splitNumber: 2
    },
    series: [
      {
        type: 'custom',
        name: '到站次数',
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)])
          const xAxisPoint = api.coord([api.value(0), 0])
          const distance = 20
          return {
            type: 'group',
            children: [
              {
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] - distance,
                  y: location[1],
                  xAxisPoint: [xAxisPoint[0] - distance, xAxisPoint[1]]
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#E1D31A'
                    },
                    {
                      offset: 1,
                      color: '#E0882F'
                    }
                    // {
                    //   offset: 1,
                    //   color: 'transparent'
                    // }
                  ])
                }
              },
              {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] - distance,
                  y: location[1],
                  xAxisPoint: [xAxisPoint[0] - distance, xAxisPoint[1]]
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#E1D31A'
                    },
                    {
                      offset: 1,
                      color: '#E0882F'
                    }
                    // {
                    //   offset: 1,
                    //   color: 'transparent'
                    // }
                  ])
                }
              },
              {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] - distance,
                  y: location[1],
                  xAxisPoint: [xAxisPoint[0] - distance, xAxisPoint[1]]
                },
                style: {
                  fill: '#E1D31A'
                }
              }
            ]
          }
        },
        data: []
      },
      {
        type: 'custom',
        name: '发送次数',
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1)])
          return {
            type: 'group',
            children: [
              {
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#01A8BB'
                    },
                    {
                      offset: 1,
                      color: '#02409A'
                    }
                    // ,
                    // {
                    //   offset: 1,
                    //   color: 'transparent'
                    // }
                  ])
                }
              },
              {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#01A8BB'
                    },
                    {
                      offset: 1,
                      color: '#02409A'
                    }
                  ])
                }
              },
              {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0],
                  y: location[1],
                  xAxisPoint: api.coord([api.value(0), 0])
                },
                style: {
                  fill: '#01A8BB'
                }
              }
            ]
          }
        },
        data: []
      },
      {
        type: 'bar',
        name: '发送次数',
        itemStyle: {
          color: 'transparent'
        },
        data: []
      }
    ]
  }
}

/**
 * 3D柱状图
 */
export const singleVertical3DBarChartConfig: any = () => {
  const offsetX = 8
  const offsetY = 4
  // 绘制左侧面
  const CubeLeft = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx, shape) {
      // 会canvas的应该都能看得懂，shape是从custom传入的
      const xAxisPoint = shape.xAxisPoint
      const c0 = [shape.x, shape.y]
      const c1 = [shape.x - offsetX, shape.y - offsetY]
      const c2 = [xAxisPoint[0] - offsetX, xAxisPoint[1] - offsetY]
      const c3 = [xAxisPoint[0], xAxisPoint[1]]
      ctx.moveTo(c0[0], c0[1])
      ctx.lineTo(c1[0], c1[1])
      ctx.lineTo(c2[0], c2[1])
      ctx.lineTo(c3[0], c3[1])
      ctx.closePath()
    }
  })

  // 绘制右侧面
  const CubeRight = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx, shape) {
      const xAxisPoint = shape.xAxisPoint
      const c1 = [shape.x, shape.y]
      const c2 = [xAxisPoint[0], xAxisPoint[1]]
      const c3 = [xAxisPoint[0] + offsetX, xAxisPoint[1] - offsetY]
      const c4 = [shape.x + offsetX, shape.y - offsetY]
      ctx.moveTo(c1[0], c1[1])
      ctx.lineTo(c2[0], c2[1])
      ctx.lineTo(c3[0], c3[1])
      ctx.lineTo(c4[0], c4[1])
      ctx.closePath()
    }
  })

  // 绘制顶面
  const CubeTop = echarts.graphic.extendShape({
    shape: {
      x: 0,
      y: 0
    },
    buildPath: function (ctx, shape) {
      const c1 = [shape.x, shape.y]
      const c2 = [shape.x + offsetX, shape.y - offsetY] //右点
      const c3 = [shape.x, shape.y - offsetX]
      const c4 = [shape.x - offsetX, shape.y - offsetY]
      ctx.moveTo(c1[0], c1[1])
      ctx.lineTo(c2[0], c2[1])
      ctx.lineTo(c3[0], c3[1])
      ctx.lineTo(c4[0], c4[1])
      ctx.closePath()
    }
  })
  // 注册三个面图形
  echarts.graphic.registerShape('CubeLeft', CubeLeft)
  echarts.graphic.registerShape('CubeRight', CubeRight)
  echarts.graphic.registerShape('CubeTop', CubeTop)
  return {
    grid: {
      left: '2%',
      right: '4%',
      bottom: '5%',
      top: '20%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      axisPointer: {
        type: 'none'
      },
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
        for (let i = 0; i < params.length; i++) {
          result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
        }
        return result
      }
    },
    legend: {
      // data: ['到站次数', '发车次数'], // 图例项的名称
      right: '40%',
      // top: 1,
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // x轴标签文字颜色
        },
        padding: [0, 0, 0, 0]
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#A2B0C7' // x轴线颜色
        }
      },
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
      axisTick: {
        show: false
      }
    },
    yAxis: {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
      splitNumber: 2
    },
    series: [
      {
        type: 'custom',
        name: '到站次数',
        renderItem: (params, api) => {
          const location = api.coord([api.value(0), api.value(1) > 0 ? api.value(1) * 0.9 : 0])
          const xAxisPoint = api.coord([api.value(0), 0])
          const distance = 0
          return {
            type: 'group',
            children: [
              {
                type: 'CubeLeft',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] - distance,
                  y: location[1],
                  xAxisPoint: [xAxisPoint[0] - distance, xAxisPoint[1]]
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#48D6EF'
                    },
                    {
                      offset: 0.5,
                      color: '#3178E9'
                    },
                    {
                      offset: 1,
                      color: '#1067f1'
                    }
                  ])
                }
              },
              {
                type: 'CubeRight',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] - distance,
                  y: location[1],
                  xAxisPoint: [xAxisPoint[0] - distance, xAxisPoint[1]]
                },
                style: {
                  fill: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    {
                      offset: 0,
                      color: '#48D6EF'
                    },
                    {
                      offset: 0.5,
                      color: '#3178E9'
                    },
                    {
                      offset: 1,
                      color: '#1067f1'
                    }
                  ])
                }
              },
              {
                type: 'CubeTop',
                shape: {
                  api,
                  xValue: api.value(0),
                  yValue: api.value(1),
                  x: location[0] - distance,
                  y: location[1],
                  xAxisPoint: [xAxisPoint[0] - distance, xAxisPoint[1]]
                },
                style: {
                  fill: '#48D6EF'
                }
              }
            ]
          }
        },
        data: []
      }
    ]
  }
}

/**
 * 象形柱状图
 */
export const pictorialBarChartConfig: any = () => {
  return {
    grid: {
      left: '12%',
      top: '5%',
      bottom: '12%',
      right: '8%'
    },
    xAxis: {
      data: [],
      axisTick: {
        show: false
      },
      axisLine: {
        lineStyle: {
          color: '#A2B0C7',
          width: 1 //这里是为了突出显示加上的
        }
      },
      axisLabel: {
        textStyle: {
          color: '#FFFFFF',
          fontSize: 12
        }
      }
    },
    yAxis: {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    series: [
      {
        type: 'pictorialBar',
        barCategoryGap: '0%',
        symbol: 'path://M0,10 L10,10 C5.5,10 5.5,5 5,0 C4.5,5 4.5,10 0,10 z',
        label: {
          show: true,
          position: 'top',
          distance: 15,
          color: '#159AFF',
          fontWeight: 'bolder',
          fontSize: 15
        },
        itemStyle: {
          normal: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: '#003AFF'
                },
                {
                  offset: 1,
                  color: '#1493F9'
                }
              ],
              global: false //  缺省为  false
            }
          },
          emphasis: {
            opacity: 1
          }
        },
        data: []
      }
    ]
  }
}

/**
 * 象形柱状图
 */
export const pictorialBarChartConfig2: any = () => {
  return {
    tooltip: {
      trigger: 'axis',
      confine: true,
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      appendToBody: true, // 将 tooltip 渲染到 body 元素中
      axisPointer: {
        type: 'none' //隐藏辅助线
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '0%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      triggerEvent: true,
      axisTick: {
        show: false
      },
      axisLine: {
        show: true
      },
      axisLabel: {
        color: '#D8F0FF'
      }
    },
    legend: {
      data: [], // 图例项的名称
      right: 1,
      top: 1,
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    yAxis: {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    series: [
      {
        name: '',
        type: 'pictorialBar',
        symbolOffset: ['-15%', 0],
        barWidth: '45%',
        stack: '总量',
        label: {
          normal: {
            show: false
          }
        },
        itemStyle: {
          normal: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(0, 151, 251, 1)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(0, 34, 66, 0.2)' // 100% 处的颜色
                }
              ],
              globalCoord: false // 缺省为 false
            } //渐变颜色
          }
        },
        symbol:
          'path://M12.000,-0.000 C12.000,-0.000 16.074,60.121 22.731,60.121 C26.173,60.121 -3.234,60.121 0.511,60.121 C7.072,60.121 12.000,-0.000 12.000,-0.000 Z',
        data: []
      },
      {
        name: '',
        type: 'pictorialBar',
        symbolOffset: ['15%', 0],
        barWidth: '45%',
        stack: '总量1',
        label: {
          normal: {
            show: false
          }
        },
        itemStyle: {
          normal: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(48, 236, 166, 1)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(0, 34, 66, 0.2)' // 100% 处的颜色
                }
              ],
              globalCoord: false // 缺省为 false
            } //渐变颜色
          }
        },
        symbol:
          'path://M12.000,-0.000 C12.000,-0.000 16.074,60.121 22.731,60.121 C26.173,60.121 -3.234,60.121 0.511,60.121 C7.072,60.121 12.000,-0.000 12.000,-0.000 Z',

        data: []
      },
      {
        name: '环比',
        type: 'line',
        data: [],
        yAxisIndex: 0
      }
    ]
  }
}

/**
 * @name
 * @description 分格柱状图
 */
export const gridBarChartConfig: any = () => {
  return {
    grid: {
      left: '1%',
      right: '2%',
      bottom: '5%',
      top: '40%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      axisPointer: {
        type: 'none' //隐藏辅助线
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesType === 'bar') {
            result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
          }
        }
        return result
      }
    },
    xAxis: {
      type: 'category',
      data: [],
      triggerEvent: true,
      axisTick: {
        show: false
      },
      axisLine: {
        show: true
      },
      axisLabel: {
        color: '#D8F0FF'
      }
    },
    yAxis: {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    series: [
      {
        name: '',
        stack: '1',
        type: 'bar',
        //data: [120, 200, 150, 80, 70], // 示例数据
        data: [],
        barWidth: 20, // 设置柱状图宽度
        showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        label: {
          show: false,
          position: 'top', // 在柱顶显示
          formatter: '{c}', // 显示数字
          textStyle: {
            color: 'white' // 标签文字颜色
          }
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#1978E5' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#1254A9' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      },
      {
        // 分隔
        type: 'pictorialBar',
        itemStyle: {
          normal: {
            color: '#060D31'
          }
        },
        symbolRepeat: 'fixed',
        symbolMargin: 4,
        symbol: 'rect',
        symbolClip: true,
        symbolSize: [20, 2],
        symbolPosition: 'start',
        symbolOffset: [0, -6],
        data: [],
        width: 20,
        z: 0,
        zlevel: 1
      }
    ]
  }
}

/** 多Y轴对比显示 */
export const multipleYBarConfig: any = () => {
  return {
    grid: {
      left: 24,
      right: 30,
      bottom: '8%',
      top: '25%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      formatter: function (params: any) {
        console.log('-------tooltip.formatter--------', params)
        let result = `${params[0].seriesName}：${params[0].data}<br/>` // 显示 x 轴的值
        result += `${params[1].seriesName}：${params[1].data}%`
        return result
      }
    },
    legend: {
      data: ['评价数', '投诉数'], // 图例项的名称
      right: 80,
      top: '2%',
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    xAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // x轴标签文字颜色
        }
      },
      axisTick: {
        show: false
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#274771' // x轴线颜色
        }
      },
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    yAxis: [
      {
        type: 'value',
        name: '',
        min: 0,
        nameTextStyle: {
          color: '#D2D4D9',
          align: 'right'
        },
        axisLabel: {
          textStyle: {
            color: '#FFFFFF' // y轴标签文字颜色
          }
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2B0C7' // y轴线颜色
          }
        },
        splitLine: {
          show: true,
          lineStyle: {
            type: 'dashed',
            color: '#293357'
          }
        },
        splitNumber: 2,
        nameGap: 14
      },
      {
        type: 'value',
        name: '单位/%',
        nameTextStyle: {
          color: '#D2D4D9',
          align: 'center'
        },
        axisLabel: {
          textStyle: {
            color: '#FFFFFF' // y轴标签文字颜色
          }
        },
        axisLine: {
          show: false,
          lineStyle: {
            color: '#A2B0C7' // y轴线颜色
          }
        },
        splitLine: {
          show: false,
          lineStyle: {
            type: 'dashed',
            color: '#293357'
          }
        },
        splitNumber: 2,
        nameGap: 14
      }
    ],
    series: [
      {
        name: '评价数',
        type: 'bar',
        barWidth: 10, // 设置柱状图宽度
        showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        label: {
          show: false,
          position: 'top', // 在柱顶显示
          formatter: '{c}', // 显示数字
          textStyle: {
            color: 'white' // 标签文字颜色
          }
        },
        itemStyle: {
          normal: {
            show: true,
            barBorderRadius: [10, 10, 0, 0],
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#83ABFF' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#4682FF' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      },
      {
        name: '投诉数',
        type: 'bar',
        barWidth: 20, // 设置柱状图宽度
        showBackground: true,
        //backgroundStyle: { color: 'red' },
        itemStyle: {
          normal: {
            show: true,
            barBorderRadius: [10, 10, 0, 0],
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: '#66E1DF' // 0% 处的颜色
                },
                {
                  offset: 0.5,
                  color: '#64DEDC ' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'rgba(102,225,223,0)' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      }
    ]
  }
}

/**
 * @name
 * @description X轴多柱子
 */
export const multiBarChartConfig: any = () => {
  return {
    grid: {
      left: '1%',
      right: '2%',
      bottom: '5%',
      top: '40%',
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      axisPointer: {
        type: 'none' //隐藏辅助线
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      formatter: function (params) {
        if (!Array.isArray(params) || params.length === 0) {
          return ''
        }
        // 用 Map 记录已添加的 seriesName，防止重复
        const seenSeries = new Map()
        return (
          `${params[0].axisValue}<br/>` +
          params
            .filter((p) => p.seriesName !== undefined && p.data !== undefined)
            .map((p) => {
              if (!seenSeries.has(p.seriesName) && p.seriesName != '') {
                seenSeries.set(p.seriesName, true)
                return `${p.seriesName}：${p.data}`
              }
              return '' // 避免重复
            })
            .filter(Boolean) // 过滤掉空字符串
            .join('<br/>')
        ) // 直接拼接，无需手动去掉 `<br/>`
      }
    },
    legend: {
      data: [], // 图例项的名称
      right: 180,
      top: '2%',
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    xAxis: {
      type: 'category',
      //data: ['优', '良', '中', '次', '差'],
      data: [],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // x轴标签文字颜色
        }
      },
      axisLine: {
        show: true,
        lineStyle: {
          color: '#274771' // x轴线颜色
        }
      },
      axisTick: {
        show: false
      },
      splitLine: {
        show: false,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    yAxis: {
      name: '',
      nameTextStyle: {
        color: '#D2D4D9',
        align: 'center'
      },
      type: 'value',
      axisLabel: {
        textStyle: {
          color: '#FFFFFF' // y轴标签文字颜色
        }
      },
      axisLine: {
        show: false,
        lineStyle: {
          color: '#A2B0C7' // y轴线颜色
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      }
    },
    series: [
      {
        name: '',
        // stack: '1',
        type: 'bar',
        data: [],
        barWidth: 20, // 设置柱状图宽度
        // showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        label: {
          show: false,
          position: 'top', // 在柱顶显示
          formatter: '{c}', // 显示数字
          textStyle: {
            color: 'white' // 标签文字颜色
          }
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,58,255,0.3)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#159AFF' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      },
      {
        name: '',
        // stack: '1',
        type: 'bar',
        data: [],
        barWidth: 20, // 设置柱状图宽度
        // showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        label: {
          show: false,
          position: 'top', // 在柱顶显示
          formatter: '{c}', // 显示数字
          textStyle: {
            color: 'white' // 标签文字颜色
          }
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(35, 226, 38, 0)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'green' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      }
      
    ]
  }
}


/**
 * @name
 * @description 竖向柱状图
 */
export const baseBarYChartConfig: any = () => {
  return {
    grid: {
      left: '1%',
      right: '5%',
      bottom: '5%',
      top: '4%',
      containLabel: true
    },
    tooltip: {
      trigger: 'axis',
      confine: true,
      axisPointer: {
        type: 'line'
      },
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      appendToBody: true // 将 tooltip 渲染到 body 元素中
    },
    xAxis: {
      type: 'value',
      axisLine: {
        show: true,
        lineStyle: {
          color: '#FFFFFF' // y轴线颜色
        }
      },
      boundaryGap: [0, 0.01],
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF'
        }
      },
      nameTextStyle: {
        color: '#D2D4D9',
        align: 'center'
      },
      axisTick: {
        show: false
      },
    },
    series: [
      {
        name: '2011',
        type: 'bar',
        barWidth: 14, // 设置柱状图宽度
        showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        label: {
          show: true,
          formatter: '{c}万元', // 显示数字
          textStyle: {
            color: 'white' // 标签文字颜色
          }
        },
        data: [],
        itemStyle: {
          normal: {
            show: true,
            barBorderRadius: [0, 0, 0, 0],
            color: new echarts.graphic.LinearGradient(1, 1, 0, 1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,58,255,0)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#159AFF' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      }
    ]
  }
}

/**
 * @name
 * @description 正负柱状图
 */
export const plusMinusBarChartConfig: any = () => {
  return {
    grid: {
      left: '1%',
      right: '4%',
      bottom: '5%',
      top: '4%',
      containLabel: true
    },
    tooltip: {
    },
    xAxis: {
      type: 'value',
      axisLabel: {
        show: true,
        lineStyle: {
          color: '#FFFFFF' // y轴线颜色
        },
        formatter: (value) => {
          if (value < 0) {
            return -value;
          } else {
            return value;
          }
        }
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
    },
    yAxis: {
      type: 'category',
      data: [],
      axisLabel: {
        textStyle: {
          color: '#FFFFFF'
        }
      },
      nameTextStyle: {
        color: '#D2D4D9',
        align: 'center'
      },
      axisTick: {
        show: false
      },
    },
    series: [
      {
        name: 'Income',
        type: 'bar',
        stack: 'Total',
        label: {
          show: false
        },
        emphasis: {
          focus: 'series'
        },
        data: [],
        barWidth: 14,
        showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        itemStyle: {
          normal: {
            show: true,
            barBorderRadius: [0, 0, 0, 0],
            color: new echarts.graphic.LinearGradient(1, 1, 0, 1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,58,255,0)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#159AFF' // 100% 处的颜色
                }
              ],
              false
            )
          }
        }
      },
      {
        name: 'Expenses',
        type: 'bar',
        stack: 'Total',
        label: {
          show: false
        },
        emphasis: {
          focus: 'series'
        },
        barWidth: 14,
        showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        itemStyle: {
          normal: {
            show: true,
            barBorderRadius: [0, 0, 0, 0],
            color: new echarts.graphic.LinearGradient(0, 1, 1, 1,
              [
                {
                  offset: 0,
                  color: 'rgba(35, 226, 38, 0)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: 'green' // 100% 处的颜色
                }
              ],
              false
            )
          }
        },
        data: []
      }
    ]
  }
}
