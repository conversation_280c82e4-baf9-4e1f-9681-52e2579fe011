import type { Right } from '@element-plus/icons-vue'
import type { EChartsOption } from 'echarts'
import * as echarts from 'echarts'

/**
 * @name 基础饼图配置
 * @returns
 */
export const basePieConfig00 = (): any => {
  const baseConfig = {
    color: ['#4AD3FF', '#165DFF', '#21D04B', '#FCDC43', '#f9c956'],
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: '12'
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      appendToBody: true, // 将 tooltip 渲染到 body 元素中
      //formatter: '{a} {b}: {c} ({d}%)'
      formatter: function (params: any) {
        if (params.seriesIndex === 0) {
          return params.name + ': ' + params.value
        }
      }
    },
    grid: {
      left: '26px',
      right: '20px',
      bottom: '36px',
      top: '10%',
      containLabel: true
    },
    legend: {
      top: 'bottom',
      textStyle: {
        color: '#4E5969'
      },
    },
    label: {
      show: true,
      // formatter: '{d}% \n {b}',
      formatter: '{a|{b}} \n {b|{d}%}',
        rich: {
          a: {
            color: '#4E5969',
            fontSize: 12,
            align: 'center',
          },
          b: {
            color: '#4E5969',
            fontSize: 12,
            align: 'center',
            padding: 2
          },
        }
    },
    labelLayout: function (params) {
      const isLeft = params.labelRect.x < 150;
      const points = params.labelLinePoints;
      // Update the end point.
      points[2][0] = isLeft
        ? params.labelRect.x
        : params.labelRect.x + params.labelRect.width;
      return {
        labelLinePoints: points
      };
    },
    series: [
    {
      type: 'pie',
      center: ['50%', '46%'],
      radius: ['40%', '66%'],
      itemStyle: {
        borderColor: '#fff',
        borderWidth: 1
      },
      data: [
        { value: 1048, name: '腐蚀性' },
        { value: 735, name: '放射性' },
        { value: 580, name: '易燃易爆' },
        { value: 484, name: '其他类型' },
      ],
    }
    ]
  }

  return baseConfig
}

/**
 * @name 基础饼图配置
 * @returns
 */
export const basePieConfig = (): EChartsOption => {
  return {
    color: ['#2056DD', '#1AD693', '#FF4949', '#FF5D05', '#FFBF00', '#FD2F98'],
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      appendToBody: true, // 将 tooltip 渲染到 body 元素中
      //formatter: '{a} {b}: {c} ({d}%)'
      formatter: '{a} {b}: {c}'
    },
    title: {
      show: false,
      text: '总数',
      subtext: 7789,
      textStyle: {
        color: '#f2f2f2',
        fontSize: 40
        // align: 'center'
      },
      subtextStyle: {
        fontSize: 30,
        color: ['#ff9d19']
      },
      x: 'center',
      y: 'center'
    },
    grid: {
      bottom: 150,
      left: 100,
      right: '10%'
    },
    legend: {
      show: false,
      orient: 'vertical',
      top: 'middle',
      right: '5%',
      textStyle: {
        color: '#f2f2f2',
        fontSize: 25
      },
      icon: 'roundRect',
      data: []
    },
    series: [
      // 主要展示层的
      {
        radius: ['30%', '61%'],
        center: ['50%', '50%'],
        type: 'pie',
        label: {
          normal: {
            show: true,
            formatter: function (params: { name: any; percent: number }) {
              // 使用换行符 \n 来分隔标题和百分比
              return `${params.name}\n${params.percent.toFixed(0)}%` // params.name 显示标题，params.percent 显示百分比
            },
            textStyle: {
              fontSize: '100%',
              color: '#fff' // 设置字体颜色（普通状态下的颜色）
            },
            position: 'outside'
          },
          emphasis: {
            show: true
          }
        },
        labelLine: {
          normal: {
            show: true,
            length: '5%',
            length2: '8%'
          },
          emphasis: {
            show: true
          }
        },
        name: '',
        data: []
      },
      // 边框的设置
      {
        radius: ['24%', '28%'],
        center: ['50%', '50%'],
        type: 'pie',
        label: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        labelLine: {
          normal: {
            show: false
          },
          emphasis: {
            show: false
          }
        },
        animation: false,
        tooltip: {
          show: false
        },
        data: [
          {
            value: 1,
            itemStyle: {
              color: 'rgba(250,250,250,0.3)'
            }
          }
        ]
      },
      {
        name: '',
        type: 'pie',
        clockWise: false, //顺时加载
        hoverAnimation: false, //鼠标移入变大
        center: ['50%', '50%'],
        radius: ['65%', '65%'],
        label: {
          normal: {
            show: false
          }
        },
        data: [
          {
            value: '1',
            name: '',
            itemStyle: {
              normal: {
                borderWidth: 2,
                borderColor: '#0b5263'
              }
            },
            tooltip: {
              show: false
            }
          }
        ]
      }
    ]
  }
}

/**
 * 玫瑰图
 */
export const baseRoseConfig: any = () => {
  return {
    title: {
      text: '',
      x: 'center',
      y: 'center',
      left: 'center',
      bottom: '2%',
      right: '2%',
      textStyle: {
        fontSize: '10%',
        fontWeight: 'bold',
        color: '#fff'
      }
    },
    tooltip: {
      trigger: 'item',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      appendToBody: true, // 将 tooltip 渲染到 body 元素中
      formatter: '{b}: {c}'
    },
    grid: {
      left: '2%',
      right: '3%',
      top: '30%',
      containLabel: true
    },
    series: [
      {
        type: 'pie',
        label: {
          normal: {
            show: true,
            position: 'outside', // 在饼图内部显示标签
            formatter: '{b}\n{c}个',
            textStyle: {
              color: '#fff',
              fontSize: '1rem' // 设置文字大小
            }
          }
        },
        labelLine: {
          normal: {
            length: 2 // 调整标签线的长度
          }
        },
        color: ['#18DB79', '#A600FF', '#2056DD', '#FFBF00', '#FF4949'],
        radius: ['20%', '150%'],
        center: ['50%', '90%'],
        roseType: 'area',
        itemStyle: {
          borderRadius: 1
        },
        startAngle: 180,
        endAngle: 360,
        data: []
      }
    ]
  }
}

/**
 * @name 环形饼图配置
 * @returns
 */
export const ringPieConfig = (data: Array<any>): any => {
  // data = [
  //   {
  //     name: "使用中资源量",
  //     value: 754,
  //   },
  //   {
  //     name: "维修中资源量",
  //     value: 611,
  //   },
  //   {
  //     name: "保养中资源量",
  //     value: 400,
  //   },
  //   {
  //     name: "已损坏资源量",
  //     value: 200,
  //   },
  // ];
  const arrName = getArrayValue(data, 'name')
  const arrValue = getArrayValue(data, 'value')
  let sumValue = 0
  arrValue.forEach((item) => {
    sumValue = sumValue + Number(item)
  })
  const objData = array2obj(data, 'name')
  const optionData = getData(data)
  function getArrayValue(array: Array<any>, key: any) {
    key = key || 'value'
    const res: Array<any> = []
    if (array) {
      array.forEach(function (t: any) {
        res.push(t[key])
      })
    }
    return res
  }

  function array2obj(array: Array<any>, key: any) {
    const resObj: any = {}
    for (let i = 0; i < array.length; i++) {
      resObj[array[i][key]] = array[i]
    }
    return resObj
  }

  function getData(data: any) {
    const res = {
      series: [] as Array<any>,
      yAxis: [] as Array<any>
    }
    for (let i = 0; i < data.length; i++) {
      // console.log([70 - i * 15 + '%', 67 - i * 15 + '%']);
      res.series.push({
        name: '',
        type: 'pie',
        clockWise: false, //顺时加载
        hoverAnimation: false, //鼠标移入变大
        radius: [85 - i * 15 + '%', 78 - i * 15 + '%'],
        center: ['30%', '50%'],
        label: {
          show: false
        },
        itemStyle: {
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          borderWidth: 5
        },
        data: [
          {
            value: data[i].value,
            name: data[i].name
          },
          {
            value: sumValue - data[i].value,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
              borderWidth: 0
            },
            tooltip: {
              show: false
            },
            hoverAnimation: false
          }
        ]
      })
      res.series.push({
        name: '',
        type: 'pie',
        silent: true,
        z: 1,
        clockWise: false, //顺时加载
        hoverAnimation: false, //鼠标移入变大
        radius: [85 - i * 15 + '%', 78 - i * 15 + '%'],
        center: ['30%', '50%'],
        label: {
          show: false
        },
        itemStyle: {
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          borderWidth: 5
        },
        data: [
          {
            value: 10,
            itemStyle: {
              color: 'rgb(3, 31, 62)',
              borderWidth: 0
            },
            tooltip: {
              show: false
            },
            hoverAnimation: false
          },
          {
            value: 0,
            name: '',
            itemStyle: {
              color: 'rgba(0,0,0,0)',
              borderWidth: 0
            },
            tooltip: {
              show: false
            },
            hoverAnimation: false
          }
        ]
      })
      res.yAxis.push(((data[i].value / sumValue) * 100).toFixed(2) + ' %')
    }
    return res
  }

  const colorList = ['#FFBF00', '#1AD693', '#2056DD', '#07C7EE', '#F350DA']
  const rich: any = {
    title: {
      fontSize: '1rem',
      lineHeight: 16,
      width: 60,
      color: '#FFFFFF',
      marginRight: '1rem'
    },
    value: {
      fontSize: '1rem',
      lineHeight: 16,
      color: '#fff',
      align: 'right'
    }
  }
  for (let i = 0; i < arrName.length; i++) {
    rich['value' + i] = {
      fontSize: '1rem',
      lineHeight: 16,
      fontWeight: 'bold',
      color: colorList[i],
      align: 'right'
    }
  }
  return {
    legend: {
      show: true,
      top: 'center',
      right: 40,
      data: arrName,
      width: 50,
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 8, // 设置小方块的宽度
      itemHeight: 8, // 设置小方块的高度
      padding: [0, 5],
      itemGap: 15,
      formatter: function (name: any) {
        return `{title| ${name}}{value${arrName.indexOf(name)}|${objData[name].value}}`
      },

      textStyle: {
        rich: rich
      }
    },
    tooltip: {
      show: true,
      trigger: 'item',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      formatter: function (params: any) {
        const result = `${params.marker} ${params.name}： ${params.value}<br/>`
        return result
      }
    },
    color: colorList,
    grid: {
      top: '16%',
      bottom: '53%',
      left: '30%',
      containLabel: false
    },
    yAxis: [
      {
        type: 'category',
        inverse: true,
        axisLine: {
          show: false
        },
        axisTick: {
          show: false
        },
        axisLabel: {
          interval: 0,
          inside: true,
          textStyle: {
            color: '#fff',
            fontSize: 12
          },
          show: false
        },
        data: optionData.yAxis
      }
    ],
    xAxis: [
      {
        show: false
      }
    ],
    series: optionData.series
  }
}

// src/service/pie3DConfig.js
export const pie3DConfig = () => {
  const dataList = [
    {
      name: '公务用车运行维护费',
      val: 1230, // 存储数据的地方
      itemStyle: {
        color: 'rgba(0, 81, 180, 0.5)'
      }
    },
    {
      name: '办公费',
      val: 800, // 存储数据的地方
      itemStyle: {
        color: 'rgba(255, 196, 0, 0.5)'
      }
    },
    {
      name: '差旅费',
      val: 500, // 存储数据的地方
      itemStyle: {
        color: 'rgba(95, 144, 110, 0.5)'
      }
    }
  ]
  const heightProportion = 0.2 // 柱状扇形的高度比例

  // 生成扇形的曲面参数方程，用于 series-surface.parametricEquation
  function getParametricEquation(startRatio, endRatio, isSelected, isHovered, k, height) {
    // 计算
    const midRatio = (startRatio + endRatio) / 3
    const startRadian = startRatio * Math.PI * 2
    const endRadian = endRatio * Math.PI * 2
    const midRadian = midRatio * Math.PI * 2

    // 如果只有一个扇形，则不实现选中效果。
    if (startRatio === 0 && endRatio === 1) {
      isSelected = false
    }

    // 通过扇形内径/外径的值，换算出辅助参数 k（默认值 1/3）
    k = typeof k !== 'undefined' ? k : 1 / 3

    // 计算选中效果分别在 x 轴、y 轴方向上的位移（未选中，则位移均为 0）
    const offsetX = isSelected ? Math.cos(midRadian) * 0.1 : 0
    const offsetY = isSelected ? Math.sin(midRadian) * 0.1 : 0

    // 计算高亮效果的放大比例（未高亮，则比例为 1）
    const hoverRate = isHovered ? 1.1 : 1

    // 返回曲面参数方程
    return {
      u: {
        min: -Math.PI,
        max: Math.PI * 3,
        step: Math.PI / 32
      },
      v: {
        min: 0,
        max: Math.PI * 2,
        step: Math.PI / 20
      },
      x: function (u, v) {
        if (u < startRadian) {
          return offsetX + Math.cos(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        if (u > endRadian) {
          return offsetX + Math.cos(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        return offsetX + Math.cos(u) * (1 + Math.cos(v) * k) * hoverRate
      },
      y: function (u, v) {
        if (u < startRadian) {
          return offsetY + Math.sin(startRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        if (u > endRadian) {
          return offsetY + Math.sin(endRadian) * (1 + Math.cos(v) * k) * hoverRate
        }
        return offsetY + Math.sin(u) * (1 + Math.cos(v) * k) * hoverRate
      },
      z: function (u, v) {
        if (u < -Math.PI * 0.5) {
          return Math.sin(u)
        }
        if (u > Math.PI * 2.5) {
          return Math.sin(u)
        }
        return Math.sin(v) > 0 ? heightProportion * height : -1
      }
    }
  }

  // 生成模拟 3D 饼图的配置项
  function getPie3D(pieData, internalDiameterRatio) {
    let series = []
    let sumValue = 0
    let startValue = 0
    let endValue = 0
    const legendData = []
    const linesSeries = [] // line3D模拟label指示线
    const k =
      typeof internalDiameterRatio !== 'undefined'
        ? (1 - internalDiameterRatio) / (1 + internalDiameterRatio)
        : 1 / 3

    // 为每一个饼图数据，生成一个 series-surface 配置
    for (let i = 0; i < pieData.length; i++) {
      sumValue += pieData[i].value

      const seriesItem = {
        name: typeof pieData[i].name === 'undefined' ? `series${i}` : pieData[i].name,
        type: 'surface',
        parametric: true,
        wireframe: {
          show: false
        },
        pieData: pieData[i],
        pieStatus: {
          selected: false,
          hovered: false,
          k: k
        }
      }

      if (typeof pieData[i].itemStyle != 'undefined') {
        const itemStyle = {}
        typeof pieData[i].itemStyle.color != 'undefined'
          ? (itemStyle.color = pieData[i].itemStyle.color)
          : null
        typeof pieData[i].itemStyle.opacity != 'undefined'
          ? (itemStyle.opacity = pieData[i].itemStyle.opacity)
          : null
        seriesItem.itemStyle = itemStyle
      }
      series.push(seriesItem)
    }

    // 使用上一次遍历时，计算出的数据和 sumValue，调用 getParametricEquation 函数，
    // 向每个 series-surface 传入不同的参数方程 series-surface.parametricEquation，也就是实现每一个扇形。
    for (let i = 0; i < series.length; i++) {
      endValue = startValue + series[i].pieData.value
      series[i].pieData.startRatio = startValue / sumValue
      series[i].pieData.endRatio = endValue / sumValue
      series[i].parametricEquation = getParametricEquation(
        series[i].pieData.startRatio,
        series[i].pieData.endRatio,
        false,
        false,
        k,
        series[i].pieData.value
      )

      startValue = endValue

      // 计算label指示线的起始和终点位置
      const midRadian = (series[i].pieData.endRatio + series[i].pieData.startRatio) * Math.PI
      const posX = Math.cos(midRadian) * (1 + Math.cos(Math.PI / 2))
      const posY = Math.sin(midRadian) * (1 + Math.cos(Math.PI / 2))
      const posZ = Math.log(Math.abs(series[i].pieData.value + 1)) * 0.1
      const flag =
        (midRadian >= 0 && midRadian <= Math.PI / 2) ||
        (midRadian >= (3 * Math.PI) / 2 && midRadian <= Math.PI * 2)
          ? 1
          : -1
      const color = pieData[i].itemStyle.color
      const turningPosArr = [
        posX * 1.8 + i * 0.1 * flag + (flag < 0 ? -0.5 : 0),
        posY * 1.8 + i * 0.1 * flag + (flag < 0 ? -0.5 : 0),
        posZ * 2
      ]
      const endPosArr = [
        posX * 1.9 + i * 0.1 * flag + (flag < 0 ? -0.5 : 0),
        posY * 1.9 + i * 0.1 * flag + (flag < 0 ? -0.5 : 0),
        posZ * 6
      ]

      linesSeries.push(
        {
          type: 'line3D',
          lineStyle: {
            color: color
          },
          data: [[posX, posY, posZ], turningPosArr, endPosArr]
        },
        {
          type: 'scatter3D',
          label: {
            show: true,
            distance: 0,
            position: 'center',
            textStyle: {
              color: '#ffffff',
              backgroundColor: color,
              borderWidth: 2,
              fontSize: 14,
              padding: 10,
              borderRadius: 4
            },
            formatter: '{b}'
          },
          symbolSize: 0,
          data: [{ name: series[i].name + '\n' + series[i].pieData.val, value: endPosArr }]
        }
      )

      legendData.push(series[i].name)
    }
    series = series.concat(linesSeries)

    // 最底下圆盘
    series.push({
      name: 'mouseoutSeries',
      type: 'surface',
      parametric: true,
      wireframe: {
        show: false
      },
      itemStyle: {
        opacity: 1,
        color: 'rgba(25, 93, 176, 1)'
      },
      parametricEquation: {
        u: {
          min: 0,
          max: Math.PI * 2,
          step: Math.PI / 20
        },
        v: {
          min: 0,
          max: Math.PI,
          step: Math.PI / 20
        },
        x: function (u, v) {
          return ((Math.sin(v) * Math.sin(u) + Math.sin(u)) / Math.PI) * 2
        },
        y: function (u, v) {
          return ((Math.sin(v) * Math.cos(u) + Math.cos(u)) / Math.PI) * 2
        },
        z: function (u, v) {
          return Math.cos(v) > 0 ? -0 : -1.5
        }
      }
    })
    return series
  }

  let total = 0
  dataList.forEach((item) => {
    total += item.val
  })
  const series = getPie3D(
    dataList.map((item) => {
      item.value = Number(((item.val / total) * 100).toFixed(2))
      return item
    }),
    0.8,
    240,
    28,
    26,
    1
  )
  return {
    legend: {
      tooltip: {
        show: true
      },
      data: dataList.map((item) => item.name),
      top: '5%',
      left: '5%',
      icon: 'circle',
      textStyle: {
        color: '#fff',
        fontSize: 14
      }
    },
    animation: true,
    title: [
      {
        x: 'center',
        top: '40%',
        text: total,
        textStyle: {
          color: '#fff',
          fontSize: 42,
          fontWeight: 'bold'
        }
      },
      {
        x: 'center',
        top: '48%',
        text: '还款总额',
        textStyle: {
          color: '#fff',
          fontSize: 22,
          fontWeight: 400
        }
      }
    ],
    backgroundColor: '#333',
    labelLine: {
      show: true,
      lineStyle: {
        color: '#7BC0CB'
      }
    },
    label: {
      show: false
    },
    xAxis3D: {
      min: -1.5,
      max: 1.5
    },
    yAxis3D: {
      min: -1.5,
      max: 1.5
    },
    zAxis3D: {
      min: -1,
      max: 1
    },
    grid3D: {
      show: false,
      boxHeight: 4,
      bottom: '50%',
      viewControl: {
        distance: 180,
        alpha: 25,
        beta: 60,
        autoRotate: true // 自动旋转
      }
    },
    series: series
  }
}
