import { Bottom } from '@element-plus/icons-vue'
import * as echarts from 'echarts'

/**
 * 基础“折线图堆叠”
 */
export const baseStackedLineConfig = (): any => {
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      axisPointer: {
        type: 'none' //隐藏辅助线
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
        for (let i = 0; i < params.length; i++) {
          result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
        }
        return result
      }
    },
    grid: {
      left: '26px',
      right: '20px',
      bottom: '26px',
      top: '36px',
      containLabel: true
    },
    legend: {
      data: ['入市', '出市'], // 图例项的名称
      //right: 1,
      //top: 1,
      bottom: 0,
      textStyle: {
        color: '#4E5969' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 12, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    xAxis: {
      data: [],
      axisLabel: {
        show: true,
        color: '#4E5969'
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      },
      boundaryGap: false
    },
    yAxis: {
      name: '辆',
      nameGap: 16,
      nameTextStyle: {
        color: '#4E5969',
        fontSize: 12,
        align: 'right'
      },
      axisLabel: {
        show: true,
        color: '#4E5969'
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#A2B0C7'
        }
      },
      splitNumber: 3
    },
    series: [
      {
        data: [],
        name: '入市',
        type: 'line',
        showSymbol: false,
        symbol: 'none',
        smooth: true,
        lineStyle: {
          color: '#3CC73A',
          width: 1.2
        },
        areaStyle: {
          opacity: 0.4,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(60, 199, 58, .5)' },
            { offset: 0.8, color: 'rgba(60, 199, 58, 0.2)' }
          ])
        }
      },
      {
        data: [],
        name: '出市',
        type: 'line',
        showSymbol: false,
        symbol: 'none',
        smooth: true,
        lineStyle: {
          color: '#406DFC',
          width: 1.2
        },
        areaStyle: {
          opacity: 0.4,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(64, 109, 252, 0.5)' },
            { offset: 0.8, color: 'rgba(64, 109, 252, 0.1)' }
          ])
        }
      },
    ],
  }
}

/**
 * 折线柱状图
 */
export const lineBarChartConfig = (): echarts.EChartsOption => {
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度formatter
      padding: 10, // 内边距
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesName != '' && params[i].color != '#159AFF') {
            result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
          }
        }
        return result
      }
    },
    legend: {
      data: [], // 图例项的名称
      right: 1,
      top: 1,
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    grid: {
      left: '3%',
      right: '3%',
      bottom: '3%',
      top: '25%',
      containLabel: true
    },
    xAxis: {
      type: 'category',
      data: [],
      triggerEvent: true,
      axisTick: {
        show: false
      },
      axisLine: {
        show: true
      },
      axisLabel: {
        color: '#D8F0FF',
        fontSize: 12
      }
    },
    yAxis: {
      triggerEvent: true,
      nameTextStyle: {
        color: '#D2D4D9',
        fontSize: 12
      },
      axisLabel: {
        show: true,
        color: '#D8F0FF',
        fontSize: 12
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: 'rgba(255, 255, 255, .1)'
        }
      },
      axisTick: {
        show: false
      }
    },
    series: [
      {
        name: '进省',
        stack: '1',
        type: 'bar',
        data: [],
        barWidth: 10, // 设置柱状图宽度
        showBackground: true,
        backgroundStyle: { color: '#0F1838' },
        label: {
          show: false,
          position: 'top',
          textStyle: {
            color: '#D8F0FF'
          }
        },
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(
              0,
              0,
              0,
              1,
              [
                {
                  offset: 0,
                  color: 'rgba(0,58,255,0.4)' // 0% 处的颜色
                },
                {
                  offset: 1,
                  color: '#159AFF' // 100% 处的颜色
                }
              ],
              false
            )
          },
          color: '#159AFF'
        }
      },
      {
        name: '出省',
        data: [],
        type: 'line',
        showAllSymbol: true,
        symbol: 'emptyCircle',
        symbolSize: 3,
        smooth: true,
        label: {
          show: false,
          position: 'top',
          textStyle: {
            color: '#D8F0FF'
          }
        },
        lineStyle: {
          color: '#83D8D8',
          width: 1.2
        },
        areaStyle: {
          opacity: 0.4,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(102, 225, 223, .5)' },
            { offset: 0.8, color: 'rgba(102, 225, 223, 0)' }
          ])
        },
        itemStyle: {
          color: '#66E1DF'
        }
      },
      {
        name: '',
        stack: '1',
        type: 'bar',
        data: [],
        itemStyle: {
          normal: {
            color: '#159AFF'
          }
        }
      }
    ]
  }
}

/**
 * 堆积折线图
 */
export const heapLineChartConfig = (): any => {
  const color = ['#1874DE', '#23D764', '#18E0DF', '#F9733D', '#D9841F']
  const hexToRgba = (hex: any, opacity: any) => {
    let rgbaColor = ''
    const reg = /^#[\da-f]{6}$/i
    if (reg.test(hex)) {
      rgbaColor = `rgba(${parseInt('0x' + hex.slice(1, 3))},${parseInt(
        '0x' + hex.slice(3, 5)
      )},${parseInt('0x' + hex.slice(5, 7))},${opacity})`
    }
    return rgbaColor
  }
  const lineStyle = (index: any) => {
    return {
      normal: {
        color: color[index],
        shadowColor: hexToRgba(color[index], 0.5),
        shadowBlur: 3,
        shadowOffsetY: 8
      }
    }
  }
  const areaStyle = (index: any) => {
    return {
      normal: {
        color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
          {
            offset: 0,
            color: hexToRgba(color[index], 0.3)
          },
          {
            offset: 1,
            color: hexToRgba(color[index], 0.1)
          }
        ]),
        shadowColor: hexToRgba(color[index], 0.1),
        shadowBlur: 10
      }
    }
  }

  return {
    color: color,
    legend: {
      data: ['优', '良', '中', '次', '差'], // 图例项的名称
      right: 1,
      top: 1,
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    grid: {
      left: '3%',
      right: '5%',
      bottom: '3%',
      top: 30,
      containLabel: true
    },
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度formatter
      padding: 10, // 内边距
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
        for (let i = 0; i < params.length; i++) {
          if (params[i].seriesName != '') {
            result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
          }
        }
        return result
      }
    },
    xAxis: {
      data: [],
      axisLabel: {
        show: true,
        color: '#D8F0FF' // 设置标签字体颜色为白色
        // fontSize: '.875rem' // 调整字体大小
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
      boundaryGap: false
    },
    yAxis: {
      name: '条',
      nameTextStyle: {
        color: '#D2D4D9'
        // fontSize: '.875rem'
      },
      axisLabel: {
        show: true,
        color: '#D8F0FF'
        // fontSize: '.875rem'
      },
      splitLine: {
        show: false
      },
      splitNumber: 2
    },
    series: [
      {
        name: '优',
        stack: '值',
        type: 'line',
        data: [],
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        lineStyle: lineStyle(0),
        areaStyle: areaStyle(0)
      },
      {
        name: '良',
        stack: '值',
        type: 'line',
        data: [],
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        lineStyle: lineStyle(1),
        areaStyle: areaStyle(1)
      },
      {
        name: '中',
        stack: '值',
        type: 'line',
        data: [],
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        lineStyle: lineStyle(2),
        areaStyle: areaStyle(2)
      },
      {
        name: '次',
        stack: '值',
        type: 'line',
        data: [],
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        lineStyle: lineStyle(3),
        areaStyle: areaStyle(3)
      },
      {
        name: '差',
        stack: '值',
        type: 'line',
        data: [],
        symbol: 'none',
        smooth: true,
        symbolSize: 6,
        lineStyle: lineStyle(4),
        areaStyle: areaStyle(4)
      }
    ]
  }
}

/**
 * @name 双Y轴
 * @description 折线图
 */
export const doubleYLineConfig = (): any => {
  return {
    tooltip: {
      show: true,
      trigger: 'axis',
      backgroundColor: 'rgba(7,22,60,0.9)', // 背景色
      textStyle: {
        color: '#fff', // 字体颜色
        fontSize: 12
      },
      borderColor: '#ccc', // 边框颜色
      borderWidth: 1, // 边框宽度
      padding: 10, // 内边距
      axisPointer: {
        type: 'none' //隐藏辅助线
      },
      formatter: function (params: any) {
        let result = `${params[0].axisValue}<br/>` // 显示 x 轴的值
        for (let i = 0; i < params.length; i++) {
          result += `${params[i].marker} ${params[i].seriesName}： ${params[i].value}<br/>` // 显示每列数据
        }
        return result
      }
    },
    grid: {
      left: '5%',
      right: '5%',
      bottom: '12%',
      top: '38%',
      containLabel: true
    },
    legend: {
      data: [],
      right: 100,
      top: 2,
      textStyle: {
        color: '#fff' // 设置图例文本颜色为白色
      },
      icon: 'rect', // 将图例项图标设置为小方块
      itemWidth: 10, // 设置小方块的宽度
      itemHeight: 3, // 设置小方块的高度
      tooltip: {
        show: true
      }
    },
    xAxis: {
      data: [],
      axisLabel: {
        show: true,
        color: '#D8F0FF' // 设置标签字体颜色为白色
      },
      axisLine: {
        show: true
      },
      axisTick: {
        show: true
      },
      splitLine: {
        show: true,
        lineStyle: {
          type: 'dashed',
          color: '#293357'
        }
      },
      boundaryGap: false
    },
    yAxis: [
      {
        type: 'value',
        name: '', // 左侧Y轴
        position: 'left', // 指定左侧Y轴
        alignTicks: true, // 使两个Y轴对齐刻度，可选
        splitNumber: 1,
        nameGap: 16,
        nameTextStyle: {
          color: '#D2D4D9',
          fontSize: '.875rem',
          align: 'left'
        },
        axisLabel: {
          show: true,
          color: '#D8F0FF'
        },
        splitLine: {
          show: false
        },
        areaStyle: {
          opacity: 0.4,
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba( 32, 86,221, 0.5)' },
            { offset: 0.8, color: 'rgba( 32, 86,221, 0)' }
          ])
        }
      },
      {
        type: 'value',
        name: '', // 右侧Y轴
        position: 'right', // 指定右侧Y轴
        splitNumber: 1,
        alignTicks: true, // 使两个Y轴对齐刻度，可选
        nameGap: 16,
        nameTextStyle: {
          color: '#D2D4D9',
          fontSize: '.875rem',
          align: 'right'
        },
        axisLabel: {
          show: true,
          color: '#D8F0FF'
        },
        splitLine: {
          show: false
        }
      }
    ],
    series: [
      {
        name: '',
        type: 'line',
        showSymbol: false,
        symbol: 'none',
        smooth: true,
        yAxisIndex: 0, // 使用第一个Y轴（即左侧的Y轴）
        data: []
      },
      {
        name: '',
        type: 'line',
        showSymbol: false,
        symbol: 'none',
        smooth: true,
        yAxisIndex: 1, // 使用第一个Y轴（即左侧的Y轴）
        data: []
      }
    ]
  }
}
