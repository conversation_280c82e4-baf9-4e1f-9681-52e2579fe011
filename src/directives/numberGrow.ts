import type { App } from 'vue'

/**
 * @name 自定义指令
 * @description 数字增长动画
 */
// 定义自定义指令
const numberGrow = {
  mounted(el: HTMLElement, binding: { value: number }) {
    startAnimation(el, binding.value)
  },
  updated(el: HTMLElement, binding: { value: number }) {
    // 数据更新时重新执行动画
    startAnimation(el, binding.value)
  }
}

// 动画执行函数
const startAnimation = (el: HTMLElement, value: number) => {
  const duration = 50 // 控制动画持续的时间
  const step = value / duration // 每一步增加的值
  let current = 0 // 初始值

  const updateElement = () => {
    el.innerHTML = Math.round(current)?.toString() || '--' // 格式化并更新元素内容
  }

  const animate = () => {
    // fixed 优化显示
    if (value === 0) {
      el.innerHTML = '-'
      return
    }
    if (current < value) {
      current += step // 增加当前值
      updateElement()
      requestAnimationFrame(animate) // 使用 requestAnimationFrame 实现动画
    } else {
      el.innerHTML = value?.toString() || '--' // 到达目标值时，直接设置最终值
    }
  }
  animate() // 启动动画
}

// 注册指令
export default {
  install(app: App) {
    app.directive('number-grow', numberGrow)
  }
}
