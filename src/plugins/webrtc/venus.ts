import { amplesky_venus } from './amplesky-sdk.min.js'

export interface Venus {
  isResult: (msg: VenusMsg) => msg is VenusResult
  isError: (msg: VenusMsg) => msg is VenusError

  getVersion: () => { version: string }
  /**
   * 呼叫摄像头
   * 页面只能呼叫同一个摄像头一次，使用完后应该调用 {@link hangup_camera} 挂断该摄像头。
   * 如果未挂断重复呼叫同一个摄像头，将报错。
   *
   * @param venusServer 网关地址
   * @param mcuServer 会议IP，需要传值但不会使用，传 '127.0.0.1' 即可
   * @param cameraNum 呼叫号
   * @param videoId 显示远端视频 video 标签 id
   * @param callback 回调函数
   */
  call_camera: (
    venusServer: string,
    mcuServer: string,
    cameraNum: string,
    videoId: string,
    callback: (msg: VenusMsg) => void
  ) => void

  /**
   * 挂断通话
   *
   * @param callNumber 呼叫号
   */
  hangup_camera: (callNumber: string) => void

  set_call_codec_type: (arg1: any, arg2: any, arg3: boolean) => void
}

interface VenusResult {
  videomonitor: 'event'
  result: {
    event: 'calling' | 'accepted'
    username: string
  }
}
type VenusError = string

export type VenusMsg = VenusResult | VenusError
export const isResult = (msg: VenusMsg): msg is VenusResult => !!(msg as VenusResult).result
export const isError = (msg: VenusMsg): msg is VenusError => typeof msg === 'string'

export const venus: Venus = amplesky_venus

export default venus
