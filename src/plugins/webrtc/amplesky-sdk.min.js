import $ from 'jquery'
/*  Version: 1.2.75-3d09fb2d2,  Compile Date: 2024-06-28 14:46:44  */
Venus.sessions={};var WertcSessionID="";var peerConnect;var RemberLocalStream=null;let VideoCallmedia;let VideoCallBack;let videoCallpluginHandle;let ScreenStream=null;let localConfig=null;let loginHandleId;Venus.isExtensionEnabled=function(){if(navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia){return true}if(window.navigator.userAgent.match("Chrome")){var chromever=parseInt(window.navigator.userAgent.match(/Chrome\/(.*) /)[1],10);var maxver=33;if(window.navigator.userAgent.match("Linux"))maxver=35;if(chromever>=26&&chromever<=maxver){return true}return Venus.extension.isInstalled()}else{return true}};var defaultExtension={extensionId:"hapfgfdkleiggjjpfpenajgdnfckjpaj",isInstalled:function(){return document.querySelector("#venus-extension-installed")!==null},getScreen:function(callback){var pending=window.setTimeout(function(){var error=new Error("NavigatorUserMediaError");error.name='The required Chrome extension is not installed: click <a href="#">here</a> to install it. (NOTE: this will need you to refresh the page)';return callback(error)},1e3);this.cache[pending]=callback;window.postMessage({type:"venusGetScreen",id:pending},"*")},init:function(){var cache={};this.cache=cache;window.addEventListener("message",function(event){if(event.origin!=window.location.origin)return;if(event.data.type=="venusGotScreen"&&cache[event.data.id]){var callback=cache[event.data.id];delete cache[event.data.id];if(event.data.sourceId===""){var error=new Error("NavigatorUserMediaError");error.name="You cancelled the request for permission, giving up...";callback(error)}else{callback(null,event.data.sourceId)}}else if(event.data.type=="venusGetScreenPending"){console.log("clearing ",event.data.id);window.clearTimeout(event.data.id)}})}};Venus.useDefaultDependencies=function(deps){var f=deps&&deps.fetch||fetch;var p=deps&&deps.Promise||Promise;var socketCls=deps&&deps.WebSocket||WebSocket;return{newWebSocket:function(server,proto){return new socketCls(server,proto)},extension:deps&&deps.extension||defaultExtension,isArray:function(arr){return Array.isArray(arr)},webRTCAdapter:deps&&deps.adapter||adapter,httpAPICall:function(url,options){var fetchOptions={method:options.verb,headers:{Accept:"application/json, text/plain, */*"},cache:"no-cache"};if(options.verb==="POST"){fetchOptions.headers["Content-Type"]="application/json"}if(options.withCredentials!==undefined){fetchOptions.credentials=options.withCredentials===true?"include":options.withCredentials?options.withCredentials:"omit"}if(options.body!==undefined){fetchOptions.body=JSON.stringify(options.body)}var fetching=f(url,fetchOptions).catch(function(error){return p.reject({message:"Probably a network error, is the server down?",error:error})});if(options.timeout!==undefined){var timeout=new p(function(resolve,reject){var timerId=setTimeout(function(){clearTimeout(timerId);return reject({message:"Request timed out",timeout:options.timeout})},options.timeout)});fetching=p.race([fetching,timeout])}fetching.then(function(response){if(response.ok){if(typeof options.success===typeof Venus.noop){return response.json().then(function(parsed){options.success(parsed)}).catch(function(error){return p.reject({message:"Failed to parse response body",error:error,response:response})})}}else{return p.reject({message:"API call failed",response:response})}}).catch(function(error){if(typeof options.error===typeof Venus.noop){options.error(error.message||"<< internal error >>",error)}});return fetching}}};Venus.useOldDependencies=function(deps){var jq=deps&&deps.jQuery||jQuery;var socketCls=deps&&deps.WebSocket||WebSocket;return{newWebSocket:function(server,proto){return new socketCls(server,proto)},isArray:function(arr){return jq.isArray(arr)},extension:deps&&deps.extension||defaultExtension,webRTCAdapter:deps&&deps.adapter||adapter,httpAPICall:function(url,options){var payload=options.body!==undefined?{contentType:"application/json",data:JSON.stringify(options.body)}:{};var credentials=options.withCredentials!==undefined?{xhrFields:{withCredentials:options.withCredentials}}:{};return jq.ajax(jq.extend(payload,credentials,{url:url,type:options.verb,cache:false,dataType:"json",async:options.async,timeout:options.timeout,success:function(result){if(typeof options.success===typeof Venus.noop){options.success(result)}},error:function(xhr,status,err){if(typeof options.error===typeof Venus.noop){options.error(status,err)}}}))}}};Venus.noop=function(){};Venus.dataChanDefaultLabel="VenusDataChannel";Venus.endOfCandidates=null;Venus.init=function(options){options=options||{};options.callback=typeof options.callback=="function"?options.callback:Venus.noop;if(Venus.initDone===true){options.callback()}else{if(typeof console=="undefined"||typeof console.log=="undefined")console={log:function(){}};Venus.trace=Venus.noop;Venus.debug=Venus.noop;Venus.vdebug=Venus.noop;Venus.log=Venus.noop;Venus.warn=Venus.noop;Venus.error=Venus.noop;if(options.debug===true||options.debug==="all"){Venus.trace=console.trace.bind(console);Venus.debug=console.debug.bind(console);Venus.vdebug=console.debug.bind(console);Venus.log=console.log.bind(console);Venus.warn=console.warn.bind(console);Venus.error=console.error.bind(console)}else if(Array.isArray(options.debug)){for(var i in options.debug){var d=options.debug[i];switch(d){case"trace":Venus.trace=console.trace.bind(console);break;case"debug":Venus.debug=console.debug.bind(console);break;case"vdebug":Venus.vdebug=console.debug.bind(console);break;case"log":Venus.log=console.log.bind(console);break;case"warn":Venus.warn=console.warn.bind(console);break;case"error":Venus.error=console.error.bind(console);break;default:console.error("Unknown debugging option '"+d+"' (supported: 'trace', 'debug', 'vdebug', 'log', warn', 'error')");break}}}Venus.log("Initializing library");var usedDependencies=options.dependencies||Venus.useDefaultDependencies();Venus.isArray=usedDependencies.isArray;Venus.webRTCAdapter=usedDependencies.webRTCAdapter;Venus.httpAPICall=usedDependencies.httpAPICall;Venus.newWebSocket=usedDependencies.newWebSocket;Venus.extension=usedDependencies.extension;Venus.extension.init();Venus.listDevices=function(callback,config){callback=typeof callback=="function"?callback:Venus.noop;if(config==null)config={audio:true,video:true};if(Venus.isGetUserMediaAvailable()){navigator.mediaDevices.getUserMedia(config).then(function(stream){navigator.mediaDevices.enumerateDevices().then(function(devices){Venus.debug(devices);callback(devices);try{var tracks=stream.getTracks();for(var i in tracks){var mst=tracks[i];if(mst!==null&&mst!==undefined)mst.stop()}}catch(e){}})}).catch(function(err){Venus.error(err);callback([])})}else{Venus.warn("navigator.mediaDevices unavailable");callback([])}};Venus.attachMediaStream=function(element,stream){try{element.srcObject=stream}catch{try{element.src=URL.createObjectURL(stream)}catch{Venus.error("Error attaching stream to element")}}};Venus.reattachMediaStream=function(to,from){try{to.srcObject=from.srcObject}catch{try{to.src=from.src}catch{Venus.error("Error reattaching stream to element")}}};var iOS=["iPad","iPhone","iPod"].indexOf(navigator.platform)>=0;var eventName=iOS?"pagehide":"beforeunload";var oldOBF=window["on"+eventName];window.addEventListener(eventName,function(event){Venus.log("Closing window");for(var s in Venus.sessions){if(Venus.sessions[s]!==null&&Venus.sessions[s]!==undefined&&Venus.sessions[s].destroyOnUnload){Venus.log("Destroying session "+s);Venus.sessions[s].destroy({asyncRequest:false,notifyDestroyed:false})}}if(oldOBF&&typeof oldOBF=="function")oldOBF()});Venus.safariVp8=false;if(Venus.webRTCAdapter.browserDetails.browser==="safari"&&Venus.webRTCAdapter.browserDetails.version>=605){if(RTCRtpSender&&RTCRtpSender.getCapabilities&&RTCRtpSender.getCapabilities("video")&&RTCRtpSender.getCapabilities("video").codecs&&RTCRtpSender.getCapabilities("video").codecs.length){for(var i in RTCRtpSender.getCapabilities("video").codecs){var codec=RTCRtpSender.getCapabilities("video").codecs[i];if(codec&&codec.mimeType&&codec.mimeType.toLowerCase()==="video/vp8"){Venus.safariVp8=true;break}}if(Venus.safariVp8){Venus.log("This version of Safari supports VP8")}else{Venus.warn("This version of Safari does NOT support VP8: if you're using a Technology Preview, "+"try enabling the 'WebRTC VP8 codec' setting in the 'Experimental Features' Develop menu")}}else{var testpc=new RTCPeerConnection({},{});testpc.createOffer({offerToReceiveVideo:true}).then(function(offer){Venus.safariVp8=offer.sdp.indexOf("VP8")!==-1;if(Venus.safariVp8){Venus.log("This version of Safari supports VP8")}else{Venus.warn("This version of Safari does NOT support VP8: if you're using a Technology Preview, "+"try enabling the 'WebRTC VP8 codec' setting in the 'Experimental Features' Develop menu")}testpc.close();testpc=null})}}Venus.unifiedPlan=false;if(Venus.webRTCAdapter.browserDetails.browser==="firefox"&&Venus.webRTCAdapter.browserDetails.version>=59){Venus.unifiedPlan=true}else if(Venus.webRTCAdapter.browserDetails.browser==="chrome"&&Venus.webRTCAdapter.browserDetails.version<72){Venus.unifiedPlan=false}else if(!window.RTCRtpTransceiver||!("currentDirection"in RTCRtpTransceiver.prototype)){Venus.unifiedPlan=false}else{const tempPc=new RTCPeerConnection;try{tempPc.addTransceiver("audio");Venus.unifiedPlan=true}catch(e){}tempPc.close()}Venus.initDone=true;options.callback()}};Venus.isWebrtcSupported=function(){return window.RTCPeerConnection!==undefined&&window.RTCPeerConnection!==null};Venus.isGetUserMediaAvailable=function(){return navigator.mediaDevices!==undefined&&navigator.mediaDevices!==null&&navigator.mediaDevices.getUserMedia!==undefined&&navigator.mediaDevices.getUserMedia!==null};Venus.randomString=function(len){var charSet="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";var randomString="";for(var i=0;i<len;i++){var randomPoz=Math.floor(Math.random()*charSet.length);randomString+=charSet.substring(randomPoz,randomPoz+1)}return randomString};function Venus(gatewayCallbacks){if(Venus.initDone===undefined){gatewayCallbacks.error("Library not initialized");return{}}if(!Venus.isWebrtcSupported()){gatewayCallbacks.error("WebRTC not supported by this browser");return{}}Venus.log("Library initialized: "+Venus.initDone);gatewayCallbacks=gatewayCallbacks||{};gatewayCallbacks.success=typeof gatewayCallbacks.success=="function"?gatewayCallbacks.success:Venus.noop;gatewayCallbacks.error=typeof gatewayCallbacks.error=="function"?gatewayCallbacks.error:Venus.noop;gatewayCallbacks.destroyed=typeof gatewayCallbacks.destroyed=="function"?gatewayCallbacks.destroyed:Venus.noop;if(gatewayCallbacks.server===null||gatewayCallbacks.server===undefined){gatewayCallbacks.error("Invalid server url");return{}}var websockets=false;var ws=null;var wsHandlers={};var wsKeepaliveTimeoutId=null;var servers=null,serversIndex=0;var server=gatewayCallbacks.server;if(Venus.isArray(server)){Venus.log("Multiple servers provided ("+server.length+"), will use the first that works");server=null;servers=gatewayCallbacks.server;Venus.debug(servers)}else{if(server.indexOf("ws")===0){websockets=true;Venus.log("Using WebSockets to contact Venus: "+server)}else{websockets=false;Venus.log("Using REST API to contact Venus: "+server)}}var iceServers=gatewayCallbacks.iceServers;let Turn=localStorage.getItem("turnEnable");let serverIP=localStorage.getItem("iceServer");let iceName=localStorage.getItem("iceTurnUser");let icepass=localStorage.getItem("iceTurnPwd");let Protol=localStorage.getItem("turnProtocol");let iceSwitch=localStorage.getItem("setIceSwitch");if(iceServers===undefined||iceServers===null)if(parseInt(iceSwitch)==1){if(parseInt(Turn)==0){if(Protol!=0){iceServers=[{urls:"turn:"+serverIP+"?transport=tcp",username:iceName,credential:icepass,credentialType:"password"}]}else{iceServers=[{urls:"turn:"+serverIP+"?transport=udp",username:iceName,credential:icepass,credentialType:"password"}]}}else{iceServers=[{urls:"stun:"+serverIP}]}}else{iceServers=[]}var iceTransportPolicy=gatewayCallbacks.iceTransportPolicy;if(iceTransportPolicy===undefined||iceTransportPolicy===null)if(parseInt(Turn)==0){iceTransportPolicy="relay"}var bundlePolicy=gatewayCallbacks.bundlePolicy;var ipv6Support=gatewayCallbacks.ipv6;if(ipv6Support===undefined||ipv6Support===null)ipv6Support=false;var withCredentials=false;if(gatewayCallbacks.withCredentials!==undefined&&gatewayCallbacks.withCredentials!==null)withCredentials=gatewayCallbacks.withCredentials===true;var maxev=10;if(gatewayCallbacks.max_poll_events!==undefined&&gatewayCallbacks.max_poll_events!==null)maxev=gatewayCallbacks.max_poll_events;if(maxev<1)maxev=1;var token=null;if(gatewayCallbacks.token!==undefined&&gatewayCallbacks.token!==null)token=gatewayCallbacks.token;var apisecret=null;if(gatewayCallbacks.apisecret!==undefined&&gatewayCallbacks.apisecret!==null)apisecret=gatewayCallbacks.apisecret;this.destroyOnUnload=true;if(gatewayCallbacks.destroyOnUnload!==undefined&&gatewayCallbacks.destroyOnUnload!==null)this.destroyOnUnload=gatewayCallbacks.destroyOnUnload===true;var keepAlivePeriod=25e3;if(gatewayCallbacks.keepAlivePeriod!==undefined&&gatewayCallbacks.keepAlivePeriod!==null)keepAlivePeriod=gatewayCallbacks.keepAlivePeriod;if(isNaN(keepAlivePeriod))keepAlivePeriod=25e3;var longPollTimeout=6e4;if(gatewayCallbacks.longPollTimeout!==undefined&&gatewayCallbacks.longPollTimeout!==null)longPollTimeout=gatewayCallbacks.longPollTimeout;if(isNaN(longPollTimeout))longPollTimeout=6e4;function getMaxBitrates(simulcastMaxBitrates){var maxBitrates={high:9e5,medium:3e5,low:1e5};if(simulcastMaxBitrates!==undefined&&simulcastMaxBitrates!==null){if(simulcastMaxBitrates.high)maxBitrates.high=simulcastMaxBitrates.high;if(simulcastMaxBitrates.medium)maxBitrates.medium=simulcastMaxBitrates.medium;if(simulcastMaxBitrates.low)maxBitrates.low=simulcastMaxBitrates.low}return maxBitrates}var connected=false;var sessionId=null;var pluginHandles={};var that=this;var retries=0;var transactions={};createSession(gatewayCallbacks);this.getServer=function(){return server};this.isConnected=function(){return connected};this.reconnect=function(callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;callbacks["reconnect"]=true;createSession(callbacks)};this.getSessionId=function(){return sessionId};this.destroy=function(callbacks){destroySession(callbacks)};this.attach=function(callbacks){createHandle(callbacks)};function eventHandler(){if(sessionId==null)return;Venus.debug("Long poll...");if(!connected){Venus.warn("Is the server down? (connected=false)");return}var longpoll=server+"/"+sessionId+"?rid="+(new Date).getTime();if(maxev!==undefined&&maxev!==null)longpoll=longpoll+"&maxev="+maxev;if(token!==null&&token!==undefined)longpoll=longpoll+"&token="+encodeURIComponent(token);if(apisecret!==null&&apisecret!==undefined)longpoll=longpoll+"&apisecret="+encodeURIComponent(apisecret);console.info("心跳：");Venus.httpAPICall(longpoll,{verb:"GET",withCredentials:withCredentials,success:handleEvent,timeout:6e4,error:function(textStatus,errorThrown){destroySession();gatewayCallbacks.destroyed();gatewayCallbacks.error(textStatus+":"+errorThrown);cleanupWebrtc(WertcSessionID,true);$(document).trigger("netError");Venus.error("longpoll--",textStatus+":",errorThrown);retries++;if(retries>3){connected=false;gatewayCallbacks.error("Lost connection to the server (is it down?)");return}eventHandler()}})}function handleEvent(json,skipTimeout){retries=0;if(!websockets&&sessionId!==undefined&&sessionId!==null&&skipTimeout!==true)eventHandler();if(!websockets&&Venus.isArray(json)){for(var i=0;i<json.length;i++){handleEvent(json[i],true)}return}if(json["venus"]==="keepalive"){Venus.vdebug("Got a keepalive on session "+sessionId);return}else if(json["venus"]==="ack"){Venus.debug("Got an ack on session "+sessionId);Venus.debug(json);var transaction=json["transaction"];if(transaction!==null&&transaction!==undefined){var reportSuccess=transactions[transaction];if(reportSuccess!==null&&reportSuccess!==undefined){reportSuccess(json)}delete transactions[transaction]}return}else if(json["venus"]==="success"){Venus.debug("Got a success on session "+sessionId);Venus.debug(json);var transaction=json["transaction"];if(transaction!==null&&transaction!==undefined){var reportSuccess=transactions[transaction];if(reportSuccess!==null&&reportSuccess!==undefined){reportSuccess(json)}delete transactions[transaction]}var plugindata=json["plugindata"];if(plugindata===undefined||plugindata===null){Venus.warn("Missing plugindata...")}else{var data=plugindata["data"];Venus.debug(data);var sender=json["sender"];if(sender===undefined||sender===null){}else{var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||sender===null){return}var callback=pluginHandle.onmessage;if(callback!==null&&callback!==undefined){Venus.debug("Notifying application...");callback(data)}else{Venus.debug("No provided notification callback")}}}return}else if(json["venus"]==="trickle"){var sender=json["sender"];if(sender===undefined||sender===null){Venus.warn("Missing sender...");return}var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||pluginHandle===null){Venus.debug("This handle is not attached to this session");return}var candidate=json["candidate"];Venus.debug("Got a trickled candidate on session "+sessionId);Venus.debug(candidate);var config=pluginHandle.webrtcStuff;if(config.pc&&config.remoteSdp){Venus.debug("Adding remote candidate:",candidate);if(!candidate||candidate.completed===true){config.pc.addIceCandidate(Venus.endOfCandidates)}else{config.pc.addIceCandidate(candidate)}}else{Venus.debug("We didn't do setRemoteDescription (trickle got here before the offer?), caching candidate");if(!config.candidates)config.candidates=[];config.candidates.push(candidate);Venus.debug(config.candidates)}}else if(json["venus"]==="webrtcup"){Venus.debug("Got a webrtcup event on session "+sessionId);Venus.debug(json);var sender=json["sender"];if(sender===undefined||sender===null){Venus.warn("Missing sender...");return}var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||pluginHandle===null){Venus.debug("This handle is not attached to this session");return}pluginHandle.webrtcState(true);return}else if(json["venus"]==="hangup"){Venus.debug("Got a hangup event on session "+sessionId);Venus.debug(json);var sender=json["sender"];if(sender===undefined||sender===null){Venus.warn("Missing sender...");return}var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||pluginHandle===null){Venus.debug("This handle is not attached to this session");return}pluginHandle.webrtcState(false,json["reason"]);pluginHandle.hangup();pluginHandle.onmessage(json)}else if(json["venus"]==="detached"){Venus.debug("Got a detached event on session "+sessionId);Venus.debug(json);var sender=json["sender"];if(sender===undefined||sender===null){Venus.warn("Missing sender...");return}var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||pluginHandle===null){return}pluginHandle.detached=true;pluginHandle.ondetached();pluginHandle.detach()}else if(json["venus"]==="media"){Venus.debug("Got a media event on session "+sessionId);Venus.debug(json);var sender=json["sender"];if(sender===undefined||sender===null){Venus.warn("Missing sender...");return}var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||pluginHandle===null){Venus.debug("This handle is not attached to this session");return}pluginHandle.mediaState(json["type"],json["receiving"])}else if(json["venus"]==="slowlink"){Venus.debug("Got a slowlink event on session "+sessionId);Venus.debug(json);$(document).trigger("slowLink",[json]);var sender=json["sender"];if(sender===undefined||sender===null){Venus.warn("Missing sender...");return}var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||pluginHandle===null){Venus.debug("This handle is not attached to this session");return}pluginHandle.slowLink(json["uplink"],json["lost"])}else if(json["venus"]==="error"){Venus.debug(json);var transaction=json["transaction"];if(transaction!==null&&transaction!==undefined){var reportSuccess=transactions[transaction];if(reportSuccess!==null&&reportSuccess!==undefined){reportSuccess(json)}delete transactions[transaction]}return}else if(json["venus"]==="event"){Venus.debug("Got a plugin event on session "+sessionId);Venus.debug(json);var sender=json["sender"];if(sender===undefined||sender===null){Venus.warn("Missing sender...");return}var plugindata=json["plugindata"];if(plugindata===undefined||plugindata===null){Venus.warn("Missing plugindata...");return}Venus.debug("  -- Event is coming from "+sender+" ("+plugindata["plugin"]+")");var data=plugindata["data"];Venus.debug(data);var pluginHandle=pluginHandles[sender];if(pluginHandle===undefined||pluginHandle===null){Venus.warn("This handle is not attached to this session");return}var jsep=json["jsep"];if(jsep!==undefined&&jsep!==null){Venus.debug("Handling SDP as well...");Venus.debug(jsep)}var callback=pluginHandle.onmessage;if(callback!==null&&callback!==undefined){Venus.debug("Notifying application...");callback(data,jsep)}else{Venus.debug("No provided notification callback")}}else if(json["venus"]==="timeout"){Venus.debug(json);if(websockets){ws.close(3504,"Gateway timeout")}return}else{Venus.warn("Unknown message/event  '"+json["venus"]+"' on session "+sessionId);Venus.debug(json)}}function keepAlive(){console.log("keep alive...");if(server===null||!websockets||!connected)return;clearTimeout(wsKeepaliveTimeoutId);wsKeepaliveTimeoutId=setTimeout(keepAlive,keepAlivePeriod);var request={venus:"keepalive",session_id:sessionId,transaction:Venus.randomString(12)};if(token!==null&&token!==undefined)request["token"]=token;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;ws.send(JSON.stringify(request))}function createSession(callbacks){var transaction=Venus.randomString(12);var request={venus:"create",transaction:transaction};if(callbacks["reconnect"]){connected=false;request["venus"]="claim";request["session_id"]=sessionId;if(ws){ws.onopen=null;ws.onerror=null;ws.onclose=null;if(wsKeepaliveTimeoutId){clearTimeout(wsKeepaliveTimeoutId);wsKeepaliveTimeoutId=null}}}if(token!==null&&token!==undefined)request["token"]=token;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;if(server===null&&Venus.isArray(servers)){server=servers[serversIndex];if(server.indexOf("ws")===0){websockets=true;Venus.log("Server #"+(serversIndex+1)+": trying WebSockets to contact Venus ("+server+")")}else{websockets=false;Venus.log("Server #"+(serversIndex+1)+": trying REST API to contact Venus ("+server+")")}}if(websockets){ws=Venus.newWebSocket(server,"venus-protocol");wsHandlers={error:function(){if(Venus.isArray(servers)&&!callbacks["reconnect"]){serversIndex++;if(serversIndex==servers.length){callbacks.error("Error connecting to any of the provided Venus servers: Is the server down?");return}server=null;setTimeout(function(){createSession(callbacks)},200);return}callbacks.error("Error connecting to the Venus WebSockets server: Is the server down?")},open:function(){transactions[transaction]=function(json){Venus.debug(json);if(json["venus"]!=="success"){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason);callbacks.error(json["error"].reason);return}wsKeepaliveTimeoutId=setTimeout(keepAlive,keepAlivePeriod);connected=true;sessionId=json["session_id"]?json["session_id"]:json.data["id"];if(callbacks["reconnect"]){Venus.log("Claimed session: "+sessionId)}else{Venus.log("Created session: "+sessionId)}Venus.sessions[sessionId]=that;callbacks.success()};ws.send(JSON.stringify(request))},message:function(event){handleEvent(JSON.parse(event.data))},close:function(){if(server===null||!connected){return}connected=false;gatewayCallbacks.error("Lost connection to the server (is it down?)")}};for(var eventName in wsHandlers){ws.addEventListener(eventName,wsHandlers[eventName])}return}Venus.httpAPICall(server,{verb:"POST",withCredentials:withCredentials,body:request,success:function(json){Venus.debug(json);if(json["venus"]!=="success"){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason);callbacks.error(json["error"].reason);return}connected=true;sessionId=json["session_id"]?json["session_id"]:json.data["id"];if(callbacks["reconnect"]){Venus.log("Claimed session: "+sessionId)}else{Venus.log("Created session: "+sessionId)}Venus.sessions[sessionId]=that;eventHandler();callbacks.success()},error:function(textStatus,errorThrown){Venus.error(textStatus+":",errorThrown);if(Venus.isArray(servers)&&!callbacks["reconnect"]){serversIndex++;if(serversIndex==servers.length){callbacks.error("Error connecting to any of the provided Venus servers: Is the server down?");return}server=null;setTimeout(function(){createSession(callbacks)},200);return}if(errorThrown==="")callbacks.error(textStatus+": Is the server down?");else callbacks.error(textStatus+": "+errorThrown)}})}function destroySession(callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;var asyncRequest=true;if(callbacks.asyncRequest!==undefined&&callbacks.asyncRequest!==null)asyncRequest=callbacks.asyncRequest===true;var notifyDestroyed=true;if(callbacks.notifyDestroyed!==undefined&&callbacks.notifyDestroyed!==null)notifyDestroyed=callbacks.notifyDestroyed===true;var cleanupHandles=false;if(callbacks.cleanupHandles!==undefined&&callbacks.cleanupHandles!==null)cleanupHandles=callbacks.cleanupHandles===true;Venus.log("Destroying session "+sessionId+" (async="+asyncRequest+")");if(sessionId===undefined||sessionId===null){Venus.warn("No session to destroy");callbacks.success();if(notifyDestroyed)gatewayCallbacks.destroyed();return}if(cleanupHandles){for(var handleId in pluginHandles)destroyHandle(handleId,{noRequest:true})}if(!connected){Venus.warn("Is the server down? (connected=false)");callbacks.success();return}var request={venus:"destroy",transaction:Venus.randomString(12)};if(token!==null&&token!==undefined)request["token"]=token;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;if(websockets){request["session_id"]=sessionId;var unbindWebSocket=function(){for(var eventName in wsHandlers){ws.removeEventListener(eventName,wsHandlers[eventName])}ws.removeEventListener("message",onUnbindMessage);ws.removeEventListener("error",onUnbindError);if(wsKeepaliveTimeoutId){clearTimeout(wsKeepaliveTimeoutId)}ws.close()};var onUnbindMessage=function(event){var data=JSON.parse(event.data);if(data.session_id==request.session_id&&data.transaction==request.transaction){unbindWebSocket();callbacks.success();if(notifyDestroyed)gatewayCallbacks.destroyed()}};var onUnbindError=function(event){unbindWebSocket();callbacks.error("Failed to destroy the server: Is the server down?");if(notifyDestroyed)gatewayCallbacks.destroyed()};ws.addEventListener("message",onUnbindMessage);ws.addEventListener("error",onUnbindError);ws.send(JSON.stringify(request));return}Venus.httpAPICall(server+"/"+sessionId,{verb:"POST",async:asyncRequest,withCredentials:withCredentials,body:request,success:function(json){Venus.log("Destroyed session:");Venus.debug(json);sessionId=null;connected=false;if(json["venus"]!=="success"){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason)}callbacks.success();if(notifyDestroyed)gatewayCallbacks.destroyed()},error:function(textStatus,errorThrown){Venus.error(textStatus+":",errorThrown);sessionId=null;connected=false;callbacks.success();if(notifyDestroyed)gatewayCallbacks.destroyed()}})}function createHandle(callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;callbacks.consentDialog=typeof callbacks.consentDialog=="function"?callbacks.consentDialog:Venus.noop;callbacks.iceState=typeof callbacks.iceState=="function"?callbacks.iceState:Venus.noop;callbacks.mediaState=typeof callbacks.mediaState=="function"?callbacks.mediaState:Venus.noop;callbacks.webrtcState=typeof callbacks.webrtcState=="function"?callbacks.webrtcState:Venus.noop;callbacks.slowLink=typeof callbacks.slowLink=="function"?callbacks.slowLink:Venus.noop;callbacks.onmessage=typeof callbacks.onmessage=="function"?callbacks.onmessage:Venus.noop;callbacks.onlocalstream=typeof callbacks.onlocalstream=="function"?callbacks.onlocalstream:Venus.noop;callbacks.onremotestream=typeof callbacks.onremotestream=="function"?callbacks.onremotestream:Venus.noop;callbacks.ondata=typeof callbacks.ondata=="function"?callbacks.ondata:Venus.noop;callbacks.ondataopen=typeof callbacks.ondataopen=="function"?callbacks.ondataopen:Venus.noop;callbacks.oncleanup=typeof callbacks.oncleanup=="function"?callbacks.oncleanup:Venus.noop;callbacks.ondetached=typeof callbacks.ondetached=="function"?callbacks.ondetached:Venus.noop;if(!connected){Venus.warn("Is the server down? (connected=false)");callbacks.error("Is the server down? (connected=false)");return}var plugin=callbacks.plugin;if(plugin===undefined||plugin===null){Venus.error("Invalid plugin");callbacks.error("Invalid plugin");return}var opaqueId=callbacks.opaqueId;var handleToken=callbacks.token?callbacks.token:token;var transaction=Venus.randomString(12);var request={venus:"attach",plugin:plugin,opaque_id:opaqueId,transaction:transaction};if(handleToken!==null&&handleToken!==undefined)request["token"]=handleToken;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;if(websockets){transactions[transaction]=function(json){Venus.debug(json);if(json["venus"]!=="success"){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason);callbacks.error("Ooops: "+json["error"].code+" "+json["error"].reason);return}var handleId=json.data["id"];WertcSessionID=handleId;if(loginHandleId==null){loginHandleId=handleId}Venus.log("Created handle: "+handleId);var pluginHandle={session:that,plugin:plugin,id:handleId,token:handleToken,detached:false,webrtcStuff:{started:false,myStream:null,streamExternal:false,remoteStream:null,mySdp:null,mediaConstraints:null,pc:null,dataChannel:{},dtmfSender:null,trickle:true,iceDone:false,volume:{value:null,timer:null},bitrate:{value:null,bsnow:null,bsbefore:null,tsnow:null,tsbefore:null,timer:null}},getPeerconnection:function(){return peerConnect},getId:function(){return handleId},getPlugin:function(){return plugin},getVolume:function(){return getVolume(handleId,true)},getRemoteVolume:function(){return getVolume(handleId,true)},getLocalVolume:function(){return getVolume(handleId,false)},isAudioMuted:function(){return isMuted(handleId,false)},muteAudio:function(){return mute(handleId,false,true)},unmuteAudio:function(){return mute(handleId,false,false)},isVideoMuted:function(){return isMuted(handleId,true)},muteVideo:function(){return mute(handleId,true,true)},unmuteVideo:function(){return mute(handleId,true,false)},getBitrate:function(){return getBitrate(handleId)},send:function(callbacks){sendMessage(handleId,callbacks)},sendWithHandleID:function(handleId,callbacks){sendMessage(handleId,callbacks)},data:function(callbacks){sendData(handleId,callbacks)},dtmf:function(callbacks){sendDtmf(handleId,callbacks)},consentDialog:callbacks.consentDialog,iceState:callbacks.iceState,mediaState:callbacks.mediaState,webrtcState:callbacks.webrtcState,slowLink:callbacks.slowLink,onmessage:callbacks.onmessage,createOffer:function(callbacks){prepareWebrtc(handleId,true,callbacks)},createAnswer:function(callbacks){prepareWebrtc(handleId,false,callbacks)},handleRemoteJsep:function(callbacks){prepareWebrtcPeer(handleId,callbacks)},destroySession:function(callbacks){destroySession(callbacks)},VideoCallGetScreen:function(isLocal,iscomming,callback){VideoCallGetScreen(handleId,isLocal,iscomming,callback)},changeLocalVideoFrameRate:function(width,height,rate,callback){changeLocalVideoFrameRate(width,height,rate,callback)},CloseScreenVideo:function(){CloseScreenVideo()},CloseLocalVideo:function(){CloseLocalVideo()},getVideoInfo:function(callback){getVideoInfo(handleId,callback)},getVideoInfoLocal:function(callback){getVideoInfoLocal(handleId,callback)},getAudioInfo:function(callback){getAudioInfo(handleId,callback)},getAudioInfoLocal:function(callback){getAudioInfoLocal(handleId,callback)},roomDestroyHandle:function(){roomDestroyHandle(handleId)},onlocalstream:callbacks.onlocalstream,onremotestream:callbacks.onremotestream,ondata:callbacks.ondata,ondataopen:callbacks.ondataopen,oncleanup:callbacks.oncleanup,ondetached:callbacks.ondetached,hangup:function(sendRequest){cleanupWebrtc(handleId,sendRequest===true)},detach:function(callbacks){destroyHandle(handleId,callbacks)}};pluginHandles[handleId]=pluginHandle;callbacks.success(pluginHandle)};request["session_id"]=sessionId;ws.send(JSON.stringify(request));return}Venus.httpAPICall(server+"/"+sessionId,{verb:"POST",withCredentials:withCredentials,body:request,success:function(json){Venus.debug(json);if(json["venus"]!=="success"){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason);callbacks.error("Ooops: "+json["error"].code+" "+json["error"].reason);return}var handleId=json.data["id"];if(loginHandleId==null){loginHandleId=handleId}Venus.log("Created handle: "+handleId);var pluginHandle={session:that,plugin:plugin,id:handleId,token:handleToken,detached:false,webrtcStuff:{started:false,myStream:null,streamExternal:false,remoteStream:null,mySdp:null,mediaConstraints:null,pc:null,dataChannel:{},dtmfSender:null,trickle:true,iceDone:false,volume:{value:null,timer:null},bitrate:{value:null,bsnow:null,bsbefore:null,tsnow:null,tsbefore:null,timer:null}},getPeerconnection:function(){return peerConnect},getId:function(){return handleId},getPlugin:function(){return plugin},getVolume:function(){return getVolume(handleId,true)},getRemoteVolume:function(){return getVolume(handleId,true)},getLocalVolume:function(){return getVolume(handleId,false)},isAudioMuted:function(){return isMuted(handleId,false)},muteAudio:function(){return mute(handleId,false,true)},unmuteAudio:function(){return mute(handleId,false,false)},isVideoMuted:function(){return isMuted(handleId,true)},muteVideo:function(){return mute(handleId,true,true)},unmuteVideo:function(){return mute(handleId,true,false)},getBitrate:function(){return getBitrate(handleId)},send:function(callbacks){sendMessage(handleId,callbacks)},sendWithHandleID:function(handleId,callbacks){sendMessage(handleId,callbacks)},data:function(callbacks){sendData(handleId,callbacks)},dtmf:function(callbacks){sendDtmf(handleId,callbacks)},consentDialog:callbacks.consentDialog,iceState:callbacks.iceState,mediaState:callbacks.mediaState,webrtcState:callbacks.webrtcState,slowLink:callbacks.slowLink,onmessage:callbacks.onmessage,createOffer:function(callbacks){prepareWebrtc(handleId,true,callbacks)},createAnswer:function(callbacks){prepareWebrtc(handleId,false,callbacks)},handleRemoteJsep:function(callbacks){prepareWebrtcPeer(handleId,callbacks)},VideoCallGetScreen:function(isLocal,iscomming,callback){VideoCallGetScreen(handleId,isLocal,iscomming,callback)},changeLocalVideoFrameRate:function(width,height,rate,callback){changeLocalVideoFrameRate(width,height,rate,callback)},CloseScreenVideo:function(){CloseScreenVideo()},CloseLocalVideo:function(){CloseLocalVideo()},getVideoInfo:function(callback){getVideoInfo(handleId,callback)},getVideoInfoLocal:function(callback){getVideoInfoLocal(handleId,callback)},getAudioInfo:function(callback){getAudioInfo(handleId,callback)},getAudioInfoLocal:function(callback){getAudioInfoLocal(handleId,callback)},roomDestroyHandle:function(){roomDestroyHandle(handleId)},onlocalstream:callbacks.onlocalstream,onremotestream:callbacks.onremotestream,ondata:callbacks.ondata,ondataopen:callbacks.ondataopen,oncleanup:callbacks.oncleanup,ondetached:callbacks.ondetached,hangup:function(sendRequest){cleanupWebrtc(handleId,sendRequest===true)},detach:function(callbacks){destroyHandle(handleId,callbacks)}};pluginHandles[handleId]=pluginHandle;callbacks.success(pluginHandle)},error:function(textStatus,errorThrown){Venus.error(textStatus+":",errorThrown);callbacks.error(textStatus+":",errorThrown)}})}function getPeerconnecttion(){return config.pc}function sendMessage(handleId,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;if(!connected){Venus.warn("Is the server down? (connected=false)");callbacks.error("Is the server down? (connected=false)");return}var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var message=callbacks.message;var jsep=callbacks.jsep;var transaction=Venus.randomString(12);var request={venus:"message",body:message,transaction:transaction};if(pluginHandle.token!==null&&pluginHandle.token!==undefined)request["token"]=pluginHandle.token;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;if(jsep!==null&&jsep!==undefined)request.jsep=jsep;Venus.debug("Sending message to plugin (handle="+handleId+"):");Venus.debug(request);if(websockets){request["session_id"]=sessionId;request["handle_id"]=handleId;transactions[transaction]=function(json){Venus.debug("Message sent!");Venus.debug(json);if(json["venus"]==="success"){var plugindata=json["plugindata"];if(plugindata===undefined||plugindata===null){Venus.warn("Request succeeded, but missing plugindata...");callbacks.success();return}Venus.log("Synchronous transaction successful ("+plugindata["plugin"]+")");var data=plugindata["data"];Venus.debug(data);callbacks.success(data);console.log("======"+json);var result=data["permanent"];var Room=data["videoroom"];var exitRoom=data["exists"];if(Room=="created"&&result===false){$(document).trigger("CreatSuccess")}else if(Room=="success"&&exitRoom==true){$(document).trigger("RoomExists")}else if(Room=="success"&&exitRoom==false){$(document).trigger("RoomExistsFail")}else if(result==undefined&&Room=="event"){$(document).trigger("CreatFail")}else if(data["info"]!=null&&data["info"]!=undefined){$(document).trigger("Streaming",[data])}else if(data["error_code"]=="445"){}else if(data[""]==""){}return}else if(json["venus"]!=="ack"){if(json["error"]!==undefined&&json["error"]!==null){callbacks.error(json["error"].code+" "+json["error"].reason)}else{Venus.error("Unknown error");callbacks.error("Unknown error")}return}callbacks.success()};ws.send(JSON.stringify(request));return}Venus.httpAPICall(server+"/"+sessionId+"/"+handleId,{verb:"POST",withCredentials:withCredentials,body:request,success:function(json){Venus.debug("Message sent!");Venus.debug(json);if(json["venus"]==="success"){var plugindata=json["plugindata"];if(plugindata===undefined||plugindata===null){Venus.warn("Request succeeded, but missing plugindata...");callbacks.success();return}var plugindata=json["plugindata"];if(plugindata===undefined||plugindata===null){Venus.warn("Missing plugindata...")}else{var data=plugindata["data"];Venus.debug(data);var sender=json["sender"];if(sender===undefined||sender===null){}else{var pluginHandle=pluginHandles[sender];var callback=pluginHandle.onmessage;if(callback!==null&&callback!==undefined){Venus.debug("Notifying application...");callback(data)}else{Venus.debug("No provided notification callback")}}}Venus.log("Synchronous transaction successful ("+plugindata["plugin"]+")");var data=plugindata["data"];Venus.debug(data);callbacks.success(data);console.log("======"+json);var result=data["permanent"];var Room=data["videoroom"];var exitRoom=data["exists"];if(Room=="created"&&result===false){$(document).trigger("CreatSuccess")}else if(Room=="success"&&exitRoom==true){$(document).trigger("RoomExists")}else if(Room=="success"&&exitRoom==false){$(document).trigger("RoomExistsFail")}else if(result==undefined&&Room=="event"){$(document).trigger("CreatFail")}else if(data["info"]!=null&&data["info"]!=undefined){$(document).trigger("Streaming",[data])}else if(data["error_code"]=="445"){}else if(data[""]==""){}return}else if(json["venus"]!=="ack"){if(json["error"]!==undefined&&json["error"]!==null){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason);callbacks.error(json["error"].code+" "+json["error"].reason)}else{Venus.error("Unknown error");callbacks.error("Unknown error")}return}callbacks.success()},error:function(textStatus,errorThrown){callbacks.error(textStatus+": "+errorThrown)}})}function sendTrickleCandidate(handleId,candidate){if(!connected){Venus.warn("Is the server down? (connected=false)");return}var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");return}var request={venus:"trickle",candidate:candidate,transaction:Venus.randomString(12)};if(pluginHandle.token!==null&&pluginHandle.token!==undefined)request["token"]=pluginHandle.token;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;Venus.vdebug("Sending trickle candidate (handle="+handleId+"):");if(websockets){request["session_id"]=sessionId;request["handle_id"]=handleId;ws.send(JSON.stringify(request));return}Venus.httpAPICall(server+"/"+sessionId+"/"+handleId,{verb:"POST",withCredentials:withCredentials,body:request,success:function(json){Venus.vdebug("Candidate sent!");Venus.vdebug(json);if(json["venus"]!=="ack"){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason);return}},error:function(textStatus,errorThrown){Venus.error(textStatus+":",errorThrown)}})}function createDataChannel(handleId,dclabel,incoming,pendingText){var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");return}var config=pluginHandle.webrtcStuff;var onDataChannelMessage=function(event){Venus.log("Received message on data channel:",event);var label=event.target.label;pluginHandle.ondata(event.data,label)};var onDataChannelStateChange=function(event){Venus.log("Received state change on data channel:",event);var label=event.target.label;var dcState=config.dataChannel[label]?config.dataChannel[label].readyState:"null";Venus.log("State change on <"+label+"> data channel: "+dcState);if(dcState==="open"){if(config.dataChannel[label].pending&&config.dataChannel[label].pending.length>0){Venus.log("Sending pending messages on <"+label+">:",config.dataChannel[label].pending.length);for(var i in config.dataChannel[label].pending){var text=config.dataChannel[label].pending[i];Venus.log("Sending string on data channel <"+label+">: "+text);config.dataChannel[label].send(text)}config.dataChannel[label].pending=[]}pluginHandle.ondataopen(label)}};var onDataChannelError=function(error){Venus.error("Got error on data channel:",error)};if(!incoming){config.dataChannel[dclabel]=config.pc.createDataChannel(dclabel,{ordered:false})}else{config.dataChannel[dclabel]=incoming}config.dataChannel[dclabel].onmessage=onDataChannelMessage;config.dataChannel[dclabel].onopen=onDataChannelStateChange;config.dataChannel[dclabel].onclose=onDataChannelStateChange;config.dataChannel[dclabel].onerror=onDataChannelError;config.dataChannel[dclabel].pending=[];if(pendingText)config.dataChannel[dclabel].pending.push(pendingText)}function sendData(handleId,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var config=pluginHandle.webrtcStuff;var text=callbacks.text;if(text===null||text===undefined){Venus.warn("Invalid text");callbacks.error("Invalid text");return}var label=callbacks.label?callbacks.label:Venus.dataChanDefaultLabel;if(!config.dataChannel[label]){createDataChannel(handleId,label,false,text);callbacks.success();return}if(config.dataChannel[label].readyState!=="open"){config.dataChannel[label].pending.push(text);callbacks.success();return}Venus.log("Sending string on data channel <"+label+">: "+text);config.dataChannel[label].send(text);callbacks.success()}function sendDtmf(handleId,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var config=pluginHandle.webrtcStuff;if(config.dtmfSender===null||config.dtmfSender===undefined){if(config.pc!==undefined&&config.pc!==null){var senders=config.pc.getSenders();var audioSender=senders.find(function(sender){return sender.track&&sender.track.kind==="audio"});if(!audioSender){Venus.warn("Invalid DTMF configuration (no audio track)");callbacks.error("Invalid DTMF configuration (no audio track)");return}config.dtmfSender=audioSender.dtmf;if(config.dtmfSender){Venus.log("Created DTMF Sender");config.dtmfSender.ontonechange=function(tone){Venus.debug("Sent DTMF tone: "+tone.tone)}}}if(config.dtmfSender===null||config.dtmfSender===undefined){Venus.warn("Invalid DTMF configuration");callbacks.error("Invalid DTMF configuration");return}}var dtmf=callbacks.dtmf;if(dtmf===null||dtmf===undefined){Venus.warn("Invalid DTMF parameters");callbacks.error("Invalid DTMF parameters");return}var tones=dtmf.tones;if(tones===null||tones===undefined){Venus.warn("Invalid DTMF string");callbacks.error("Invalid DTMF string");return}var duration=dtmf.duration;if(duration===null||duration===undefined)duration=500;var gap=dtmf.gap;if(gap===null||gap===undefined)gap=50;Venus.debug("Sending DTMF string "+tones+" (duration "+duration+"ms, gap "+gap+"ms)");config.dtmfSender.insertDTMF(tones,duration,gap);callbacks.success()}function roomDestroyHandle(handleId){if(loginHandleId!=null){if(handleId!=loginHandleId){destroyHandle(handleId,{noRequest:true})}}}function destroyHandle(handleId,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;var asyncRequest=true;if(callbacks.asyncRequest!==undefined&&callbacks.asyncRequest!==null)asyncRequest=callbacks.asyncRequest===true;var noRequest=true;if(callbacks.noRequest!==undefined&&callbacks.noRequest!==null)noRequest=callbacks.noRequest===true;Venus.log("Destroying handle "+handleId+" (async="+asyncRequest+")");cleanupWebrtc(handleId);var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.detached){delete pluginHandles[handleId];callbacks.success();return}if(noRequest){delete pluginHandles[handleId];callbacks.success();return}if(!connected){Venus.warn("Is the server down? (connected=false)");callbacks.error("Is the server down? (connected=false)");return}var request={venus:"detach",transaction:Venus.randomString(12)};if(pluginHandle.token!==null&&pluginHandle.token!==undefined)request["token"]=pluginHandle.token;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;if(websockets){request["session_id"]=sessionId;request["handle_id"]=handleId;ws.send(JSON.stringify(request));delete pluginHandles[handleId];callbacks.success();return}Venus.httpAPICall(server+"/"+sessionId+"/"+handleId,{verb:"POST",async:asyncRequest,withCredentials:withCredentials,body:request,success:function(json){Venus.log("Destroyed handle:");Venus.debug(json);if(json["venus"]!=="success"){Venus.error("Ooops: "+json["error"].code+" "+json["error"].reason)}delete pluginHandles[handleId];callbacks.success()},error:function(textStatus,errorThrown){Venus.error(textStatus+":",errorThrown);delete pluginHandles[handleId];callbacks.success()}})}function getVideoInfo(handleId,callback){var pluginHandle=pluginHandles[handleId];if(pluginHandle&&pluginHandle.webrtcStuff){var config=pluginHandle.webrtcStuff;if(config.remoteStream&&config.remoteStream.stream){const track=config.remoteStream.stream.getVideoTracks()[0];let results=config.pc.getStats(track,data=>{console.info("data")}).then(function(res){for(var i=0;i<res.length;i++){var res=res[i];if(res.type=="ssrc"){callback.success(res.values)}}}).catch(function(err){console.log(err)})}}}function getVideoInfoLocal(handleId,callback){var pluginHandle=pluginHandles[handleId];if(pluginHandle&&pluginHandle.webrtcStuff){var config=pluginHandle.webrtcStuff;if(config.myStream){const track=config.myStream.getVideoTracks()[0];let results=config.pc.getStats(track,data=>{console.info("data")}).then(results=>{for(var i=0;i<results.length;i++){var res=results[i];if(res.type=="ssrc"){callback.success(res.values)}}}).catch(function(err){console.log(err)})}}}function getAudioInfo(handleId,callback){var pluginHandle=pluginHandles[handleId];if(pluginHandle&&pluginHandle.webrtcStuff){var config=pluginHandle.webrtcStuff;if(config.remoteStream&&config.remoteStream.stream){const track2=config.remoteStream.stream.getAudioTracks()[0];let results=config.pc.getStats(track2,data=>{console.info("data")}).then(results=>{for(var i=0;i<results.length;i++){var res=results[i];if(res.type=="ssrc"){callback.success(res.values)}}}).catch(function(err){console.log(err)})}}}function getAudioInfoLocal(handleId,callback){var pluginHandle=pluginHandles[handleId];if(pluginHandle&&pluginHandle.webrtcStuff){var config=pluginHandle.webrtcStuff;if(config.myStream){const track2=config.myStream.getAudioTracks()[0];let results=config.pc.getStats(track2,data=>{console.info("data")}).then(results=>{for(var i=0;i<results.length;i++){var res=results[i];if(res.type=="ssrc"){callback.success(res.values)}}}).catch(function(err){console.log(err)})}}}function CloseScreenVideo(){if(ScreenStream!=null){ScreenStream.getVideoTracks().forEach(function(track){track.stop()});ScreenStream.getAudioTracks().forEach(function(track){track.stop()});ScreenStream=null}}function CloseLocalVideo(){if(RemberLocalStream!=null){RemberLocalStream.getVideoTracks().forEach(function(track){track.stop()});RemberLocalStream.getAudioTracks().forEach(function(track){track.stop()});RemberLocalStream=null}}function VideoCallGetScreen(handleId,isLocal,isComming,backState){if(!isLocal){var screenMedia=navigator.mediaDevices.getDisplayMedia({video:{width:1280,height:720},audio:true}).then(function(stream){CloseScreenVideo();stream.getTracks().forEach(track=>{track.onended=function(){backState(false)}});navigator.mediaDevices.getUserMedia({audio:true,video:false}).then(function(audioStream){stream.addTrack(audioStream.getAudioTracks()[0]);backState(true);$(document).trigger("screenSuccess");CloseLocalVideo();ScreenStream=stream;localConfig.myStream=stream;videoCallpluginHandle.onlocalstream(stream);var videoTransceiver=null;var audioTransceiver=null;var transceivers=localConfig.pc.getTransceivers();if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="audio"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="audio"){audioTransceiver=t;break}}}if(audioTransceiver&&audioTransceiver.sender){audioTransceiver.sender.replaceTrack(stream.getAudioTracks()[0])}else{config.pc.addTrack(stream.getAudioTracks()[0],stream)}if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="video"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="video"){videoTransceiver=t;break}}}if(videoTransceiver&&videoTransceiver.sender){videoTransceiver.sender.replaceTrack(stream.getVideoTracks()[0])}else{localConfig.addTrack(stream.getVideoTracks()[0],stream)}})},function(error){console.log("==******==");backState(false)},function(canceled){console.log("==stopped==")})}else{backState(false);var gumConstraints;switch(parseInt(localStorage.getItem("resolution"))){case 0:gumConstraints={video:{width:1280,height:720},audio:true};break;case 1:gumConstraints={video:{width:640,height:480},audio:true};break;case 2:gumConstraints={video:{width:3840,height:2160},audio:true};break;case 3:gumConstraints={video:{width:1920,height:1080},audio:true};break;case 4:gumConstraints={video:{width:320,height:240},audio:true};break}if(gumConstraints==undefined||gumConstraints==null){gumConstraints={video:{width:1280,height:720},audio:true}}var videoSource=localStorage.getItem("selectedCamera");gumConstraints.video.deviceId=videoSource?{exact:videoSource}:"default";var audioSource=localStorage.getItem("selectedMic");gumConstraints.audio={deviceId:audioSource?{exact:audioSource}:undefined};navigator.mediaDevices.getUserMedia(gumConstraints).then(function(stream){CloseLocalVideo();var videotrack=stream.getVideoTracks()[0];if(videotrack){videotrack.applyConstraints({frameRate:{max:share_manager.getVideoFrameRate()}})}RemberLocalStream=stream;localConfig.myStream=stream;var videoTransceiver=null;videoCallpluginHandle.onlocalstream(stream);var transceivers=localConfig.pc.getTransceivers();if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="audio"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="audio"){audioTransceiver=t;break}}}if(audioTransceiver&&audioTransceiver.sender){audioTransceiver.sender.replaceTrack(stream.getAudioTracks()[0])}else{config.pc.addTrack(stream.getAudioTracks()[0],stream)}if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="video"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="video"){videoTransceiver=t;break}}}if(videoTransceiver&&videoTransceiver.sender){videoTransceiver.sender.replaceTrack(stream.getVideoTracks()[0])}else{localConfig.addTrack(stream.getVideoTracks()[0],stream)}}).catch(function(error){})}}function changeLocalVideoFrameRate(width,height,Rate){var videoSource=localStorage.getItem("selectedCamera");var audioSource=localStorage.getItem("selectedMic");var gumConstraints={video:{deviceId:videoSource?{exact:videoSource}:"default",width:width,height:height},audio:{deviceId:audioSource?{exact:audioSource}:undefined}};CloseLocalVideo();navigator.mediaDevices.getUserMedia(gumConstraints).then(function(stream){var videotrack=stream.getVideoTracks()[0];if(videotrack){videotrack.applyConstraints({frameRate:{max:Rate}})}RemberLocalStream=stream;localConfig.myStream=stream;var videoTransceiver=null;videoCallpluginHandle.onlocalstream(stream);var transceivers=localConfig.pc.getTransceivers();if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="audio"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="audio"){audioTransceiver=t;break}}}if(audioTransceiver&&audioTransceiver.sender){audioTransceiver.sender.replaceTrack(stream.getAudioTracks()[0])}else{config.pc.addTrack(stream.getAudioTracks()[0],stream)}if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="video"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="video"){videoTransceiver=t;break}}}if(videoTransceiver&&videoTransceiver.sender){videoTransceiver.sender.replaceTrack(stream.getVideoTracks()[0])}else{localConfig.addTrack(stream.getVideoTracks()[0],stream)}}).catch(function(error){})}function streamsDone(handleId,jsep,media,callbacks,stream){var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var config=pluginHandle.webrtcStuff;Venus.log("streamsDone:",stream);if(stream){Venus.debug("  -- Audio tracks:",stream.getAudioTracks());Venus.debug("  -- Video tracks:",stream.getVideoTracks())}var addTracks=false;if(!config.myStream||!media.update||config.streamExternal){config.myStream=stream;addTracks=true}else{if((!media.update&&isAudioSendEnabled(media)||media.update&&(media.addAudio||media.replaceAudio))&&stream.getAudioTracks()&&stream.getAudioTracks().length){config.myStream.addTrack(stream.getAudioTracks()[0]);if(Venus.unifiedPlan){Venus.log((media.replaceAudio?"Replacing":"Adding")+" audio track:",stream.getAudioTracks()[0]);var audioTransceiver=null;var transceivers=config.pc.getTransceivers();if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="audio"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="audio"){audioTransceiver=t;break}}}if(audioTransceiver&&audioTransceiver.sender){audioTransceiver.sender.replaceTrack(stream.getAudioTracks()[0])}else{config.pc.addTrack(stream.getAudioTracks()[0],stream)}}else{Venus.log((media.replaceAudio?"Replacing":"Adding")+" audio track:",stream.getAudioTracks()[0]);config.pc.addTrack(stream.getAudioTracks()[0],stream)}}if((!media.update&&isVideoSendEnabled(media)||media.update&&(media.addVideo||media.replaceVideo))&&stream.getVideoTracks()&&stream.getVideoTracks().length){config.myStream.addTrack(stream.getVideoTracks()[0]);if(Venus.unifiedPlan){Venus.log((media.replaceVideo?"Replacing":"Adding")+" video track:",stream.getVideoTracks()[0]);var videoTransceiver=null;var transceivers=config.pc.getTransceivers();if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="video"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="video"){videoTransceiver=t;break}}}if(videoTransceiver&&videoTransceiver.sender){videoTransceiver.sender.replaceTrack(stream.getVideoTracks()[0])}else{config.pc.addTrack(stream.getVideoTracks()[0],stream)}}else{Venus.log((media.replaceVideo?"Replacing":"Adding")+" video track:",stream.getVideoTracks()[0]);config.pc.addTrack(stream.getVideoTracks()[0],stream)}}}if(!config.pc){var pc_config={iceServers:iceServers,iceTransportPolicy:iceTransportPolicy,bundlePolicy:bundlePolicy};if(Venus.webRTCAdapter.browserDetails.browser==="chrome"){pc_config["sdpSemantics"]=Venus.webRTCAdapter.browserDetails.version<72?"plan-b":"unified-plan"}var pc_constraints={optional:[{DtlsSrtpKeyAgreement:true}]};if(ipv6Support===true){pc_constraints.optional.push({googIPv6:true})}if(callbacks.rtcConstraints&&typeof callbacks.rtcConstraints==="object"){Venus.debug("Adding custom PeerConnection constraints:",callbacks.rtcConstraints);for(var i in callbacks.rtcConstraints){pc_constraints.optional.push(callbacks.rtcConstraints[i])}}if(Venus.webRTCAdapter.browserDetails.browser==="edge"){pc_config.bundlePolicy="max-bundle"}Venus.log("Creating PeerConnection");Venus.debug(pc_constraints);config.pc=new RTCPeerConnection(pc_config,pc_constraints);Venus.debug(config.pc);if(config.pc.getStats){config.volume={};config.bitrate.value="0 kbits/sec"}Venus.log("Preparing local SDP and gathering candidates (trickle="+config.trickle+")");config.pc.oniceconnectionstatechange=function(e){if(config.pc)pluginHandle.iceState(config.pc.iceConnectionState)};config.pc.onicecandidate=function(event){if(event.candidate==null||Venus.webRTCAdapter.browserDetails.browser==="edge"&&event.candidate.candidate.indexOf("endOfCandidates")>0){Venus.log("End of candidates.");config.iceDone=true;if(config.trickle===true){sendTrickleCandidate(handleId,{completed:true})}else{sendSDP(handleId,callbacks)}}else{var candidate={candidate:event.candidate.candidate,sdpMid:event.candidate.sdpMid,sdpMLineIndex:event.candidate.sdpMLineIndex};if(config.trickle===true){sendTrickleCandidate(handleId,candidate)}}};config.pc.onaddstream=function(remoteStream){Venus.log("Handling Remote Stream");Venus.debug(remoteStream);config.remoteStream=remoteStream;pluginHandle.onremotestream(remoteStream.stream,pluginHandles[handleId]);peerConnect=config.pc};config.pc.ontrack=function(event){Venus.log("Handling Remote Track");Venus.debug(event);if(!event.streams)return;config.remoteStream=event.streams[0];pluginHandle.onremotestream(config.remoteStream,pluginHandles[handleId]);if(event.track.onended)return;Venus.log("Adding onended callback to track:",event.track);event.track.onended=function(ev){Venus.log("Remote track muted/removed:",ev);$(document).trigger("videoMute");if(config.remoteStream){}return true};event.track.onmute=event.track.onended;event.track.onunmute=function(ev){Venus.log("Remote track flowing again:",ev);$(document).trigger("videoUnmute");try{console.log(config.remoteStream)}catch(e){Venus.error(e)}return false}}}if(addTracks&&stream!==null&&stream!==undefined){localConfig=config;videoCallpluginHandle=pluginHandle;RemberLocalStream=stream;Venus.log("Adding local stream");var simulcast2=callbacks.simulcast2===true?true:false;stream.getTracks().forEach(function(track){Venus.log("Adding local track:",track);if(!simulcast2){config.pc.addTrack(track,stream)}else{if(track.kind==="audio"){config.pc.addTrack(track,stream)}else{Venus.log("Enabling rid-based simulcasting:",track);const maxBitrates=getMaxBitrates(callbacks.simulcastMaxBitrates);config.pc.addTransceiver(track,{direction:"sendrecv",streams:[stream],sendEncodings:[{rid:"h",active:true,maxBitrate:maxBitrates.high},{rid:"m",active:true,maxBitrate:maxBitrates.medium,scaleResolutionDownBy:2},{rid:"l",active:true,maxBitrate:maxBitrates.low,scaleResolutionDownBy:4}]})}}})}if(isDataEnabled(media)&&!config.dataChannel[Venus.dataChanDefaultLabel]){Venus.log("Creating data channel");createDataChannel(handleId,Venus.dataChanDefaultLabel,false);config.pc.ondatachannel=function(event){Venus.log("Data channel created by Venus:",event);createDataChannel(handleId,event.channel.label,event.channel)}}if(config.myStream)pluginHandle.onlocalstream(config.myStream);if(jsep===null||jsep===undefined){createOffer(handleId,media,callbacks)}else{config.pc.setRemoteDescription(jsep).then(function(){Venus.log("Remote description accepted!");config.remoteSdp=jsep.sdp;if(config.candidates&&config.candidates.length>0){for(var i=0;i<config.candidates.length;i++){var candidate=config.candidates[i];Venus.debug("Adding remote candidate:",candidate);if(!candidate||candidate.completed===true){config.pc.addIceCandidate(Venus.endOfCandidates)}else{config.pc.addIceCandidate(candidate)}}config.candidates=[]}createAnswer(handleId,media,callbacks)},callbacks.error)}}function prepareWebrtc(handleId,offer,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:webrtcError;var jsep=callbacks.jsep;if(offer&&jsep){Venus.error("Provided a JSEP to a createOffer");callbacks.error("Provided a JSEP to a createOffer");return}else if(!offer&&(!jsep||!jsep.type||!jsep.sdp)){Venus.error("A valid JSEP is required for createAnswer");callbacks.error("A valid JSEP is required for createAnswer");return}callbacks.media=callbacks.media||{audio:true,video:true};var media=callbacks.media;var pluginHandle=pluginHandles[handleId];media.update=false;if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var config=pluginHandle.webrtcStuff;config.trickle=isTrickleEnabled(callbacks.trickle);if(config.pc===undefined||config.pc===null){media.update=false;media.keepAudio=false;media.keepVideo=false}else if(config.pc!==undefined&&config.pc!==null){Venus.log("Updating existing media session");media.update=true;if(callbacks.stream!==null&&callbacks.stream!==undefined){if(callbacks.stream!==config.myStream){Venus.log("Renegotiation involves a new external stream")}}else{if(media.addAudio){media.keepAudio=false;media.replaceAudio=false;media.removeAudio=false;media.audioSend=true;if(config.myStream&&config.myStream.getAudioTracks()&&config.myStream.getAudioTracks().length){Venus.error("Can't add audio stream, there already is one");callbacks.error("Can't add audio stream, there already is one");return}}else if(media.removeAudio){media.keepAudio=false;media.replaceAudio=false;media.addAudio=false;media.audioSend=false}else if(media.replaceAudio){media.keepAudio=false;media.addAudio=false;media.removeAudio=false;media.audioSend=true}if(config.myStream===null||config.myStream===undefined){if(media.replaceAudio){media.keepAudio=false;media.replaceAudio=false;media.addAudio=true;media.audioSend=true}if(isAudioSendEnabled(media)){media.keepAudio=false;media.addAudio=true}}else{if(config.myStream.getAudioTracks()===null||config.myStream.getAudioTracks()===undefined||config.myStream.getAudioTracks().length===0){if(media.replaceAudio){media.keepAudio=false;media.replaceAudio=false;media.addAudio=true;media.audioSend=true}if(isAudioSendEnabled(media)){media.keepVideo=false;media.addAudio=true}}else{if(isAudioSendEnabled(media)&&!media.removeAudio&&!media.replaceAudio){media.keepAudio=true}}}if(media.addVideo){media.keepVideo=false;media.replaceVideo=false;media.removeVideo=false;media.videoSend=true;if(config.myStream&&config.myStream.getVideoTracks()&&config.myStream.getVideoTracks().length){Venus.error("Can't add video stream, there already is one");callbacks.error("Can't add video stream, there already is one");return}}else if(media.removeVideo){media.keepVideo=false;media.replaceVideo=false;media.addVideo=false;media.videoSend=false}else if(media.replaceVideo){media.keepVideo=false;media.addVideo=false;media.removeVideo=false;media.videoSend=true}if(config.myStream===null||config.myStream===undefined){if(media.replaceVideo){media.keepVideo=false;media.replaceVideo=false;media.addVideo=true;media.videoSend=true}if(isVideoSendEnabled(media)){media.keepVideo=false;media.addVideo=true}}else{if(config.myStream.getVideoTracks()===null||config.myStream.getVideoTracks()===undefined||config.myStream.getVideoTracks().length===0){if(media.replaceVideo){media.keepVideo=false;media.replaceVideo=false;media.addVideo=true;media.videoSend=true}if(isVideoSendEnabled(media)){media.keepVideo=false;media.addVideo=true}}else{if(isVideoSendEnabled(media)&&!media.removeVideo&&!media.replaceVideo){media.keepVideo=true}}}if(media.addData)media.data=true}if(isAudioSendEnabled(media)&&media.keepAudio&&(isVideoSendEnabled(media)&&media.keepVideo)){pluginHandle.consentDialog(false);media.update=false;streamsDone(handleId,jsep,media,callbacks,config.myStream)}}if(media.update&&!config.streamExternal){if(media.removeAudio||media.replaceAudio){if(config.myStream&&config.myStream.getAudioTracks()&&config.myStream.getAudioTracks().length){var s=config.myStream.getAudioTracks()[0];Venus.log("Removing audio track:",s);config.myStream.removeTrack(s);try{s.stop()}catch(e){}}if(config.pc.getSenders()&&config.pc.getSenders().length){var ra=true;if(media.replaceAudio&&Venus.unifiedPlan){ra=false}if(ra){for(var index in config.pc.getSenders()){var s=config.pc.getSenders()[index];console.log(s);if(s&&s.track&&s.track.kind==="audio"){Venus.log("Removing audio sender:",s);config.pc.removeTrack(s)}}}}}if(media.removeVideo||media.replaceVideo){if(config.myStream&&config.myStream.getVideoTracks()&&config.myStream.getVideoTracks().length){var s=config.myStream.getVideoTracks()[0];Venus.log("Removing video track:",s);config.myStream.removeTrack(s);try{s.stop()}catch(e){}}if(config.pc.getSenders()&&config.pc.getSenders().length){var rv=true;if(media.replaceVideo&&Venus.unifiedPlan){rv=false}if(rv){for(var index in config.pc.getSenders()){var s=config.pc.getSenders()[index];if(s&&s.track&&s.track.kind==="video"){Venus.log("Removing video sender:",s);config.pc.removeTrack(s)}}}}}}if(callbacks.stream!==null&&callbacks.stream!==undefined){var stream=callbacks.stream;Venus.log("MediaStream provided by the application");Venus.debug(stream);if(media.update){if(config.myStream&&config.myStream!==callbacks.stream&&!config.streamExternal){try{var tracks=config.myStream.getTracks();for(var i in tracks){var mst=tracks[i];Venus.log(mst);if(mst!==null&&mst!==undefined)mst.stop()}}catch(e){}config.myStream=null}}config.streamExternal=true;pluginHandle.consentDialog(false);streamsDone(handleId,jsep,media,callbacks,stream);return}if(isAudioSendEnabled(media)||isVideoSendEnabled(media)){if(!Venus.isGetUserMediaAvailable()){callbacks.error("getUserMedia not available");return}var constraints={mandatory:{},optional:[]};pluginHandle.consentDialog(true);var audioSupport=isAudioSendEnabled(media);if(audioSupport===true&&media!=undefined&&media!=null){if(typeof media.audio==="object"){audioSupport=media.audio}}var videoSupport=isVideoSendEnabled(media);if(videoSupport===true&&media!=undefined&&media!=null){var simulcast=callbacks.simulcast===true?true:false;var simulcast2=callbacks.simulcast2===true?true:false;if((simulcast||simulcast2)&&!jsep&&(media.video===undefined||media.video===false))media.video="hires";if(media.video&&media.video!="screen"&&media.video!="window"){if(typeof media.video==="object"){videoSupport=media.video}else{var width=0;var height=0,maxHeight=0;if(media.video==="lowres"){height=180;maxHeight=180;width=320}else if(media.video==="lowres-16:9"){height=180;maxHeight=180;width=320}else if(media.video==="hires"||media.video==="hires-16:9"||media.video==="hdres"){}else if(media.video==="fhdres"){height=1080;maxHeight=1080;width=1920}else if(media.video==="4kres"){height=2160;maxHeight=2160;width=3840}else if(media.video==="stdres"){height=360;maxHeight=360;width=640}else if(media.video==="stdres-16:9"){height=360;maxHeight=360;width=640}else{Venus.log("Default video setting is stdres 4:3");height=360;maxHeight=360;width=640}Venus.log("Adding media constraint:",media.video);videoSupport={height:height,width:width};Venus.log("Adding video constraint:",videoSupport)}}else if(media.video==="screen"||media.video==="window"){if(!media.screenshareFrameRate){media.screenshareFrameRate=3}if(navigator.mediaDevices&&navigator.mediaDevices.getDisplayMedia){navigator.mediaDevices.getDisplayMedia({video:true,audio:media.captureDesktopAudio}).then(function(stream){ScreenStream=stream;$(document).trigger("getMedia",true);pluginHandle.consentDialog(false);if(isAudioSendEnabled(media)){navigator.mediaDevices.getUserMedia({audio:true,video:false}).then(function(audioStream){stream.addTrack(audioStream.getAudioTracks()[0]);streamsDone(handleId,jsep,media,callbacks,stream)})}else{streamsDone(handleId,jsep,media,callbacks,stream)}},function(error){pluginHandle.consentDialog(false);callbacks.error(error)});return}function callbackUserMedia(error,stream){pluginHandle.consentDialog(false);if(error){callbacks.error(error)}else{streamsDone(handleId,jsep,media,callbacks,stream)}}function getScreenMedia(constraints,gsmCallback,useAudio){Venus.log("Adding media constraint (screen capture)");var videoSource=localStorage.getItem("selectedCamera");var audioSource=localStorage.getItem("selectedMic");var audioConf={deviceId:audioSource?{exact:audioSource}:undefined};constraints.video.deviceId=videoSource?{exact:videoSource}:"default";constraints.audio=audioConf;Venus.debug(constraints);navigator.mediaDevices.getUserMedia(constraints).then(function(stream){if(useAudio){navigator.mediaDevices.getUserMedia({audio:audioConf,video:false}).then(function(audioStream){stream.addTrack(audioStream.getAudioTracks()[0]);gsmCallback(null,stream)})}else{gsmCallback(null,stream)}}).catch(function(error){pluginHandle.consentDialog(false);gsmCallback(error)})}if(Venus.webRTCAdapter.browserDetails.browser==="chrome"){var chromever=Venus.webRTCAdapter.browserDetails.version;var maxver=33;if(window.navigator.userAgent.match("Linux"))maxver=35;if(chromever>=26&&chromever<=maxver){constraints={video:{mandatory:{googLeakyBucket:true,maxWidth:window.screen.width,maxHeight:window.screen.height,minFrameRate:media.screenshareFrameRate,maxFrameRate:media.screenshareFrameRate,chromeMediaSource:"screen"}},audio:isAudioSendEnabled(media)&&!media.keepAudio};getScreenMedia(constraints,callbackUserMedia)}else{Venus.extension.getScreen(function(error,sourceId){if(error){pluginHandle.consentDialog(false);return callbacks.error(error)}constraints={audio:false,video:{mandatory:{chromeMediaSource:"desktop",maxWidth:window.screen.width,maxHeight:window.screen.height,minFrameRate:media.screenshareFrameRate,maxFrameRate:media.screenshareFrameRate},optional:[{googLeakyBucket:true},{googTemporalLayeredScreencast:true}]}};constraints.video.mandatory.chromeMediaSourceId=sourceId;getScreenMedia(constraints,callbackUserMedia,isAudioSendEnabled(media)&&!media.keepAudio)})}}else if(Venus.webRTCAdapter.browserDetails.browser==="firefox"){if(Venus.webRTCAdapter.browserDetails.version>=33){constraints={video:{mozMediaSource:media.video,mediaSource:media.video},audio:isAudioSendEnabled(media)&&!media.keepAudio};getScreenMedia(constraints,function(err,stream){callbackUserMedia(err,stream);if(!err){var lastTime=stream.currentTime;var polly=window.setInterval(function(){if(!stream)window.clearInterval(polly);if(stream.currentTime==lastTime){window.clearInterval(polly);if(stream.onended){stream.onended()}}lastTime=stream.currentTime},500)}})}else{var error=new Error("NavigatorUserMediaError");error.name="Your version of Firefox does not support screen sharing, please install Firefox 33 (or more recent versions)";pluginHandle.consentDialog(false);callbacks.error(error);return}}return}}if(media===null||media===undefined||media.video!=="screen"){navigator.mediaDevices.enumerateDevices().then(function(devices){var audioExist=devices.some(function(device){return device.kind==="audioinput"}),videoExist=isScreenSendEnabled(media)||devices.some(function(device){return device.kind==="videoinput"});var audioSend=isAudioSendEnabled(media);var videoSend=isVideoSendEnabled(media);var needAudioDevice=isAudioSendRequired(media);var needVideoDevice=isVideoSendRequired(media);if(audioSend||videoSend||needAudioDevice||needVideoDevice){var haveAudioDevice=audioSend?audioExist:false;var haveVideoDevice=videoSend?videoExist:false;if(!haveAudioDevice&&!haveVideoDevice){pluginHandle.consentDialog(false);callbacks.error("No capture device found");return false}else if(!haveAudioDevice&&needAudioDevice){pluginHandle.consentDialog(false);callbacks.error("Audio capture is required, but no capture device found");return false}}var gumConstraints;switch(parseInt(localStorage.getItem("resolution"))){case 0:gumConstraints={audio:audioExist&&!media.keepAudio?audioSupport:false,video:videoExist&&!media.keepVideo?videoSupport?{width:1280,height:720}:false:false};break;case 1:gumConstraints={audio:audioExist&&!media.keepAudio?audioSupport:false,video:videoExist&&!media.keepVideo?videoSupport?{width:640,height:480}:false:false};break;case 2:gumConstraints={audio:audioExist&&!media.keepAudio?audioSupport:false,video:videoExist&&!media.keepVideo?videoSupport?{width:3840,height:2160}:false:false};break;case 3:gumConstraints={audio:audioExist&&!media.keepAudio?audioSupport:false,video:videoExist&&!media.keepVideo?videoSupport?{width:1920,height:1080}:false:false};break;case 4:gumConstraints={audio:audioExist&&!media.keepAudio?audioSupport:false,video:videoExist&&!media.keepVideo?videoSupport?{width:320,height:240}:false:false};break}if(gumConstraints==undefined||gumConstraints==null){gumConstraints={audio:audioExist&&!media.keepAudio?audioSupport:false,video:videoExist&&!media.keepVideo?videoSupport?{width:1280,height:720}:false:false}}Venus.log("getUserMedia constraints",gumConstraints);if(!gumConstraints.audio&&!gumConstraints.video){pluginHandle.consentDialog(false);streamsDone(handleId,jsep,media,callbacks,stream)}else{getMedia(gumConstraints,handleId,jsep,media,callbacks,stream,audioExist,videoExist,audioSupport,videoSupport,pluginHandle)}}).catch(function(error){pluginHandle.consentDialog(false);callbacks.error("enumerateDevices error",error)})}}else{streamsDone(handleId,jsep,media,callbacks)}}function getMedia(gumConstraints,handleId,jsep,media,callbacks,stream,audioExist,videoExist,audioSupport,videoSupport,pluginHandle){VideoCallmedia=media;$(document).trigger("getMedia",false);var videoSource=localStorage.getItem("selectedCamera");gumConstraints.video.deviceId=videoSource?{exact:videoSource}:"default";var audioSource=localStorage.getItem("selectedMic");gumConstraints.audio={deviceId:audioSource?{exact:audioSource}:undefined};navigator.mediaDevices.getUserMedia(gumConstraints).then(function(stream){var videotrack=stream.getVideoTracks()[0];if(videotrack){videotrack.applyConstraints({frameRate:{max:share_manager.getVideoFrameRate()}})}pluginHandle.consentDialog(false);streamsDone(handleId,jsep,media,callbacks,stream)}).catch(function(error){pluginHandle.consentDialog(false);if(error.name=="OverconstrainedError"){gumConstraints={audio:audioExist&&!media.keepAudio?audioSupport:false,video:videoExist&&!media.keepVideo?videoSupport:false};getMedia(gumConstraints,handleId,jsep,media,callbacks,stream,audioExist,videoExist,audioSupport,videoSupport,pluginHandle)}else{callbacks.error({code:error.code,name:error.name,message:error.message})}})}function prepareWebrtcPeer(handleId,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:webrtcError;var jsep=callbacks.jsep;var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var config=pluginHandle.webrtcStuff;if(jsep!==undefined&&jsep!==null){if(config.pc===null){Venus.warn("Wait, no PeerConnection?? if this is an answer, use createAnswer and not handleRemoteJsep");callbacks.error("No PeerConnection: if this is an answer, use createAnswer and not handleRemoteJsep");return}config.pc.setRemoteDescription(jsep).then(function(){Venus.log("Remote description accepted!");config.remoteSdp=jsep.sdp;if(config.candidates&&config.candidates.length>0){for(var i=0;i<config.candidates.length;i++){var candidate=config.candidates[i];Venus.debug("Adding remote candidate:",candidate);if(!candidate||candidate.completed===true){config.pc.addIceCandidate(Venus.endOfCandidates)}else{config.pc.addIceCandidate(candidate)}}config.candidates=[]}callbacks.success()},callbacks.error)}else{callbacks.error("Invalid JSEP")}}function createOffer(handleId,media,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;callbacks.customizeSdp=typeof callbacks.customizeSdp=="function"?callbacks.customizeSdp:Venus.noop;var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var config=pluginHandle.webrtcStuff;var simulcast=callbacks.simulcast===true?true:false;if(!simulcast){Venus.log("Creating offer (iceDone="+config.iceDone+")")}else{Venus.log("Creating offer (iceDone="+config.iceDone+", simulcast="+simulcast+")")}var mediaConstraints={};if(Venus.unifiedPlan){var audioTransceiver=null,videoTransceiver=null;var transceivers=config.pc.getTransceivers();if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="audio"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="audio"){if(!audioTransceiver)audioTransceiver=t;continue}if(t.sender&&t.sender.track&&t.sender.track.kind==="video"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="video"){if(!videoTransceiver)videoTransceiver=t;continue}}}var audioSend=isAudioSendEnabled(media);var audioRecv=isAudioRecvEnabled(media);if(!audioSend&&!audioRecv){if(media.removeAudio&&audioTransceiver){if(audioTransceiver.setDirection){audioTransceiver.setDirection("inactive")}else{audioTransceiver.direction="inactive"}Venus.log("Setting audio transceiver to inactive:",audioTransceiver)}}else{if(audioSend&&audioRecv){if(audioTransceiver){if(audioTransceiver.setDirection){audioTransceiver.setDirection("sendrecv")}else{audioTransceiver.direction="sendrecv"}Venus.log("Setting audio transceiver to sendrecv:",audioTransceiver)}}else if(audioSend&&!audioRecv){if(audioTransceiver){if(audioTransceiver.setDirection){audioTransceiver.setDirection("sendonly")}else{audioTransceiver.direction="sendonly"}Venus.log("Setting audio transceiver to sendonly:",audioTransceiver)}}else if(!audioSend&&audioRecv){if(audioTransceiver){if(audioTransceiver.setDirection){audioTransceiver.setDirection("recvonly")}else{audioTransceiver.direction="recvonly"}Venus.log("Setting audio transceiver to recvonly:",audioTransceiver)}else{audioTransceiver=config.pc.addTransceiver("audio",{direction:"recvonly"});Venus.log("Adding recvonly audio transceiver:",audioTransceiver)}}}var videoSend=isVideoSendEnabled(media);var videoRecv=isVideoRecvEnabled(media);if(!videoSend&&!videoRecv){if(media.removeVideo&&videoTransceiver){if(videoTransceiver.setDirection){videoTransceiver.setDirection("inactive")}else{videoTransceiver.direction="inactive"}Venus.log("Setting video transceiver to inactive:",videoTransceiver)}}else{if(videoSend&&videoRecv){if(videoTransceiver){if(videoTransceiver.setDirection){videoTransceiver.setDirection("sendrecv")}else{videoTransceiver.direction="sendrecv"}Venus.log("Setting video transceiver to sendrecv:",videoTransceiver)}}else if(videoSend&&!videoRecv){if(videoTransceiver){if(videoTransceiver.setDirection){videoTransceiver.setDirection("sendonly")}else{videoTransceiver.direction="sendonly"}Venus.log("Setting video transceiver to sendonly:",videoTransceiver)}}else if(!videoSend&&videoRecv){if(videoTransceiver){if(videoTransceiver.setDirection){videoTransceiver.setDirection("recvonly")}else{videoTransceiver.direction="recvonly"}Venus.log("Setting video transceiver to recvonly:",videoTransceiver)}else{videoTransceiver=config.pc.addTransceiver("video",{direction:"recvonly"});Venus.log("Adding recvonly video transceiver:",videoTransceiver)}}}}else{mediaConstraints["offerToReceiveAudio"]=isAudioRecvEnabled(media);mediaConstraints["offerToReceiveVideo"]=isVideoRecvEnabled(media)}var iceRestart=callbacks.iceRestart===true?true:false;if(iceRestart){mediaConstraints["iceRestart"]=true}Venus.debug(mediaConstraints);var sendVideo=isVideoSendEnabled(media);if(sendVideo&&simulcast&&Venus.webRTCAdapter.browserDetails.browser==="firefox"){Venus.log("Enabling Simulcasting for Firefox (RID)");var sender=config.pc.getSenders().find(function(s){return s.track.kind=="video"});if(sender){var parameters=sender.getParameters();if(!parameters)parameters={};const maxBitrates=getMaxBitrates(callbacks.simulcastMaxBitrates);parameters.encodings=[{rid:"h",active:true,maxBitrate:maxBitrates.high},{rid:"m",active:true,maxBitrate:maxBitrates.medium,scaleResolutionDownBy:2},{rid:"l",active:true,maxBitrate:maxBitrates.low,scaleResolutionDownBy:4}];sender.setParameters(parameters)}}config.pc.createOffer(mediaConstraints).then(function(offer){var bitrate=parseInt(localStorage.getItem("frameBitrate"));if(bitrate>0){offer.sdp=offer.sdp+"b=AS:"+bitrate+"\r\n"}Venus.debug("create offer:\n"+JSON.stringify(offer));var jsep={type:offer.type,sdp:offer.sdp};callbacks.customizeSdp(jsep);offer.sdp=jsep.sdp;Venus.log("Setting local description");if(sendVideo&&simulcast){if(Venus.webRTCAdapter.browserDetails.browser==="chrome"||Venus.webRTCAdapter.browserDetails.browser==="safari"){Venus.log("Enabling Simulcasting for Chrome (SDP munging)");offer.sdp=mungeSdpForSimulcasting(offer.sdp)}else if(Venus.webRTCAdapter.browserDetails.browser!=="firefox"){Venus.warn("simulcast=true, but this is not Chrome nor Firefox, ignoring")}}config.mySdp=offer.sdp;config.pc.setLocalDescription(offer).catch(callbacks.error);config.mediaConstraints=mediaConstraints;if(!config.iceDone&&!config.trickle){Venus.log("Waiting for all candidates...");return}Venus.log("Offer ready");Venus.debug(callbacks);callbacks.success(offer)},callbacks.error)}function createAnswer(handleId,media,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;callbacks.customizeSdp=typeof callbacks.customizeSdp=="function"?callbacks.customizeSdp:Venus.noop;var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");callbacks.error("Invalid handle");return}var config=pluginHandle.webrtcStuff;var simulcast=callbacks.simulcast===true?true:false;if(!simulcast){Venus.log("Creating answer (iceDone="+config.iceDone+")")}else{Venus.log("Creating answer (iceDone="+config.iceDone+", simulcast="+simulcast+")")}var mediaConstraints=null;if(Venus.unifiedPlan){mediaConstraints={};var audioTransceiver=null,videoTransceiver=null;var transceivers=config.pc.getTransceivers();if(transceivers&&transceivers.length>0){for(var i in transceivers){var t=transceivers[i];if(t.sender&&t.sender.track&&t.sender.track.kind==="audio"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="audio"){if(!audioTransceiver)audioTransceiver=t;continue}if(t.sender&&t.sender.track&&t.sender.track.kind==="video"||t.receiver&&t.receiver.track&&t.receiver.track.kind==="video"){if(!videoTransceiver)videoTransceiver=t;continue}}}var audioSend=isAudioSendEnabled(media);var audioRecv=isAudioRecvEnabled(media);if(!audioSend&&!audioRecv){if(media.removeAudio&&audioTransceiver){try{if(audioTransceiver.setDirection){audioTransceiver.setDirection("inactive")}else{audioTransceiver.direction="inactive"}Venus.log("Setting audio transceiver to inactive:",audioTransceiver)}catch(e){Venus.error(e)}}}else{if(audioSend&&audioRecv){if(audioTransceiver){try{if(audioTransceiver.setDirection){audioTransceiver.setDirection("sendrecv")}else{audioTransceiver.direction="sendrecv"}Venus.log("Setting audio transceiver to sendrecv:",audioTransceiver)}catch(e){Venus.error(e)}}}else if(audioSend&&!audioRecv){try{if(audioTransceiver){if(audioTransceiver.setDirection){audioTransceiver.setDirection("sendonly")}else{audioTransceiver.direction="sendonly"}Venus.log("Setting audio transceiver to sendonly:",audioTransceiver)}}catch(e){Venus.error(e)}}else if(!audioSend&&audioRecv){if(audioTransceiver){try{if(audioTransceiver.setDirection){audioTransceiver.setDirection("recvonly")}else{audioTransceiver.direction="recvonly"}Venus.log("Setting audio transceiver to recvonly:",audioTransceiver)}catch(e){Venus.error(e)}}else{audioTransceiver=config.pc.addTransceiver("audio",{direction:"recvonly"});Venus.log("Adding recvonly audio transceiver:",audioTransceiver)}}}var videoSend=isVideoSendEnabled(media);var videoRecv=isVideoRecvEnabled(media);if(!videoSend&&!videoRecv){if(media.removeVideo&&videoTransceiver){try{if(videoTransceiver.setDirection){videoTransceiver.setDirection("inactive")}else{videoTransceiver.direction="inactive"}Venus.log("Setting video transceiver to inactive:",videoTransceiver)}catch(e){Venus.error(e)}}}else{if(videoSend&&videoRecv){if(videoTransceiver){try{if(videoTransceiver.setDirection){videoTransceiver.setDirection("sendrecv")}else{videoTransceiver.direction="sendrecv"}Venus.log("Setting video transceiver to sendrecv:",videoTransceiver)}catch(e){Venus.error(e)}}}else if(videoSend&&!videoRecv){if(videoTransceiver){try{if(videoTransceiver.setDirection){videoTransceiver.setDirection("sendonly")}else{videoTransceiver.direction="sendonly"}Venus.log("Setting video transceiver to sendonly:",videoTransceiver)}catch(e){Venus.error(e)}}}else if(!videoSend&&videoRecv){if(videoTransceiver){try{if(videoTransceiver.setDirection){videoTransceiver.setDirection("recvonly")}else{videoTransceiver.direction="recvonly"}Venus.log("Setting video transceiver to recvonly:",videoTransceiver)}catch(e){Venus.error(e)}}else{videoTransceiver=config.pc.addTransceiver("video",{direction:"recvonly"});Venus.log("Adding recvonly video transceiver:",videoTransceiver)}}}}else{if(Venus.webRTCAdapter.browserDetails.browser=="firefox"||Venus.webRTCAdapter.browserDetails.browser=="edge"){mediaConstraints={offerToReceiveAudio:isAudioRecvEnabled(media),offerToReceiveVideo:isVideoRecvEnabled(media)}}else{mediaConstraints={mandatory:{OfferToReceiveAudio:isAudioRecvEnabled(media),OfferToReceiveVideo:isVideoRecvEnabled(media)}}}}Venus.debug(mediaConstraints);var sendVideo=isVideoSendEnabled(media);if(sendVideo&&simulcast&&Venus.webRTCAdapter.browserDetails.browser==="firefox"){Venus.log("Enabling Simulcasting for Firefox (RID)");var sender=config.pc.getSenders()[1];Venus.log(sender);var parameters=sender.getParameters();Venus.log(parameters);const maxBitrates=getMaxBitrates(callbacks.simulcastMaxBitrates);sender.setParameters({encodings:[{rid:"high",active:true,priority:"high",maxBitrate:maxBitrates.high},{rid:"medium",active:true,priority:"medium",maxBitrate:maxBitrates.medium},{rid:"low",active:true,priority:"low",maxBitrate:maxBitrates.low}]})}config.pc.createAnswer(mediaConstraints).then(function(answer){Venus.debug(answer);var jsep={type:answer.type,sdp:answer.sdp};callbacks.customizeSdp(jsep);answer.sdp=jsep.sdp;Venus.log("Setting local description");if(sendVideo&&simulcast){if(Venus.webRTCAdapter.browserDetails.browser==="chrome"){Venus.warn("simulcast=true, but this is an answer, and video breaks in Chrome if we enable it")}else if(Venus.webRTCAdapter.browserDetails.browser!=="firefox"){Venus.warn("simulcast=true, but this is not Chrome nor Firefox, ignoring")}}config.mySdp=answer.sdp;config.pc.setLocalDescription(answer).catch(callbacks.error);config.mediaConstraints=mediaConstraints;if(!config.iceDone&&!config.trickle){Venus.log("Waiting for all candidates...");return}callbacks.success(answer)},callbacks.error)}function sendSDP(handleId,callbacks){callbacks=callbacks||{};callbacks.success=typeof callbacks.success=="function"?callbacks.success:Venus.noop;callbacks.error=typeof callbacks.error=="function"?callbacks.error:Venus.noop;var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle, not sending anything");return}var config=pluginHandle.webrtcStuff;Venus.log("Sending offer/answer SDP...");if(config.mySdp===null||config.mySdp===undefined){Venus.warn("Local SDP instance is invalid, not sending anything...");return}config.mySdp={type:config.pc.localDescription.type,sdp:config.pc.localDescription.sdp};if(config.trickle===false)config.mySdp["trickle"]=false;Venus.debug(callbacks);config.sdpSent=true;callbacks.success(config.mySdp)}function getVolume(handleId,remote){var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");return 0}var stream=remote?"remote":"local";var config=pluginHandle.webrtcStuff;if(!config.volume[stream])config.volume[stream]={value:0};if(config.pc.getStats&&Venus.webRTCAdapter.browserDetails.browser==="chrome"){if(remote&&(config.remoteStream===null||config.remoteStream===undefined)){Venus.warn("Remote stream unavailable");return 0}else if(!remote&&(config.myStream===null||config.myStream===undefined)){Venus.warn("Local stream unavailable");return 0}if(config.volume[stream].timer===null||config.volume[stream].timer===undefined){Venus.log("Starting "+stream+" volume monitor");config.volume[stream].timer=setInterval(function(){config.pc.getStats().then(function(stats){var results=stats.result();for(var i=0;i<results.length;i++){var res=results[i];if(res.type=="ssrc"){if(remote&&res.stat("audioOutputLevel"))config.volume[stream].value=parseInt(res.stat("audioOutputLevel"));else if(!remote&&res.stat("audioInputLevel"))config.volume[stream].value=parseInt(res.stat("audioInputLevel"))}}})},200);return 0}return config.volume[stream].value}else{Venus.warn("Getting the "+stream+" volume unsupported by browser");return 0}}function isMuted(handleId,video){var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");return true}var config=pluginHandle.webrtcStuff;if(config.pc===null||config.pc===undefined){Venus.warn("Invalid PeerConnection");return true}if(config.myStream===undefined||config.myStream===null){Venus.warn("Invalid local MediaStream");return true}if(video){if(config.myStream.getVideoTracks()===null||config.myStream.getVideoTracks()===undefined||config.myStream.getVideoTracks().length===0){Venus.warn("No video track");return true}return!config.myStream.getVideoTracks()[0].enabled}else{if(config.myStream.getAudioTracks()===null||config.myStream.getAudioTracks()===undefined||config.myStream.getAudioTracks().length===0){Venus.warn("No audio track");return true}return!config.myStream.getAudioTracks()[0].enabled}}function getMessageInfo(Callback){console.log("====00000===")}function mute(handleId,video,mute){var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");return false}var config=pluginHandle.webrtcStuff;if(config.pc===null||config.pc===undefined){Venus.warn("Invalid PeerConnection");return false}if(config.myStream===undefined||config.myStream===null){Venus.warn("Invalid local MediaStream");return false}if(video){if(config.myStream.getVideoTracks()===null||config.myStream.getVideoTracks()===undefined||config.myStream.getVideoTracks().length===0){Venus.warn("No video track");return false}config.myStream.getVideoTracks()[0].enabled=mute?false:true;return true}else{if(config.myStream.getAudioTracks()===null||config.myStream.getAudioTracks()===undefined||config.myStream.getAudioTracks().length===0){Venus.warn("No audio track");return false}config.myStream.getAudioTracks()[0].enabled=mute?false:true;return true}}function getBitrate(handleId){var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined||pluginHandle.webrtcStuff===null||pluginHandle.webrtcStuff===undefined){Venus.warn("Invalid handle");return"Invalid handle"}var config=pluginHandle.webrtcStuff;if(config.pc===null||config.pc===undefined)return"Invalid PeerConnection";if(config.pc.getStats){if(config.bitrate.timer===null||config.bitrate.timer===undefined){Venus.log("Starting bitrate timer (via getStats)");config.bitrate.timer=setInterval(function(){config.pc.getStats().then(function(stats){stats.forEach(function(res){if(!res)return;var inStats=false;if((res.mediaType==="video"||res.id.toLowerCase().indexOf("video")>-1)&&res.type==="inbound-rtp"&&res.id.indexOf("rtcp")<0){inStats=true}else if(res.type=="ssrc"&&res.bytesReceived&&(res.googCodecName==="VP8"||res.googCodecName==="")){inStats=true}if(inStats){config.bitrate.bsnow=res.bytesReceived;config.bitrate.tsnow=res.timestamp;if(config.bitrate.bsbefore===null||config.bitrate.tsbefore===null){config.bitrate.bsbefore=config.bitrate.bsnow;config.bitrate.tsbefore=config.bitrate.tsnow}else{var timePassed=config.bitrate.tsnow-config.bitrate.tsbefore;if(Venus.webRTCAdapter.browserDetails.browser=="safari")timePassed=timePassed/1e3;var bitRate=Math.round((config.bitrate.bsnow-config.bitrate.bsbefore)*8/timePassed);if(Venus.webRTCAdapter.browserDetails.browser==="safari")bitRate=parseInt(bitRate/1e3);config.bitrate.value=bitRate+" kbits/sec";config.bitrate.bsbefore=config.bitrate.bsnow;config.bitrate.tsbefore=config.bitrate.tsnow}}})})},1e3);return"0 kbits/sec"}return config.bitrate.value}else{Venus.warn("Getting the video bitrate unsupported by browser");return"Feature unsupported by browser"}}function webrtcError(error){}function cleanupWebrtc(handleId,hangupRequest){Venus.log("Cleaning WebRTC stuff");var pluginHandle=pluginHandles[handleId];if(pluginHandle===null||pluginHandle===undefined){return}var config=pluginHandle.webrtcStuff;if(config!==null&&config!==undefined){if(hangupRequest===true){var request={venus:"hangup",transaction:Venus.randomString(12)};if(pluginHandle.token!==null&&pluginHandle.token!==undefined)request["token"]=pluginHandle.token;if(apisecret!==null&&apisecret!==undefined)request["apisecret"]=apisecret;Venus.debug("Sending hangup request (handle="+handleId+"):");Venus.debug(request);if(websockets){request["session_id"]=sessionId;request["handle_id"]=handleId;ws.send(JSON.stringify(request))}else{Venus.httpAPICall(server+"/"+sessionId+"/"+handleId,{verb:"POST",withCredentials:withCredentials,body:request})}}config.remoteStream=null;if(config.volume){if(config.volume["local"]&&config.volume["local"].timer)clearInterval(config.volume["local"].timer);if(config.volume["remote"]&&config.volume["remote"].timer)clearInterval(config.volume["remote"].timer)}config.volume={};if(config.bitrate.timer)clearInterval(config.bitrate.timer);config.bitrate.timer=null;config.bitrate.bsnow=null;config.bitrate.bsbefore=null;config.bitrate.tsnow=null;config.bitrate.tsbefore=null;config.bitrate.value=null;try{if(!config.streamExternal&&config.myStream!==null&&config.myStream!==undefined){Venus.log("Stopping local stream tracks");var tracks=config.myStream.getTracks();for(var i in tracks){var mst=tracks[i];Venus.log(mst);if(mst!==null&&mst!==undefined)mst.stop()}}}catch(e){}config.streamExternal=false;config.myStream=null;try{config.pc.close()}catch(e){}config.pc=null;config.candidates=null;config.mySdp=null;config.remoteSdp=null;config.iceDone=false;config.dataChannel={};config.dtmfSender=null}pluginHandle.oncleanup()}function mungeSdpForSimulcasting(sdp){var lines=sdp.split("\r\n");var video=false;var ssrc=[-1],ssrc_fid=[-1];var cname=null,msid=null,mslabel=null,label=null;var insertAt=-1;for(var i=0;i<lines.length;i++){var mline=lines[i].match(/m=(\w+) */);if(mline){var medium=mline[1];if(medium==="video"){if(ssrc[0]<0){video=true}else{insertAt=i;break}}else{if(ssrc[0]>-1){insertAt=i;break}}continue}if(!video)continue;var fid=lines[i].match(/a=ssrc-group:FID (\d+) (\d+)/);if(fid){ssrc[0]=fid[1];ssrc_fid[0]=fid[2];lines.splice(i,1);i--;continue}if(ssrc[0]){var match=lines[i].match("a=ssrc:"+ssrc[0]+" cname:(.+)");if(match){cname=match[1]}match=lines[i].match("a=ssrc:"+ssrc[0]+" msid:(.+)");if(match){msid=match[1]}match=lines[i].match("a=ssrc:"+ssrc[0]+" mslabel:(.+)");if(match){mslabel=match[1]}match=lines[i].match("a=ssrc:"+ssrc[0]+" label:(.+)");if(match){label=match[1]}if(lines[i].indexOf("a=ssrc:"+ssrc_fid[0])===0){lines.splice(i,1);i--;continue}if(lines[i].indexOf("a=ssrc:"+ssrc[0])===0){lines.splice(i,1);i--;continue}}if(lines[i].length==0){lines.splice(i,1);i--;continue}}if(ssrc[0]<0){insertAt=-1;video=false;for(var i=0;i<lines.length;i++){var mline=lines[i].match(/m=(\w+) */);if(mline){var medium=mline[1];if(medium==="video"){if(ssrc[0]<0){video=true}else{insertAt=i;break}}else{if(ssrc[0]>-1){insertAt=i;break}}continue}if(!video)continue;if(ssrc[0]<0){var value=lines[i].match(/a=ssrc:(\d+)/);if(value){ssrc[0]=value[1];lines.splice(i,1);i--;continue}}else{var match=lines[i].match("a=ssrc:"+ssrc[0]+" cname:(.+)");if(match){cname=match[1]}match=lines[i].match("a=ssrc:"+ssrc[0]+" msid:(.+)");if(match){msid=match[1]}match=lines[i].match("a=ssrc:"+ssrc[0]+" mslabel:(.+)");if(match){mslabel=match[1]}match=lines[i].match("a=ssrc:"+ssrc[0]+" label:(.+)");if(match){label=match[1]}if(lines[i].indexOf("a=ssrc:"+ssrc_fid[0])===0){lines.splice(i,1);i--;continue}if(lines[i].indexOf("a=ssrc:"+ssrc[0])===0){lines.splice(i,1);i--;continue}}if(lines[i].length==0){lines.splice(i,1);i--;continue}}}if(ssrc[0]<0){Venus.warn("Couldn't find the video SSRC, simulcasting NOT enabled");return sdp}if(insertAt<0){insertAt=lines.length}ssrc[1]=Math.floor(Math.random()*4294967295);ssrc[2]=Math.floor(Math.random()*4294967295);ssrc_fid[1]=Math.floor(Math.random()*4294967295);ssrc_fid[2]=Math.floor(Math.random()*4294967295);for(var i=0;i<ssrc.length;i++){if(cname){lines.splice(insertAt,0,"a=ssrc:"+ssrc[i]+" cname:"+cname);insertAt++}if(msid){lines.splice(insertAt,0,"a=ssrc:"+ssrc[i]+" msid:"+msid);insertAt++}if(mslabel){lines.splice(insertAt,0,"a=ssrc:"+ssrc[i]+" mslabel:"+mslabel);insertAt++}if(label){lines.splice(insertAt,0,"a=ssrc:"+ssrc[i]+" label:"+label);insertAt++}if(cname){lines.splice(insertAt,0,"a=ssrc:"+ssrc_fid[i]+" cname:"+cname);insertAt++}if(msid){lines.splice(insertAt,0,"a=ssrc:"+ssrc_fid[i]+" msid:"+msid);insertAt++}if(mslabel){lines.splice(insertAt,0,"a=ssrc:"+ssrc_fid[i]+" mslabel:"+mslabel);insertAt++}if(label){lines.splice(insertAt,0,"a=ssrc:"+ssrc_fid[i]+" label:"+label);insertAt++}}lines.splice(insertAt,0,"a=ssrc-group:FID "+ssrc[2]+" "+ssrc_fid[2]);lines.splice(insertAt,0,"a=ssrc-group:FID "+ssrc[1]+" "+ssrc_fid[1]);lines.splice(insertAt,0,"a=ssrc-group:FID "+ssrc[0]+" "+ssrc_fid[0]);lines.splice(insertAt,0,"a=ssrc-group:SIM "+ssrc[0]+" "+ssrc[1]+" "+ssrc[2]);sdp=lines.join("\r\n");if(!sdp.endsWith("\r\n"))sdp+="\r\n";return sdp}function getPeer(media){return config.pc}function isAudioSendEnabled(media){Venus.debug("isAudioSendEnabled:",media);if(media===undefined||media===null)return true;if(media.audio===false)return false;if(media.audioSend===undefined||media.audioSend===null)return true;return media.audioSend===true}function isAudioSendRequired(media){Venus.debug("isAudioSendRequired:",media);if(media===undefined||media===null)return false;if(media.audio===false||media.audioSend===false)return false;if(media.failIfNoAudio===undefined||media.failIfNoAudio===null)return false;return media.failIfNoAudio===true}function isAudioRecvEnabled(media){Venus.debug("isAudioRecvEnabled:",media);if(media===undefined||media===null)return true;if(media.audio===false)return false;if(media.audioRecv===undefined||media.audioRecv===null)return true;return media.audioRecv===true}function isVideoSendEnabled(media){Venus.debug("isVideoSendEnabled:",media);if(media===undefined||media===null)return true;if(media.video===false)return false;if(media.videoSend===undefined||media.videoSend===null)return true;return media.videoSend===true}function isVideoSendRequired(media){Venus.debug("isVideoSendRequired:",media);if(media===undefined||media===null)return false;if(media.video===false||media.videoSend===false)return false;if(media.failIfNoVideo===undefined||media.failIfNoVideo===null)return false;return media.failIfNoVideo===true}function isVideoRecvEnabled(media){Venus.debug("isVideoRecvEnabled:",media);if(media===undefined||media===null)return true;if(media.video===false)return false;if(media.videoRecv===undefined||media.videoRecv===null)return true;return media.videoRecv===true}function isScreenSendEnabled(media){Venus.debug("isScreenSendEnabled:",media);if(media===undefined||media===null)return false;if(typeof media.video!=="object"||typeof media.video.mandatory!=="object")return false;var constraints=media.video.mandatory;if(constraints.chromeMediaSource)return constraints.chromeMediaSource==="desktop"||constraints.chromeMediaSource==="screen";else if(constraints.mozMediaSource)return constraints.mozMediaSource==="window"||constraints.mozMediaSource==="screen";else if(constraints.mediaSource)return constraints.mediaSource==="window"||constraints.mediaSource==="screen";return false}function isDataEnabled(media){Venus.debug("isDataEnabled:",media);if(Venus.webRTCAdapter.browserDetails.browser=="edge"){Venus.warn("Edge doesn't support data channels yet");return false}if(media===undefined||media===null)return false;return media.data===true}function isTrickleEnabled(trickle){Venus.debug("isTrickleEnabled:",trickle);if(trickle===undefined||trickle===null)return true;return trickle===true}}!function(e,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((e||self).openImSdk={})}(this,function(e){var n,t,r=function(){function e(){this.events=void 0,this.events={}}var n=e.prototype;return n.emit=function(e,n){return this.events[e]&&this.events[e].forEach(function(e){return e(n)}),this},n.on=function(e,n){return this.events[e]?this.events[e].push(n):this.events[e]=[n],this},n.off=function(e,n){if(e&&"function"==typeof n){var t=this.events[e],r=t.findIndex(function(e){return e===n});t.splice(r,1)}else this.events[e]=[];return this},e}();function u(){return u=Object.assign||function(e){for(var n=1;n<arguments.length;n++){var t=arguments[n];for(var r in t)Object.prototype.hasOwnProperty.call(t,r)&&(e[r]=t[r])}return e},u.apply(this,arguments)}function i(e,n){return i=Object.setPrototypeOf||function(e,n){return e.__proto__=n,e},i(e,n)}e.RequestFunc=void 0,(n=e.RequestFunc||(e.RequestFunc={})).INITSDK="InitSDK",n.LOGIN="Login",n.LOGOUT="Logout",n.GETLOGINSTATUS="GetLoginStatus",n.GETLOGINUSER="GetLoginUser",n.GETSELFUSERINFO="GetSelfUserInfo",n.CREATETEXTMESSAGE="CreateTextMessage",n.CREATETEXTATMESSAGE="CreateTextAtMessage",n.CREATEADVANCEDTEXTMESSAGE="CreateAdvancedTextMessage",n.CREATEIMAGEMESSAGEFROMBYURL="CreateImageMessageByURL",n.CREATESOUNDMESSAGEBYURL="CreateSoundMessageByURL",n.CREATEVIDEOMESSAGEBYURL="CreateVideoMessageByURL",n.CREATEFILEMESSAGEBYURL="CreateFileMessageByURL",n.CREATEIMAGEMESSAGEFROMFULLPATH="CreateImageMessageFromFullPath",n.CREATESOUNDMESSAGEFROMFULLPATH="CreateSoundMessageFromFullPath",n.CREATEVIDEOMESSAGEFROMFULLPATH="CreateVideoMessageFromFullPath",n.CREATEFILEMESSAGEFROMFULLPATH="CreateFileMessageFromFullPath",n.CREATELOCATIONMESSAGE="CreateLocationMessage",n.CREATECUSTOMMESSAGE="CreateCustomMessage",n.CREATEMERGERMESSAGE="CreateMergerMessage",n.CREATEFORWARDMESSAGE="CreateForwardMessage",n.CREATEQUOTEMESSAGE="CreateQuoteMessage",n.CREATEADVANCEDQUOTEMESSAGE="CreateAdvancedQuoteMessage",n.CREATECARDMESSAGE="CreateCardMessage",n.CREATEFACEMESSAGE="CreateFaceMessage",n.SENDMESSAGE="SendMessage",n.SENDMESSAGENOTOSS="SendMessageNotOss",n.GETHISTORYMESSAGELIST="GetHistoryMessageList",n.GETADVANCEDHISTORYMESSAGELIST="GetAdvancedHistoryMessageList",n.GETHISTORYMESSAGELISTREVERSE="GetHistoryMessageListReverse",n.REVOKEMESSAGE="RevokeMessage",n.SETONECONVERSATIONPRIVATECHAT="SetOneConversationPrivateChat",n.DELETEMESSAGEFROMLOCALSTORAGE="DeleteMessageFromLocalStorage",n.DELETEMESSAGEFROMLOCALANDSVR="DeleteMessageFromLocalAndSvr",n.DELETECONVERSATIONFROMLOCALANDSVR="DeleteConversationFromLocalAndSvr",n.DELETEALLCONVERSATIONFROMLOCAL="DeleteAllConversationFromLocal",n.DELETEALLMSGFROMLOCALANDSVR="DeleteAllMsgFromLocalAndSvr",n.DELETEALLMSGFROMLOCAL="DeleteAllMsgFromLocal",n.MARKSINGLEMESSAGEHASREAD="MarkSingleMessageHasRead",n.INSERTSINGLEMESSAGETOLOCALSTORAGE="InsertSingleMessageToLocalStorage",n.INSERTGROUPMESSAGETOLOCALSTORAGE="InsertGroupMessageToLocalStorage",n.TYPINGSTATUSUPDATE="TypingStatusUpdate",n.MARKC2CMESSAGEASREAD="MarkC2CMessageAsRead",n.MARKMESSAGEASREADBYCONID="MarkMessageAsReadByConID",n.CLEARC2CHISTORYMESSAGE="ClearC2CHistoryMessage",n.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR="ClearC2CHistoryMessageFromLocalAndSvr",n.CLEARGROUPHISTORYMESSAGE="ClearGroupHistoryMessage",n.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR="ClearGroupHistoryMessageFromLocalAndSvr",n.ADDFRIEND="AddFriend",n.SEARCHFRIENDS="SearchFriends",n.GETDESIGNATEDFRIENDSINFO="GetDesignatedFriendsInfo",n.GETRECVFRIENDAPPLICATIONLIST="GetRecvFriendApplicationList",n.GETSENDFRIENDAPPLICATIONLIST="GetSendFriendApplicationList",n.GETFRIENDLIST="GetFriendList",n.SETFRIENDREMARK="SetFriendRemark",n.ADDBLACK="AddBlack",n.GETBLACKLIST="GetBlackList",n.REMOVEBLACK="RemoveBlack",n.CHECKFRIEND="CheckFriend",n.ACCEPTFRIENDAPPLICATION="AcceptFriendApplication",n.REFUSEFRIENDAPPLICATION="RefuseFriendApplication",n.DELETEFRIEND="DeleteFriend",n.GETUSERSINFO="GetUsersInfo",n.SETSELFINFO="SetSelfInfo",n.GETALLCONVERSATIONLIST="GetAllConversationList",n.GETCONVERSATIONLISTSPLIT="GetConversationListSplit",n.GETONECONVERSATION="GetOneConversation",n.GETCONVERSATIONIDBYSESSIONTYPE="GetConversationIDBySessionType",n.GETMULTIPLECONVERSATION="GetMultipleConversation",n.DELETECONVERSATION="DeleteConversation",n.SETCONVERSATIONDRAFT="SetConversationDraft",n.PINCONVERSATION="PinConversation",n.GETTOTALUNREADMSGCOUNT="GetTotalUnreadMsgCount",n.GETCONVERSATIONRECVMESSAGEOPT="GetConversationRecvMessageOpt",n.SETCONVERSATIONRECVMESSAGEOPT="SetConversationRecvMessageOpt",n.SEARCHLOCALMESSAGES="SearchLocalMessages",n.MARKGROUPMESSAGEHASREAD="MarkGroupMessageHasRead",n.MARKGROUPMESSAGEASREAD="MarkGroupMessageAsRead",n.INVITEUSERTOGROUP="InviteUserToGroup",n.KICKGROUPMEMBER="KickGroupMember",n.GETGROUPMEMBERSINFO="GetGroupMembersInfo",n.GETGROUPMEMBERLIST="GetGroupMemberList",n.GETGROUPMEMBERLISTBYJOINTIMEFILTER="GetGroupMemberListByJoinTimeFilter",n.SEARCHGROUPMEMBERS="SearchGroupMembers",n.SETGROUPAPPLYMEMBERFRIEND="SetGroupApplyMemberFriend",n.SETGROUPLOOKMEMBERINFO="SetGroupLookMemberInfo",n.GETJOINEDGROUPLIST="GetJoinedGroupList",n.CREATEGROUP="CreateGroup",n.SETGROUPINFO="SetGroupInfo",n.SETGROUPMEMBERNICKNAME="SetGroupMemberNickname",n.GETGROUPSINFO="GetGroupsInfo",n.JOINGROUP="JoinGroup",n.SEARCHGROUPS="SearchGroups",n.QUITGROUP="QuitGroup",n.DISMISSGROUP="DismissGroup",n.CHANGEGROUPMUTE="ChangeGroupMute",n.CHANGEGROUPMEMBERMUTE="ChangeGroupMemberMute",n.TRANSFERGROUPOWNER="TransferGroupOwner",n.GETSENDGROUPAPPLICATIONLIST="GetSendGroupApplicationList",n.GETRECVGROUPAPPLICATIONLIST="GetRecvGroupApplicationList",n.ACCEPTGROUPAPPLICATION="AcceptGroupApplication",n.REFUSEGROUPAPPLICATION="RefuseGroupApplication",n.SIGNAL_INGINVITE="SignalingInvite",n.SIGNALINGINVITEINGROUP="SignalingInviteInGroup",n.SIGNALINGACCEPT="SignalingAccept",n.SIGNALINGREJECT="SignalingReject",n.SIGNALINGCANCEL="SignalingCancel",n.SIGNALINGHUNGUP="SignalingHungUp",n.GETSUBDEPARTMENT="GetSubDepartment",n.GETDEPARTMENTMEMBER="GetDepartmentMember",n.GETUSERINDEPARTMENT="GetUserInDepartment",n.GETDEPARTMENTMEMBERANDSUBDEPARTMENT="GetDepartmentMemberAndSubDepartment",n.GETDEPARTMENTINFO="GetDepartmentInfo",n.SEARCHORGANIZATION="SearchOrganization",n.RESETCONVERSATIONGROUPATTYPE="ResetConversationGroupAtType",n.SETGROUPMEMBERROLELEVEL="SetGroupMemberRoleLevel",n.SETGROUPVERIFICATION="SetGroupVerification",n.SETGLOBALRECVMESSAGEOPT="SetGlobalRecvMessageOpt",n.NEWREVOKEMESSAGE="NewRevokeMessage",n.FINDMESSAGELIST="FindMessageList",e.CbEvents=void 0,(t=e.CbEvents||(e.CbEvents={})).ONCONNECTFAILED="OnConnectFailed",t.ONCONNECTSUCCESS="OnConnectSuccess",t.ONCONNECTING="OnConnecting",t.ONKICKEDOFFLINE="OnKickedOffline",t.ONSELFINFOUPDATED="OnSelfInfoUpdated",t.ONUSERTOKENEXPIRED="OnUserTokenExpired",t.ONPROGRESS="OnProgress",t.ONRECVNEWMESSAGE="OnRecvNewMessage",t.ONRECVNEWMESSAGES="OnRecvNewMessages",t.ONRECVMESSAGEREVOKED="OnRecvMessageRevoked",t.ONRECVC2CREADRECEIPT="OnRecvC2CReadReceipt",t.ONRECVGROUPREADRECEIPT="OnRecvGroupReadReceipt",t.ONCONVERSATIONCHANGED="OnConversationChanged",t.ONNEWCONVERSATION="OnNewConversation",t.ONSYNCSERVERFAILED="OnSyncServerFailed",t.ONSYNCSERVERFINISH="OnSyncServerFinish",t.ONSYNCSERVERSTART="OnSyncServerStart",t.ONTOTALUNREADMESSAGECOUNTCHANGED="OnTotalUnreadMessageCountChanged",t.ONBLACKADDED="OnBlackAdded",t.ONBLACKDELETED="OnBlackDeleted",t.ONFRIENDAPPLICATIONACCEPTED="OnFriendApplicationAccepted",t.ONFRIENDAPPLICATIONADDED="OnFriendApplicationAdded",t.ONFRIENDAPPLICATIONDELETED="OnFriendApplicationDeleted",t.ONFRIENDAPPLICATIONREJECTED="OnFriendApplicationRejected",t.ONFRIENDINFOCHANGED="OnFriendInfoChanged",t.ONFRIENDADDED="OnFriendAdded",t.ONFRIENDDELETED="OnFriendDeleted",t.ONJOINEDGROUPADDED="OnJoinedGroupAdded",t.ONJOINEDGROUPDELETED="OnJoinedGroupDeleted",t.ONGROUPMEMBERADDED="OnGroupMemberAdded",t.ONGROUPMEMBERDELETED="OnGroupMemberDeleted",t.ONGROUPAPPLICATIONADDED="OnGroupApplicationAdded",t.ONGROUPAPPLICATIONDELETED="OnGroupApplicationDeleted",t.ONGROUPINFOCHANGED="OnGroupInfoChanged",t.ONGROUPMEMBERINFOCHANGED="OnGroupMemberInfoChanged",t.ONGROUPAPPLICATIONACCEPTED="OnGroupApplicationAccepted",t.ONGROUPAPPLICATIONREJECTED="OnGroupApplicationRejected",t.ONRECEIVENEWINVITATION="OnReceiveNewInvitation",t.ONINVITEEACCEPTED="OnInviteeAccepted",t.ONINVITEEREJECTED="OnInviteeRejected",t.ONINVITATIONCANCELLED="OnInvitationCancelled",t.ONHANGUP="OnHangUp",t.ONINVITATIONTIMEOUT="OnInvitationTimeout",t.ONINVITEEACCEPTEDBYOTHERDEVICE="OnInviteeAcceptedByOtherDevice",t.ONINVITEEREJECTEDBYOTHERDEVICE="OnInviteeRejectedByOtherDevice",t.ONORGANIZATIONUPDATED="OnOrganizationUpdated",t.ONRECVNEWMESSAGEFROMOTHERWEB="OnRecvNewMessageFromOtherWeb",t.ONNEWRECVMESSAGEREVOKED="OnNewRecvMessageRevoked";var o,a,s,E,c,S,I,d,R,A,N,O,D=function(e){try{e&&e.terminate()}catch(e){console.log(e)}},T=function(e){return(36*Math.random()).toString(36).slice(2)+(new Date).getTime().toString()+e},G=function(n){var t,r;function o(){var t;return(t=n.call(this)||this).ws=void 0,t.uid=void 0,t.token=void 0,t.platform="web",t.wsUrl="",t.lock=!1,t.logoutFlag=!1,t.ws2promise={},t.onceFlag=!0,t.timer=void 0,t.lastTime=0,t.heartbeatCount=0,t.heartbeatStartTime=0,t.platformID=0,t.isBatch=!1,t.worker=null,t.getLoginStatus=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETLOGINSTATUS,operationID:i,userID:t.uid,data:""},r,u)})},t.getLoginUser=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETLOGINUSER,operationID:i,userID:t.uid,data:""},r,u)})},t.getSelfUserInfo=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSELFUSERINFO,operationID:i,userID:t.uid,data:""},r,u)})},t.getUsersInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETUSERSINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.setSelfInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETSELFINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.createTextMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATETEXTMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createTextAtMessage=function(n,r){return new Promise(function(i,o){var a=u({},n);a.atUserIDList=JSON.stringify(a.atUserIDList),a.atUsersInfo=JSON.stringify(a.atUsersInfo);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATETEXTATMESSAGE,operationID:s,userID:t.uid,data:a},i,o)})},t.createAdvancedTextMessage=function(n,r){return new Promise(function(i,o){var a=u({},n);a.messageEntityList=JSON.stringify(a.messageEntityList);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEADVANCEDTEXTMESSAGE,operationID:s,userID:t.uid,data:a},i,o)})},t.createImageMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.bigPicture=JSON.stringify(s.bigPicture),s.snapshotPicture=JSON.stringify(s.snapshotPicture),s.sourcePicture=JSON.stringify(s.sourcePicture);var E={reqFuncName:e.RequestFunc.CREATEIMAGEMESSAGEFROMBYURL,operationID:a,userID:t.uid,data:JSON.stringify(s)};t.wsSend(E,i,o)})},t.createSoundMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={soundBaseInfo:JSON.stringify(n)},s={reqFuncName:e.RequestFunc.CREATESOUNDMESSAGEBYURL,operationID:o,userID:t.uid,data:JSON.stringify(a)};t.wsSend(s,u,i)})},t.createVideoMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={videoBaseInfo:JSON.stringify(n)},s={reqFuncName:e.RequestFunc.CREATEVIDEOMESSAGEBYURL,operationID:o,userID:t.uid,data:JSON.stringify(a)};t.wsSend(s,u,i)})},t.createFileMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={fileBaseInfo:JSON.stringify(n)},s={reqFuncName:e.RequestFunc.CREATEFILEMESSAGEBYURL,operationID:o,userID:t.uid,data:JSON.stringify(a)};t.wsSend(s,u,i)})},t.createFileMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEFILEMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createImageMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEIMAGEMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createSoundMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATESOUNDMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createVideoMessageFromFullPath=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEVIDEOMESSAGEFROMFULLPATH,operationID:o,userID:t.uid,data:n},u,i)})},t.createMergerMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.messageList=JSON.stringify(n.messageList),s.summaryList=JSON.stringify(n.summaryList),t.wsSend({reqFuncName:e.RequestFunc.CREATEMERGERMESSAGE,operationID:a,userID:t.uid,data:s},i,o)})},t.createForwardMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEFORWARDMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createFaceMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEFACEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createLocationMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATELOCATIONMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createCustomMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATECUSTOMMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createQuoteMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATEQUOTEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.createAdvancedQuoteMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.messageEntityList=JSON.stringify(s.messageEntityList),t.wsSend({reqFuncName:e.RequestFunc.CREATEADVANCEDQUOTEMESSAGE,operationID:a,userID:t.uid,data:s},i,o)})},t.createCardMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CREATECARDMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.sendMessage=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.offlinePushInfo=s.offlinePushInfo?JSON.stringify(n.offlinePushInfo):"",t.wsSend({reqFuncName:e.RequestFunc.SENDMESSAGE,operationID:a,userID:t.uid,data:s},i,o)})},t.sendMessageNotOss=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.offlinePushInfo=s.offlinePushInfo?JSON.stringify(n.offlinePushInfo):"",t.wsSend({reqFuncName:e.RequestFunc.SENDMESSAGENOTOSS,operationID:a,userID:t.uid,data:s},i,o)})},t.getHistoryMessageList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETHISTORYMESSAGELIST,operationID:o,userID:t.uid,data:n},u,i)})},t.getAdvancedHistoryMessageList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETADVANCEDHISTORYMESSAGELIST,operationID:o,userID:t.uid,data:n},u,i)})},t.getHistoryMessageListReverse=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETHISTORYMESSAGELISTREVERSE,operationID:o,userID:t.uid,data:n},u,i)})},t.revokeMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REVOKEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.setOneConversationPrivateChat=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETONECONVERSATIONPRIVATECHAT,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteMessageFromLocalStorage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEMESSAGEFROMLOCALSTORAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteMessageFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEMESSAGEFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteConversationFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETECONVERSATIONFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteAllConversationFromLocal=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEALLCONVERSATIONFROMLOCAL,operationID:i,userID:t.uid,data:""},r,u)})},t.deleteAllMsgFromLocal=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEALLMSGFROMLOCAL,operationID:i,userID:t.uid,data:""},r,u)})},t.deleteAllMsgFromLocalAndSvr=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEALLMSGFROMLOCALANDSVR,operationID:i,userID:t.uid,data:""},r,u)})},t.markGroupMessageHasRead=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.MARKGROUPMESSAGEHASREAD,operationID:o,userID:t.uid,data:n},u,i)})},t.markGroupMessageAsRead=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.msgIDList=JSON.stringify(s.msgIDList),t.wsSend({reqFuncName:e.RequestFunc.MARKGROUPMESSAGEASREAD,operationID:a,userID:t.uid,data:s},i,o)})},t.insertSingleMessageToLocalStorage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.INSERTSINGLEMESSAGETOLOCALSTORAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.insertGroupMessageToLocalStorage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.INSERTGROUPMESSAGETOLOCALSTORAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.typingStatusUpdate=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.TYPINGSTATUSUPDATE,operationID:o,userID:t.uid,data:n},u,i)})},t.markC2CMessageAsRead=function(n,r){return new Promise(function(i,o){var a=u({},n);a.msgIDList=JSON.stringify(a.msgIDList);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.MARKC2CMESSAGEASREAD,operationID:s,userID:t.uid,data:a},i,o)})},t.markNotifyMessageHasRead=function(e,n){t.markMessageAsReadByConID({conversationID:e,msgIDList:[]})},t.markMessageAsReadByConID=function(n,r){return new Promise(function(i,o){var a=u({},n);a.msgIDList=JSON.stringify(a.msgIDList);var s=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.MARKMESSAGEASREADBYCONID,operationID:s,userID:t.uid,data:a},i,o)})},t.clearC2CHistoryMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARC2CHISTORYMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.clearC2CHistoryMessageFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARC2CHISTORYMESSAGEFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.clearGroupHistoryMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARGROUPHISTORYMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.clearGroupHistoryMessageFromLocalAndSvr=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CLEARGROUPHISTORYMESSAGEFROMLOCALANDSVR,operationID:o,userID:t.uid,data:n},u,i)})},t.getAllConversationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETALLCONVERSATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getConversationListSplit=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETCONVERSATIONLISTSPLIT,operationID:o,userID:t.uid,data:n},u,i)})},t.getOneConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETONECONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.getConversationIDBySessionType=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETCONVERSATIONIDBYSESSIONTYPE,operationID:o,userID:t.uid,data:n},u,i)})},t.getMultipleConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETMULTIPLECONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETECONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.setConversationDraft=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETCONVERSATIONDRAFT,operationID:o,userID:t.uid,data:n},u,i)})},t.pinConversation=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.PINCONVERSATION,operationID:o,userID:t.uid,data:n},u,i)})},t.getTotalUnreadMsgCount=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETTOTALUNREADMSGCOUNT,operationID:i,userID:t.uid,data:""},r,u)})},t.getConversationRecvMessageOpt=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETCONVERSATIONRECVMESSAGEOPT,operationID:o,userID:t.uid,data:n},u,i)})},t.setConversationRecvMessageOpt=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.conversationIDList=JSON.stringify(n.conversationIDList),t.wsSend({reqFuncName:e.RequestFunc.SETCONVERSATIONRECVMESSAGEOPT,operationID:a,userID:t.uid,data:s},i,o)})},t.searchLocalMessages=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SEARCHLOCALMESSAGES,operationID:o,userID:t.uid,data:n},u,i)})},t.addFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ADDFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.searchFriends=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SEARCHFRIENDS,operationID:o,userID:t.uid,data:n},u,i)})},t.getDesignatedFriendsInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDESIGNATEDFRIENDSINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.getRecvFriendApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETRECVFRIENDAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getSendFriendApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSENDFRIENDAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getFriendList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETFRIENDLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.setFriendRemark=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETFRIENDREMARK,operationID:o,userID:t.uid,data:n},u,i)})},t.checkFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CHECKFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.acceptFriendApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ACCEPTFRIENDAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.refuseFriendApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REFUSEFRIENDAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.deleteFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DELETEFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.addBlack=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ADDBLACK,operationID:o,userID:t.uid,data:n},u,i)})},t.removeBlack=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REMOVEBLACK,operationID:o,userID:t.uid,data:n},u,i)})},t.getBlackList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETBLACKLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.inviteUserToGroup=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.userIDList=JSON.stringify(s.userIDList),t.wsSend({reqFuncName:e.RequestFunc.INVITEUSERTOGROUP,operationID:a,userID:t.uid,data:s},i,o)})},t.kickGroupMember=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.userIDList=JSON.stringify(s.userIDList),t.wsSend({reqFuncName:e.RequestFunc.KICKGROUPMEMBER,operationID:a,userID:t.uid,data:s},i,o)})},t.getGroupMembersInfo=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.userIDList=JSON.stringify(s.userIDList),t.wsSend({reqFuncName:e.RequestFunc.GETGROUPMEMBERSINFO,operationID:a,userID:t.uid,data:s},i,o)})},t.getGroupMemberList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETGROUPMEMBERLIST,operationID:o,userID:t.uid,data:n},u,i)})},t.getGroupMemberListByJoinTimeFilter=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.filterUserIDList=JSON.stringify(s.filterUserIDList),t.wsSend({reqFuncName:e.RequestFunc.GETGROUPMEMBERLISTBYJOINTIMEFILTER,operationID:a,userID:t.uid,data:s},i,o)})},t.searchGroupMembers=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={reqFuncName:e.RequestFunc.SEARCHGROUPMEMBERS,operationID:o,userID:t.uid,data:{searchParam:JSON.stringify(n)}};t.wsSend(a,u,i)})},t.setGroupApplyMemberFriend=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPAPPLYMEMBERFRIEND,operationID:o,userID:t.uid,data:n},u,i)})},t.setGroupLookMemberInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPLOOKMEMBERINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.getJoinedGroupList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETJOINEDGROUPLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.createGroup=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.groupBaseInfo=JSON.stringify(s.groupBaseInfo),s.memberList=JSON.stringify(s.memberList),t.wsSend({reqFuncName:e.RequestFunc.CREATEGROUP,operationID:a,userID:t.uid,data:s},i,o)})},t.setGroupInfo=function(n,r){return new Promise(function(i,o){var a=r||T(t.uid),s=u({},n);s.groupInfo=JSON.stringify(s.groupInfo),t.wsSend({reqFuncName:e.RequestFunc.SETGROUPINFO,operationID:a,userID:t.uid,data:s},i,o)})},t.setGroupMemberNickname=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPMEMBERNICKNAME,operationID:o,userID:t.uid,data:n},u,i)})},t.getGroupsInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETGROUPSINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.joinGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.JOINGROUP,operationID:o,userID:t.uid,data:n},u,i)})},t.searchGroups=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SEARCHGROUPS,operationID:o,userID:t.uid,data:n},u,i)})},t.quitGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.QUITGROUP,operationID:o,userID:t.uid,data:n},u,i)})},t.dismissGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.DISMISSGROUP,operationID:o,userID:t.uid,data:n},u,i)})},t.changeGroupMute=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CHANGEGROUPMUTE,operationID:o,userID:t.uid,data:n},u,i)})},t.changeGroupMemberMute=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.CHANGEGROUPMEMBERMUTE,operationID:o,userID:t.uid,data:n},u,i)})},t.transferGroupOwner=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.TRANSFERGROUPOWNER,operationID:o,userID:t.uid,data:n},u,i)})},t.getSendGroupApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSENDGROUPAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.getRecvGroupApplicationList=function(n){return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETRECVGROUPAPPLICATIONLIST,operationID:i,userID:t.uid,data:""},r,u)})},t.acceptGroupApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.ACCEPTGROUPAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.refuseGroupApplication=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.REFUSEGROUPAPPLICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingInvite=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={};a.invitation=n,t.wsSend({reqFuncName:e.RequestFunc.SIGNAL_INGINVITE,operationID:o,userID:t.uid,data:a},u,i)})},t.signalingInviteInGroup=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a={};a.invitation=n,t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGINVITEINGROUP,operationID:o,userID:t.uid,data:a},u,i)})},t.signalingAccept=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGACCEPT,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingReject=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGREJECT,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingCancel=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGCANCEL,operationID:o,userID:t.uid,data:n},u,i)})},t.signalingHungUp=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SIGNALINGHUNGUP,operationID:o,userID:t.uid,data:n},u,i)})},t.getSubDepartment=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETSUBDEPARTMENT,operationID:o,userID:t.uid,data:n},u,i)})},t.getDepartmentMember=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDEPARTMENTMEMBER,operationID:o,userID:t.uid,data:n},u,i)})},t.getUserInDepartment=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETUSERINDEPARTMENT,operationID:o,userID:t.uid,data:n},u,i)})},t.getDepartmentMemberAndSubDepartment=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDEPARTMENTMEMBERANDSUBDEPARTMENT,operationID:o,userID:t.uid,data:n},u,i)})},t.getDepartmentInfo=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.GETDEPARTMENTINFO,operationID:o,userID:t.uid,data:n},u,i)})},t.searchOrganization=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid),a=n;a.input=JSON.stringify(a.input),t.wsSend({reqFuncName:e.RequestFunc.SEARCHORGANIZATION,operationID:o,userID:t.uid,data:a},u,i)})},t.resetConversationGroupAtType=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.RESETCONVERSATIONGROUPATTYPE,operationID:o,userID:t.uid,data:n},u,i)})},t.setGroupMemberRoleLevel=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPMEMBERROLELEVEL,operationID:o,userID:t.uid,data:n},u,i)})},t.setGroupVerification=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGROUPVERIFICATION,operationID:o,userID:t.uid,data:n},u,i)})},t.setGlobalRecvMessageOpt=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.SETGLOBALRECVMESSAGEOPT,operationID:o,userID:t.uid,data:n},u,i)})},t.newRevokeMessage=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.NEWREVOKEMESSAGE,operationID:o,userID:t.uid,data:n},u,i)})},t.findMessageList=function(n,r){return new Promise(function(u,i){var o=r||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.FINDMESSAGELIST,operationID:o,userID:t.uid,data:n},u,i)})},t.wsSend=function(n,r,u){var i,o,a;if(null==(i=window)||!i.navigator||window.navigator.onLine)if((null==(o=t.ws)?void 0:o.readyState)===(null==(a=t.ws)?void 0:a.OPEN)){"object"==typeof n.data&&(n.data=JSON.stringify(n.data));var s={oid:n.operationID||T(t.uid),mname:n.reqFuncName,mrsve:r,mrjet:u,flag:!1};t.ws2promise[s.oid]=s;var E=function(n){t.lastTime=(new Date).getTime();var r=JSON.parse(n.data);if(e.CbEvents[r.event.toUpperCase()])t.emit(r.event,r);else{r.event===e.RequestFunc.LOGOUT&&t.ws2promise[r.operationID]&&(t.logoutFlag=!0,t.ws.close(),t.ws=void 0);var u=t.ws2promise[r.operationID];u?(0===r.errCode?u.mrsve(r):u.mrjet(r),delete t.ws2promise[r.operationID]):r.event!==e.RequestFunc.SENDMESSAGE&&r.event!==e.RequestFunc.SENDMESSAGENOTOSS||t.emit(e.CbEvents.ONRECVNEWMESSAGEFROMOTHERWEB,r)}};try{"web"==t.platform?(t.ws.send(JSON.stringify(n)),t.ws.onmessage=E):(t.ws.send({data:JSON.stringify(n),success:function(e){"uni"===t.platform&&void 0!==t.ws._callbacks&&void 0!==t.ws._callbacks.message&&(t.ws._callbacks.message=[])}}),t.onceFlag&&(t.ws.onMessage(E),t.onceFlag=!1))}catch(e){return void u({event:n.reqFuncName,errCode:112,errMsg:"no ws conect...",data:"",operationID:n.operationID||""})}n.reqFuncName===e.RequestFunc.LOGOUT&&(t.onceFlag=!0)}else u({event:n.reqFuncName,errCode:112,errMsg:"ws conecting...",data:"",operationID:n.operationID||""});else u({event:n.reqFuncName,errCode:113,errMsg:"net work error",data:"",operationID:n.operationID||""})},t.getPlatform(),t}r=n,(t=o).prototype=Object.create(r.prototype),t.prototype.constructor=t,i(t,r);var a=o.prototype;return a.login=function(n){var t=this;return new Promise(function(r,u){var i=n.userID,o=n.token,a=n.platformID,s=n.isBatch,E=void 0!==s&&s,c=n.operationID;t.wsUrl=n.url+"?sendID="+i+"&token="+o+"&platformID="+a,t.platformID=a;var S={userID:i,token:o},I={event:e.RequestFunc.LOGIN,errCode:0,errMsg:"",data:"",operationID:c||""};t.createWs(function(){t.uid=i,t.token=o,t.isBatch=E,t.iLogin(S,c).then(function(e){t.logoutFlag=!1,t.heartbeat(),r(e)}).catch(function(e){I.errCode=e.errCode,I.errMsg=e.errMsg,u(I)})},function(){I.errCode=111,I.errMsg="ws connect close...",t.logoutFlag||Object.values(t.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})}),u(I)},function(e){console.log(e),I.errCode=112,I.errMsg="ws connect error...",u(I)}),t.ws||(I.errCode=112,I.errMsg="The current platform is not supported...",u(I))})},a.iLogin=function(n,t){var r=this;return new Promise(function(u,i){var o=t||T(r.uid);r.wsSend({reqFuncName:e.RequestFunc.LOGIN,operationID:o,userID:r.uid,data:n,batchMsg:r.isBatch?1:0},u,i)})},a.logout=function(n){var t=this;return new Promise(function(r,u){var i=n||T(t.uid);t.wsSend({reqFuncName:e.RequestFunc.LOGOUT,operationID:i,userID:t.uid,data:""},r,u)})},a.getPlatform=function(){var e=typeof WebSocket,n=typeof uni,t=typeof wx;"undefined"===e?("object"===t&&(this.platform="wx"),"object"===n&&(this.platform="uni"),this.platform="unknow"):this.platform="web"},a.createWs=function(e,n,t){var r=this;return console.log("start createWs..."),new Promise(function(u,i){var o;null==(o=r.ws)||o.close(),r.ws=void 0;var a=function(){r.iLogin({userID:r.uid,token:r.token}).then(function(e){r.logoutFlag=!1,console.log("iLogin suc..."),r.heartbeat(),u()})};e&&(a=e);var s=function(){console.log("ws close agin:::"),r.logoutFlag||Object.values(r.ws2promise).forEach(function(e){return e.mrjet({event:e.mname,errCode:111,errMsg:"ws connect close...",data:"",operationID:e.oid})})};n&&(s=n);var E=function(){};if(t&&(E=t),"web"===r.platform)return r.ws=new WebSocket(r.wsUrl),r.ws.onclose=s,r.ws.onopen=a,void(r.ws.onerror=E);var c="uni"===r.platform?uni:wx;r.ws=c.connectSocket({url:r.wsUrl,complete:function(){}}),r.ws.onClose(s),r.ws.onOpen(a),r.ws.onError(E)})},a.reconnect=function(){var e=this;this.onceFlag||(this.onceFlag=!0),this.lock||(this.lock=!0,this.clearTimer(),this.timer=setTimeout(function(){e.createWs(),e.lock=!1},500))},a.clearTimer=function(){this.timer&&clearTimeout(this.timer)},a.heartbeat=function(){var e,n,t,r,u=this;console.log("start heartbeat..."),this.clearTimer(),this.worker&&D(this.worker);try{this.worker=(e=function(){var e,n,t,r;u.logoutFlag?u.worker&&D(u.worker):(null==(e=u.ws)?void 0:e.readyState)===(null==(n=u.ws)?void 0:n.CONNECTING)||(null==(t=u.ws)?void 0:t.readyState)===(null==(r=u.ws)?void 0:r.OPEN)?(new Date).getTime()-u.lastTime<9e3||u.getLoginStatus().catch(function(e){return u.reconnect()}):u.reconnect()},n=new Blob(["(function (e) {\n      setInterval(function () {\n        this.postMessage(null)\n      }, 10000)\n    })()"]),t=window.URL.createObjectURL(n),(r=new Worker(t)).onmessage=e,r)}catch(e){}},o}(r);e.OptType=void 0,(o=e.OptType||(e.OptType={}))[o.Nomal=0]="Nomal",o[o.Mute=1]="Mute",o[o.WithoutNotify=2]="WithoutNotify",e.AllowType=void 0,(a=e.AllowType||(e.AllowType={}))[a.Allowed=0]="Allowed",a[a.NotAllowed=1]="NotAllowed",e.GroupType=void 0,(s=e.GroupType||(e.GroupType={}))[s.NomalGroup=0]="NomalGroup",s[s.SuperGroup=1]="SuperGroup",s[s.WorkingGroup=2]="WorkingGroup",e.GroupVerificationType=void 0,(E=e.GroupVerificationType||(e.GroupVerificationType={}))[E.ApplyNeedInviteNot=0]="ApplyNeedInviteNot",E[E.AllNeed=1]="AllNeed",E[E.AllNot=2]="AllNot",e.GroupStatus=void 0,(c=e.GroupStatus||(e.GroupStatus={}))[c.Nomal=0]="Nomal",c[c.Baned=1]="Baned",c[c.Dismissed=2]="Dismissed",c[c.Muted=3]="Muted",e.GroupJoinSource=void 0,(S=e.GroupJoinSource||(e.GroupJoinSource={}))[S.Invitation=2]="Invitation",S[S.Search=3]="Search",S[S.QrCode=4]="QrCode",e.GroupRole=void 0,(I=e.GroupRole||(e.GroupRole={}))[I.Nomal=1]="Nomal",I[I.Owner=2]="Owner",I[I.Admin=3]="Admin",e.GroupAtType=void 0,(d=e.GroupAtType||(e.GroupAtType={}))[d.AtNormal=0]="AtNormal",d[d.AtMe=1]="AtMe",d[d.AtAll=2]="AtAll",d[d.AtAllAtMe=3]="AtAllAtMe",d[d.AtGroupNotice=4]="AtGroupNotice",e.MessageStatus=void 0,(R=e.MessageStatus||(e.MessageStatus={}))[R.Sending=1]="Sending",R[R.Succeed=2]="Succeed",R[R.Failed=3]="Failed",e.Platform=void 0,(A=e.Platform||(e.Platform={}))[A.iOS=1]="iOS",A[A.Android=2]="Android",A[A.Windows=3]="Windows",A[A.MacOSX=4]="MacOSX",A[A.Web=5]="Web",A[A.Linux=7]="Linux",A[A.Admin=8]="Admin",e.MessageType=void 0,(N=e.MessageType||(e.MessageType={}))[N.TEXTMESSAGE=101]="TEXTMESSAGE",N[N.PICTUREMESSAGE=102]="PICTUREMESSAGE",N[N.VOICEMESSAGE=103]="VOICEMESSAGE",N[N.VIDEOMESSAGE=104]="VIDEOMESSAGE",N[N.FILEMESSAGE=105]="FILEMESSAGE",N[N.ATTEXTMESSAGE=106]="ATTEXTMESSAGE",N[N.MERGERMESSAGE=107]="MERGERMESSAGE",N[N.CARDMESSAGE=108]="CARDMESSAGE",N[N.LOCATIONMESSAGE=109]="LOCATIONMESSAGE",N[N.CUSTOMMESSAGE=110]="CUSTOMMESSAGE",N[N.REVOKEMESSAGE=111]="REVOKEMESSAGE",N[N.HASREADRECEIPTMESSAGE=112]="HASREADRECEIPTMESSAGE",N[N.TYPINGMESSAGE=113]="TYPINGMESSAGE",N[N.QUOTEMESSAGE=114]="QUOTEMESSAGE",N[N.FACEMESSAGE=115]="FACEMESSAGE",N[N.ADVANCETEXTMESSAGE=117]="ADVANCETEXTMESSAGE",N[N.ADVANCEREVOKEMESSAGE=118]="ADVANCEREVOKEMESSAGE",N[N.CUSTOMMSGNOTTRIGGERCONVERSATION=119]="CUSTOMMSGNOTTRIGGERCONVERSATION",N[N.CUSTOMMSGONLINEONLY=120]="CUSTOMMSGONLINEONLY",N[N.FRIENDAPPLICATIONAPPROVED=1201]="FRIENDAPPLICATIONAPPROVED",N[N.FRIENDAPPLICATIONREJECTED=1202]="FRIENDAPPLICATIONREJECTED",N[N.FRIENDAPPLICATIONADDED=1203]="FRIENDAPPLICATIONADDED",N[N.FRIENDADDED=1204]="FRIENDADDED",N[N.FRIENDDELETED=1205]="FRIENDDELETED",N[N.FRIENDREMARKSET=1206]="FRIENDREMARKSET",N[N.BLACKADDED=1207]="BLACKADDED",N[N.BLACKDELETED=1208]="BLACKDELETED",N[N.SELFINFOUPDATED=1303]="SELFINFOUPDATED",N[N.NOTIFICATION=1400]="NOTIFICATION",N[N.GROUPCREATED=1501]="GROUPCREATED",N[N.GROUPINFOUPDATED=1502]="GROUPINFOUPDATED",N[N.JOINGROUPAPPLICATIONADDED=1503]="JOINGROUPAPPLICATIONADDED",N[N.MEMBERQUIT=1504]="MEMBERQUIT",N[N.GROUPAPPLICATIONACCEPTED=1505]="GROUPAPPLICATIONACCEPTED",N[N.GROUPAPPLICATIONREJECTED=1506]="GROUPAPPLICATIONREJECTED",N[N.GROUPOWNERTRANSFERRED=1507]="GROUPOWNERTRANSFERRED",N[N.MEMBERKICKED=1508]="MEMBERKICKED",N[N.MEMBERINVITED=1509]="MEMBERINVITED",N[N.MEMBERENTER=1510]="MEMBERENTER",N[N.GROUPDISMISSED=1511]="GROUPDISMISSED",N[N.GROUPMEMBERMUTED=1512]="GROUPMEMBERMUTED",N[N.GROUPMEMBERCANCELMUTED=1513]="GROUPMEMBERCANCELMUTED",N[N.GROUPMUTED=1514]="GROUPMUTED",N[N.GROUPCANCELMUTED=1515]="GROUPCANCELMUTED",N[N.GROUPMEMBERINFOUPDATED=1516]="GROUPMEMBERINFOUPDATED",N[N.BURNMESSAGECHANGE=1701]="BURNMESSAGECHANGE",e.SessionType=void 0,(O=e.SessionType||(e.SessionType={}))[O.Single=1]="Single",O[O.Group=2]="Group",O[O.SuperGroup=3]="SuperGroup",O[O.Notification=4]="Notification",e.OpenIMSDK=G,e.emitter=r,e.uuid=T});(function(f){if(typeof exports==="object"&&typeof module!=="undefined"){module.exports=f()}else if(typeof define==="function"&&define.amd){define([],f)}else{var g;if(typeof window!=="undefined"){g=window}else if(typeof global!=="undefined"){g=global}else if(typeof self!=="undefined"){g=self}else{g=this}g.adapter=f()}})(function(){var define,module,exports;return function(){function r(e,n,t){function o(i,f){if(!n[i]){if(!e[i]){var c="function"==typeof require&&require;if(!f&&c)return c(i,!0);if(u)return u(i,!0);var a=new Error("Cannot find module '"+i+"'");throw a.code="MODULE_NOT_FOUND",a}var p=n[i]={exports:{}};e[i][0].call(p.exports,function(r){var n=e[i][1][r];return o(n||r)},p,p.exports,r,e,n,t)}return n[i].exports}for(var u="function"==typeof require&&require,i=0;i<t.length;i++)o(t[i]);return o}return r}()({1:[function(require,module,exports){"use strict";var SDPUtils=require("sdp");function fixStatsType(stat){return{inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[stat.type]||stat.type}function writeMediaSection(transceiver,caps,type,stream,dtlsRole){var sdp=SDPUtils.writeRtpDescription(transceiver.kind,caps);sdp+=SDPUtils.writeIceParameters(transceiver.iceGatherer.getLocalParameters());sdp+=SDPUtils.writeDtlsParameters(transceiver.dtlsTransport.getLocalParameters(),type==="offer"?"actpass":dtlsRole||"active");sdp+="a=mid:"+transceiver.mid+"\r\n";if(transceiver.rtpSender&&transceiver.rtpReceiver){sdp+="a=sendrecv\r\n"}else if(transceiver.rtpSender){sdp+="a=sendonly\r\n"}else if(transceiver.rtpReceiver){sdp+="a=recvonly\r\n"}else{sdp+="a=inactive\r\n"}if(transceiver.rtpSender){var trackId=transceiver.rtpSender._initialTrackId||transceiver.rtpSender.track.id;transceiver.rtpSender._initialTrackId=trackId;var msid="msid:"+(stream?stream.id:"-")+" "+trackId+"\r\n";sdp+="a="+msid;sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" "+msid;if(transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" "+msid;sdp+="a=ssrc-group:FID "+transceiver.sendEncodingParameters[0].ssrc+" "+transceiver.sendEncodingParameters[0].rtx.ssrc+"\r\n"}}sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" cname:"+SDPUtils.localCName+"\r\n";if(transceiver.rtpSender&&transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" cname:"+SDPUtils.localCName+"\r\n"}return sdp}function filterIceServers(iceServers,edgeVersion){var hasTurn=false;iceServers=JSON.parse(JSON.stringify(iceServers));return iceServers.filter(function(server){if(server&&(server.urls||server.url)){var urls=server.urls||server.url;if(server.url&&!server.urls){console.warn("RTCIceServer.url is deprecated! Use urls instead.")}var isString=typeof urls==="string";if(isString){urls=[urls]}urls=urls.filter(function(url){var validTurn=url.indexOf("turn:")===0&&url.indexOf("transport=udp")!==-1&&url.indexOf("turn:[")===-1&&!hasTurn;if(validTurn){hasTurn=true;return true}return url.indexOf("stun:")===0&&edgeVersion>=14393&&url.indexOf("?transport=udp")===-1});delete server.url;server.urls=isString?urls[0]:urls;return!!urls.length}})}function getCommonCapabilities(localCapabilities,remoteCapabilities){var commonCapabilities={codecs:[],headerExtensions:[],fecMechanisms:[]};var findCodecByPayloadType=function(pt,codecs){pt=parseInt(pt,10);for(var i=0;i<codecs.length;i++){if(codecs[i].payloadType===pt||codecs[i].preferredPayloadType===pt){return codecs[i]}}};var rtxCapabilityMatches=function(lRtx,rRtx,lCodecs,rCodecs){var lCodec=findCodecByPayloadType(lRtx.parameters.apt,lCodecs);var rCodec=findCodecByPayloadType(rRtx.parameters.apt,rCodecs);return lCodec&&rCodec&&lCodec.name.toLowerCase()===rCodec.name.toLowerCase()};localCapabilities.codecs.forEach(function(lCodec){for(var i=0;i<remoteCapabilities.codecs.length;i++){var rCodec=remoteCapabilities.codecs[i];if(lCodec.name.toLowerCase()===rCodec.name.toLowerCase()&&lCodec.clockRate===rCodec.clockRate){if(lCodec.name.toLowerCase()==="rtx"&&lCodec.parameters&&rCodec.parameters.apt){if(!rtxCapabilityMatches(lCodec,rCodec,localCapabilities.codecs,remoteCapabilities.codecs)){continue}}rCodec=JSON.parse(JSON.stringify(rCodec));rCodec.numChannels=Math.min(lCodec.numChannels,rCodec.numChannels);commonCapabilities.codecs.push(rCodec);rCodec.rtcpFeedback=rCodec.rtcpFeedback.filter(function(fb){for(var j=0;j<lCodec.rtcpFeedback.length;j++){if(lCodec.rtcpFeedback[j].type===fb.type&&lCodec.rtcpFeedback[j].parameter===fb.parameter){return true}}return false});break}}});localCapabilities.headerExtensions.forEach(function(lHeaderExtension){for(var i=0;i<remoteCapabilities.headerExtensions.length;i++){var rHeaderExtension=remoteCapabilities.headerExtensions[i];if(lHeaderExtension.uri===rHeaderExtension.uri){commonCapabilities.headerExtensions.push(rHeaderExtension);break}}});return commonCapabilities}function isActionAllowedInSignalingState(action,type,signalingState){return{offer:{setLocalDescription:["stable","have-local-offer"],setRemoteDescription:["stable","have-remote-offer"]},answer:{setLocalDescription:["have-remote-offer","have-local-pranswer"],setRemoteDescription:["have-local-offer","have-remote-pranswer"]}}[type][action].indexOf(signalingState)!==-1}function maybeAddCandidate(iceTransport,candidate){var alreadyAdded=iceTransport.getRemoteCandidates().find(function(remoteCandidate){return candidate.foundation===remoteCandidate.foundation&&candidate.ip===remoteCandidate.ip&&candidate.port===remoteCandidate.port&&candidate.priority===remoteCandidate.priority&&candidate.protocol===remoteCandidate.protocol&&candidate.type===remoteCandidate.type});if(!alreadyAdded){iceTransport.addRemoteCandidate(candidate)}return!alreadyAdded}function makeError(name,description){var e=new Error(description);e.name=name;e.code={NotSupportedError:9,InvalidStateError:11,InvalidAccessError:15,TypeError:undefined,OperationError:undefined}[name];return e}module.exports=function(window,edgeVersion){function addTrackToStreamAndFireEvent(track,stream){stream.addTrack(track);stream.dispatchEvent(new window.MediaStreamTrackEvent("addtrack",{track:track}))}function removeTrackFromStreamAndFireEvent(track,stream){stream.removeTrack(track);stream.dispatchEvent(new window.MediaStreamTrackEvent("removetrack",{track:track}))}function fireAddTrack(pc,track,receiver,streams){var trackEvent=new Event("track");trackEvent.track=track;trackEvent.receiver=receiver;trackEvent.transceiver={receiver:receiver};trackEvent.streams=streams;window.setTimeout(function(){pc._dispatchEvent("track",trackEvent)})}var RTCPeerConnection=function(config){var pc=this;var _eventTarget=document.createDocumentFragment();["addEventListener","removeEventListener","dispatchEvent"].forEach(function(method){pc[method]=_eventTarget[method].bind(_eventTarget)});this.canTrickleIceCandidates=null;this.needNegotiation=false;this.localStreams=[];this.remoteStreams=[];this._localDescription=null;this._remoteDescription=null;this.signalingState="stable";this.iceConnectionState="new";this.connectionState="new";this.iceGatheringState="new";config=JSON.parse(JSON.stringify(config||{}));this.usingBundle=config.bundlePolicy==="max-bundle";if(config.rtcpMuxPolicy==="negotiate"){throw makeError("NotSupportedError","rtcpMuxPolicy 'negotiate' is not supported")}else if(!config.rtcpMuxPolicy){config.rtcpMuxPolicy="require"}switch(config.iceTransportPolicy){case"all":case"relay":break;default:config.iceTransportPolicy="all";break}switch(config.bundlePolicy){case"balanced":case"max-compat":case"max-bundle":break;default:config.bundlePolicy="balanced";break}config.iceServers=filterIceServers(config.iceServers||[],edgeVersion);this._iceGatherers=[];if(config.iceCandidatePoolSize){for(var i=config.iceCandidatePoolSize;i>0;i--){this._iceGatherers.push(new window.RTCIceGatherer({iceServers:config.iceServers,gatherPolicy:config.iceTransportPolicy}))}}else{config.iceCandidatePoolSize=0}this._config=config;this.transceivers=[];this._sdpSessionId=SDPUtils.generateSessionId();this._sdpSessionVersion=0;this._dtlsRole=undefined;this._isClosed=false};Object.defineProperty(RTCPeerConnection.prototype,"localDescription",{configurable:true,get:function(){return this._localDescription}});Object.defineProperty(RTCPeerConnection.prototype,"remoteDescription",{configurable:true,get:function(){return this._remoteDescription}});RTCPeerConnection.prototype.onicecandidate=null;RTCPeerConnection.prototype.onaddstream=null;RTCPeerConnection.prototype.ontrack=null;RTCPeerConnection.prototype.onremovestream=null;RTCPeerConnection.prototype.onsignalingstatechange=null;RTCPeerConnection.prototype.oniceconnectionstatechange=null;RTCPeerConnection.prototype.onconnectionstatechange=null;RTCPeerConnection.prototype.onicegatheringstatechange=null;RTCPeerConnection.prototype.onnegotiationneeded=null;RTCPeerConnection.prototype.ondatachannel=null;RTCPeerConnection.prototype._dispatchEvent=function(name,event){if(this._isClosed){return}this.dispatchEvent(event);if(typeof this["on"+name]==="function"){this["on"+name](event)}};RTCPeerConnection.prototype._emitGatheringStateChange=function(){var event=new Event("icegatheringstatechange");this._dispatchEvent("icegatheringstatechange",event)};RTCPeerConnection.prototype.getConfiguration=function(){return this._config};RTCPeerConnection.prototype.getLocalStreams=function(){return this.localStreams};RTCPeerConnection.prototype.getRemoteStreams=function(){return this.remoteStreams};RTCPeerConnection.prototype._createTransceiver=function(kind,doNotAdd){var hasBundleTransport=this.transceivers.length>0;var transceiver={track:null,iceGatherer:null,iceTransport:null,dtlsTransport:null,localCapabilities:null,remoteCapabilities:null,rtpSender:null,rtpReceiver:null,kind:kind,mid:null,sendEncodingParameters:null,recvEncodingParameters:null,stream:null,associatedRemoteMediaStreams:[],wantReceive:true};if(this.usingBundle&&hasBundleTransport){transceiver.iceTransport=this.transceivers[0].iceTransport;transceiver.dtlsTransport=this.transceivers[0].dtlsTransport}else{var transports=this._createIceAndDtlsTransports();transceiver.iceTransport=transports.iceTransport;transceiver.dtlsTransport=transports.dtlsTransport}if(!doNotAdd){this.transceivers.push(transceiver)}return transceiver};RTCPeerConnection.prototype.addTrack=function(track,stream){if(this._isClosed){throw makeError("InvalidStateError","Attempted to call addTrack on a closed peerconnection.")}var alreadyExists=this.transceivers.find(function(s){return s.track===track});if(alreadyExists){throw makeError("InvalidAccessError","Track already exists.")}var transceiver;for(var i=0;i<this.transceivers.length;i++){if(!this.transceivers[i].track&&this.transceivers[i].kind===track.kind){transceiver=this.transceivers[i]}}if(!transceiver){transceiver=this._createTransceiver(track.kind)}this._maybeFireNegotiationNeeded();if(this.localStreams.indexOf(stream)===-1){this.localStreams.push(stream)}transceiver.track=track;transceiver.stream=stream;transceiver.rtpSender=new window.RTCRtpSender(track,transceiver.dtlsTransport);return transceiver.rtpSender};RTCPeerConnection.prototype.addStream=function(stream){var pc=this;if(edgeVersion>=15025){stream.getTracks().forEach(function(track){pc.addTrack(track,stream)})}else{var clonedStream=stream.clone();stream.getTracks().forEach(function(track,idx){var clonedTrack=clonedStream.getTracks()[idx];track.addEventListener("enabled",function(event){clonedTrack.enabled=event.enabled})});clonedStream.getTracks().forEach(function(track){pc.addTrack(track,clonedStream)})}};RTCPeerConnection.prototype.removeTrack=function(sender){if(this._isClosed){throw makeError("InvalidStateError","Attempted to call removeTrack on a closed peerconnection.")}if(!(sender instanceof window.RTCRtpSender)){throw new TypeError("Argument 1 of RTCPeerConnection.removeTrack "+"does not implement interface RTCRtpSender.")}var transceiver=this.transceivers.find(function(t){return t.rtpSender===sender});if(!transceiver){throw makeError("InvalidAccessError","Sender was not created by this connection.")}var stream=transceiver.stream;transceiver.rtpSender.stop();transceiver.rtpSender=null;transceiver.track=null;transceiver.stream=null;var localStreams=this.transceivers.map(function(t){return t.stream});if(localStreams.indexOf(stream)===-1&&this.localStreams.indexOf(stream)>-1){this.localStreams.splice(this.localStreams.indexOf(stream),1)}this._maybeFireNegotiationNeeded()};RTCPeerConnection.prototype.removeStream=function(stream){var pc=this;stream.getTracks().forEach(function(track){var sender=pc.getSenders().find(function(s){return s.track===track});if(sender){pc.removeTrack(sender)}})};RTCPeerConnection.prototype.getSenders=function(){return this.transceivers.filter(function(transceiver){return!!transceiver.rtpSender}).map(function(transceiver){return transceiver.rtpSender})};RTCPeerConnection.prototype.getReceivers=function(){return this.transceivers.filter(function(transceiver){return!!transceiver.rtpReceiver}).map(function(transceiver){return transceiver.rtpReceiver})};RTCPeerConnection.prototype._createIceGatherer=function(sdpMLineIndex,usingBundle){var pc=this;if(usingBundle&&sdpMLineIndex>0){return this.transceivers[0].iceGatherer}else if(this._iceGatherers.length){return this._iceGatherers.shift()}var iceGatherer=new window.RTCIceGatherer({iceServers:this._config.iceServers,gatherPolicy:this._config.iceTransportPolicy});Object.defineProperty(iceGatherer,"state",{value:"new",writable:true});this.transceivers[sdpMLineIndex].bufferedCandidateEvents=[];this.transceivers[sdpMLineIndex].bufferCandidates=function(event){var end=!event.candidate||Object.keys(event.candidate).length===0;iceGatherer.state=end?"completed":"gathering";if(pc.transceivers[sdpMLineIndex].bufferedCandidateEvents!==null){pc.transceivers[sdpMLineIndex].bufferedCandidateEvents.push(event)}};iceGatherer.addEventListener("localcandidate",this.transceivers[sdpMLineIndex].bufferCandidates);return iceGatherer};RTCPeerConnection.prototype._gather=function(mid,sdpMLineIndex){var pc=this;var iceGatherer=this.transceivers[sdpMLineIndex].iceGatherer;if(iceGatherer.onlocalcandidate){return}var bufferedCandidateEvents=this.transceivers[sdpMLineIndex].bufferedCandidateEvents;this.transceivers[sdpMLineIndex].bufferedCandidateEvents=null;iceGatherer.removeEventListener("localcandidate",this.transceivers[sdpMLineIndex].bufferCandidates);iceGatherer.onlocalcandidate=function(evt){if(pc.usingBundle&&sdpMLineIndex>0){return}var event=new Event("icecandidate");event.candidate={sdpMid:mid,sdpMLineIndex:sdpMLineIndex};var cand=evt.candidate;var end=!cand||Object.keys(cand).length===0;if(end){if(iceGatherer.state==="new"||iceGatherer.state==="gathering"){iceGatherer.state="completed"}}else{if(iceGatherer.state==="new"){iceGatherer.state="gathering"}cand.component=1;cand.ufrag=iceGatherer.getLocalParameters().usernameFragment;var serializedCandidate=SDPUtils.writeCandidate(cand);event.candidate=Object.assign(event.candidate,SDPUtils.parseCandidate(serializedCandidate));event.candidate.candidate=serializedCandidate;event.candidate.toJSON=function(){return{candidate:event.candidate.candidate,sdpMid:event.candidate.sdpMid,sdpMLineIndex:event.candidate.sdpMLineIndex,usernameFragment:event.candidate.usernameFragment}}}var sections=SDPUtils.getMediaSections(pc._localDescription.sdp);if(!end){sections[event.candidate.sdpMLineIndex]+="a="+event.candidate.candidate+"\r\n"}else{sections[event.candidate.sdpMLineIndex]+="a=end-of-candidates\r\n"}pc._localDescription.sdp=SDPUtils.getDescription(pc._localDescription.sdp)+sections.join("");var complete=pc.transceivers.every(function(transceiver){return transceiver.iceGatherer&&transceiver.iceGatherer.state==="completed"});if(pc.iceGatheringState!=="gathering"){pc.iceGatheringState="gathering";pc._emitGatheringStateChange()}if(!end){pc._dispatchEvent("icecandidate",event)}if(complete){pc._dispatchEvent("icecandidate",new Event("icecandidate"));pc.iceGatheringState="complete";pc._emitGatheringStateChange()}};window.setTimeout(function(){bufferedCandidateEvents.forEach(function(e){iceGatherer.onlocalcandidate(e)})},0)};RTCPeerConnection.prototype._createIceAndDtlsTransports=function(){var pc=this;var iceTransport=new window.RTCIceTransport(null);iceTransport.onicestatechange=function(){pc._updateIceConnectionState();pc._updateConnectionState()};var dtlsTransport=new window.RTCDtlsTransport(iceTransport);dtlsTransport.ondtlsstatechange=function(){pc._updateConnectionState()};dtlsTransport.onerror=function(){Object.defineProperty(dtlsTransport,"state",{value:"failed",writable:true});pc._updateConnectionState()};return{iceTransport:iceTransport,dtlsTransport:dtlsTransport}};RTCPeerConnection.prototype._disposeIceAndDtlsTransports=function(sdpMLineIndex){var iceGatherer=this.transceivers[sdpMLineIndex].iceGatherer;if(iceGatherer){delete iceGatherer.onlocalcandidate;delete this.transceivers[sdpMLineIndex].iceGatherer}var iceTransport=this.transceivers[sdpMLineIndex].iceTransport;if(iceTransport){delete iceTransport.onicestatechange;delete this.transceivers[sdpMLineIndex].iceTransport}var dtlsTransport=this.transceivers[sdpMLineIndex].dtlsTransport;if(dtlsTransport){delete dtlsTransport.ondtlsstatechange;delete dtlsTransport.onerror;delete this.transceivers[sdpMLineIndex].dtlsTransport}};RTCPeerConnection.prototype._transceive=function(transceiver,send,recv){var params=getCommonCapabilities(transceiver.localCapabilities,transceiver.remoteCapabilities);if(send&&transceiver.rtpSender){params.encodings=transceiver.sendEncodingParameters;params.rtcp={cname:SDPUtils.localCName,compound:transceiver.rtcpParameters.compound};if(transceiver.recvEncodingParameters.length){params.rtcp.ssrc=transceiver.recvEncodingParameters[0].ssrc}transceiver.rtpSender.send(params)}if(recv&&transceiver.rtpReceiver&&params.codecs.length>0){if(transceiver.kind==="video"&&transceiver.recvEncodingParameters&&edgeVersion<15019){transceiver.recvEncodingParameters.forEach(function(p){delete p.rtx})}if(transceiver.recvEncodingParameters.length){params.encodings=transceiver.recvEncodingParameters}else{params.encodings=[{}]}params.rtcp={compound:transceiver.rtcpParameters.compound};if(transceiver.rtcpParameters.cname){params.rtcp.cname=transceiver.rtcpParameters.cname}if(transceiver.sendEncodingParameters.length){params.rtcp.ssrc=transceiver.sendEncodingParameters[0].ssrc}transceiver.rtpReceiver.receive(params)}};RTCPeerConnection.prototype.setLocalDescription=function(description){var pc=this;if(["offer","answer"].indexOf(description.type)===-1){return Promise.reject(makeError("TypeError",'Unsupported type "'+description.type+'"'))}if(!isActionAllowedInSignalingState("setLocalDescription",description.type,pc.signalingState)||pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not set local "+description.type+" in state "+pc.signalingState))}var sections;var sessionpart;if(description.type==="offer"){sections=SDPUtils.splitSections(description.sdp);sessionpart=sections.shift();sections.forEach(function(mediaSection,sdpMLineIndex){var caps=SDPUtils.parseRtpParameters(mediaSection);pc.transceivers[sdpMLineIndex].localCapabilities=caps});pc.transceivers.forEach(function(transceiver,sdpMLineIndex){pc._gather(transceiver.mid,sdpMLineIndex)})}else if(description.type==="answer"){sections=SDPUtils.splitSections(pc._remoteDescription.sdp);sessionpart=sections.shift();var isIceLite=SDPUtils.matchPrefix(sessionpart,"a=ice-lite").length>0;sections.forEach(function(mediaSection,sdpMLineIndex){var transceiver=pc.transceivers[sdpMLineIndex];var iceGatherer=transceiver.iceGatherer;var iceTransport=transceiver.iceTransport;var dtlsTransport=transceiver.dtlsTransport;var localCapabilities=transceiver.localCapabilities;var remoteCapabilities=transceiver.remoteCapabilities;var rejected=SDPUtils.isRejected(mediaSection)&&SDPUtils.matchPrefix(mediaSection,"a=bundle-only").length===0;if(!rejected&&!transceiver.rejected){var remoteIceParameters=SDPUtils.getIceParameters(mediaSection,sessionpart);var remoteDtlsParameters=SDPUtils.getDtlsParameters(mediaSection,sessionpart);if(isIceLite){remoteDtlsParameters.role="server"}if(!pc.usingBundle||sdpMLineIndex===0){pc._gather(transceiver.mid,sdpMLineIndex);if(iceTransport.state==="new"){iceTransport.start(iceGatherer,remoteIceParameters,isIceLite?"controlling":"controlled")}if(dtlsTransport.state==="new"){dtlsTransport.start(remoteDtlsParameters)}}var params=getCommonCapabilities(localCapabilities,remoteCapabilities);pc._transceive(transceiver,params.codecs.length>0,false)}})}pc._localDescription={type:description.type,sdp:description.sdp};if(description.type==="offer"){pc._updateSignalingState("have-local-offer")}else{pc._updateSignalingState("stable")}return Promise.resolve()};RTCPeerConnection.prototype.setRemoteDescription=function(description){var pc=this;if(["offer","answer"].indexOf(description.type)===-1){return Promise.reject(makeError("TypeError",'Unsupported type "'+description.type+'"'))}if(!isActionAllowedInSignalingState("setRemoteDescription",description.type,pc.signalingState)||pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not set remote "+description.type+" in state "+pc.signalingState))}var streams={};pc.remoteStreams.forEach(function(stream){streams[stream.id]=stream});var receiverList=[];var sections=SDPUtils.splitSections(description.sdp);var sessionpart=sections.shift();var isIceLite=SDPUtils.matchPrefix(sessionpart,"a=ice-lite").length>0;var usingBundle=SDPUtils.matchPrefix(sessionpart,"a=group:BUNDLE ").length>0;pc.usingBundle=usingBundle;var iceOptions=SDPUtils.matchPrefix(sessionpart,"a=ice-options:")[0];if(iceOptions){pc.canTrickleIceCandidates=iceOptions.substr(14).split(" ").indexOf("trickle")>=0}else{pc.canTrickleIceCandidates=false}sections.forEach(function(mediaSection,sdpMLineIndex){var lines=SDPUtils.splitLines(mediaSection);var kind=SDPUtils.getKind(mediaSection);var rejected=SDPUtils.isRejected(mediaSection)&&SDPUtils.matchPrefix(mediaSection,"a=bundle-only").length===0;var protocol=lines[0].substr(2).split(" ")[2];var direction=SDPUtils.getDirection(mediaSection,sessionpart);var remoteMsid=SDPUtils.parseMsid(mediaSection);var mid=SDPUtils.getMid(mediaSection)||SDPUtils.generateIdentifier();if(rejected||kind==="application"&&(protocol==="DTLS/SCTP"||protocol==="UDP/DTLS/SCTP")){pc.transceivers[sdpMLineIndex]={mid:mid,kind:kind,protocol:protocol,rejected:true};return}if(!rejected&&pc.transceivers[sdpMLineIndex]&&pc.transceivers[sdpMLineIndex].rejected){pc.transceivers[sdpMLineIndex]=pc._createTransceiver(kind,true)}var transceiver;var iceGatherer;var iceTransport;var dtlsTransport;var rtpReceiver;var sendEncodingParameters;var recvEncodingParameters;var localCapabilities;var track;var remoteCapabilities=SDPUtils.parseRtpParameters(mediaSection);var remoteIceParameters;var remoteDtlsParameters;if(!rejected){remoteIceParameters=SDPUtils.getIceParameters(mediaSection,sessionpart);remoteDtlsParameters=SDPUtils.getDtlsParameters(mediaSection,sessionpart);remoteDtlsParameters.role="client"}recvEncodingParameters=SDPUtils.parseRtpEncodingParameters(mediaSection);var rtcpParameters=SDPUtils.parseRtcpParameters(mediaSection);var isComplete=SDPUtils.matchPrefix(mediaSection,"a=end-of-candidates",sessionpart).length>0;var cands=SDPUtils.matchPrefix(mediaSection,"a=candidate:").map(function(cand){return SDPUtils.parseCandidate(cand)}).filter(function(cand){return cand.component===1});if((description.type==="offer"||description.type==="answer")&&!rejected&&usingBundle&&sdpMLineIndex>0&&pc.transceivers[sdpMLineIndex]){pc._disposeIceAndDtlsTransports(sdpMLineIndex);pc.transceivers[sdpMLineIndex].iceGatherer=pc.transceivers[0].iceGatherer;pc.transceivers[sdpMLineIndex].iceTransport=pc.transceivers[0].iceTransport;pc.transceivers[sdpMLineIndex].dtlsTransport=pc.transceivers[0].dtlsTransport;if(pc.transceivers[sdpMLineIndex].rtpSender){pc.transceivers[sdpMLineIndex].rtpSender.setTransport(pc.transceivers[0].dtlsTransport)}if(pc.transceivers[sdpMLineIndex].rtpReceiver){pc.transceivers[sdpMLineIndex].rtpReceiver.setTransport(pc.transceivers[0].dtlsTransport)}}if(description.type==="offer"&&!rejected){transceiver=pc.transceivers[sdpMLineIndex]||pc._createTransceiver(kind);transceiver.mid=mid;if(!transceiver.iceGatherer){transceiver.iceGatherer=pc._createIceGatherer(sdpMLineIndex,usingBundle)}if(cands.length&&transceiver.iceTransport.state==="new"){if(isComplete&&(!usingBundle||sdpMLineIndex===0)){transceiver.iceTransport.setRemoteCandidates(cands)}else{cands.forEach(function(candidate){maybeAddCandidate(transceiver.iceTransport,candidate)})}}localCapabilities=window.RTCRtpReceiver.getCapabilities(kind);if(edgeVersion<15019){localCapabilities.codecs=localCapabilities.codecs.filter(function(codec){return codec.name!=="rtx"})}sendEncodingParameters=transceiver.sendEncodingParameters||[{ssrc:(2*sdpMLineIndex+2)*1001}];var isNewTrack=false;if(direction==="sendrecv"||direction==="sendonly"){isNewTrack=!transceiver.rtpReceiver;rtpReceiver=transceiver.rtpReceiver||new window.RTCRtpReceiver(transceiver.dtlsTransport,kind);if(isNewTrack){var stream;track=rtpReceiver.track;if(remoteMsid&&remoteMsid.stream==="-"){}else if(remoteMsid){if(!streams[remoteMsid.stream]){streams[remoteMsid.stream]=new window.MediaStream;Object.defineProperty(streams[remoteMsid.stream],"id",{get:function(){return remoteMsid.stream}})}Object.defineProperty(track,"id",{get:function(){return remoteMsid.track}});stream=streams[remoteMsid.stream]}else{if(!streams.default){streams.default=new window.MediaStream}stream=streams.default}if(stream){addTrackToStreamAndFireEvent(track,stream);transceiver.associatedRemoteMediaStreams.push(stream)}receiverList.push([track,rtpReceiver,stream])}}else if(transceiver.rtpReceiver&&transceiver.rtpReceiver.track){transceiver.associatedRemoteMediaStreams.forEach(function(s){var nativeTrack=s.getTracks().find(function(t){return t.id===transceiver.rtpReceiver.track.id});if(nativeTrack){removeTrackFromStreamAndFireEvent(nativeTrack,s)}});transceiver.associatedRemoteMediaStreams=[]}transceiver.localCapabilities=localCapabilities;transceiver.remoteCapabilities=remoteCapabilities;transceiver.rtpReceiver=rtpReceiver;transceiver.rtcpParameters=rtcpParameters;transceiver.sendEncodingParameters=sendEncodingParameters;transceiver.recvEncodingParameters=recvEncodingParameters;pc._transceive(pc.transceivers[sdpMLineIndex],false,isNewTrack)}else if(description.type==="answer"&&!rejected){transceiver=pc.transceivers[sdpMLineIndex];iceGatherer=transceiver.iceGatherer;iceTransport=transceiver.iceTransport;dtlsTransport=transceiver.dtlsTransport;rtpReceiver=transceiver.rtpReceiver;sendEncodingParameters=transceiver.sendEncodingParameters;localCapabilities=transceiver.localCapabilities;pc.transceivers[sdpMLineIndex].recvEncodingParameters=recvEncodingParameters;pc.transceivers[sdpMLineIndex].remoteCapabilities=remoteCapabilities;pc.transceivers[sdpMLineIndex].rtcpParameters=rtcpParameters;if(cands.length&&iceTransport.state==="new"){if((isIceLite||isComplete)&&(!usingBundle||sdpMLineIndex===0)){iceTransport.setRemoteCandidates(cands)}else{cands.forEach(function(candidate){maybeAddCandidate(transceiver.iceTransport,candidate)})}}if(!usingBundle||sdpMLineIndex===0){if(iceTransport.state==="new"){iceTransport.start(iceGatherer,remoteIceParameters,"controlling")}if(dtlsTransport.state==="new"){dtlsTransport.start(remoteDtlsParameters)}}var commonCapabilities=getCommonCapabilities(transceiver.localCapabilities,transceiver.remoteCapabilities);var hasRtx=commonCapabilities.codecs.filter(function(c){return c.name.toLowerCase()==="rtx"}).length;if(!hasRtx&&transceiver.sendEncodingParameters[0].rtx){delete transceiver.sendEncodingParameters[0].rtx}pc._transceive(transceiver,direction==="sendrecv"||direction==="recvonly",direction==="sendrecv"||direction==="sendonly");if(rtpReceiver&&(direction==="sendrecv"||direction==="sendonly")){track=rtpReceiver.track;if(remoteMsid){if(!streams[remoteMsid.stream]){streams[remoteMsid.stream]=new window.MediaStream}addTrackToStreamAndFireEvent(track,streams[remoteMsid.stream]);receiverList.push([track,rtpReceiver,streams[remoteMsid.stream]])}else{if(!streams.default){streams.default=new window.MediaStream}addTrackToStreamAndFireEvent(track,streams.default);receiverList.push([track,rtpReceiver,streams.default])}}else{delete transceiver.rtpReceiver}}});if(pc._dtlsRole===undefined){pc._dtlsRole=description.type==="offer"?"active":"passive"}pc._remoteDescription={type:description.type,sdp:description.sdp};if(description.type==="offer"){pc._updateSignalingState("have-remote-offer")}else{pc._updateSignalingState("stable")}Object.keys(streams).forEach(function(sid){var stream=streams[sid];if(stream.getTracks().length){if(pc.remoteStreams.indexOf(stream)===-1){pc.remoteStreams.push(stream);var event=new Event("addstream");event.stream=stream;window.setTimeout(function(){pc._dispatchEvent("addstream",event)})}receiverList.forEach(function(item){var track=item[0];var receiver=item[1];if(stream.id!==item[2].id){return}fireAddTrack(pc,track,receiver,[stream])})}});receiverList.forEach(function(item){if(item[2]){return}fireAddTrack(pc,item[0],item[1],[])});window.setTimeout(function(){if(!(pc&&pc.transceivers)){return}pc.transceivers.forEach(function(transceiver){if(transceiver.iceTransport&&transceiver.iceTransport.state==="new"&&transceiver.iceTransport.getRemoteCandidates().length>0){console.warn("Timeout for addRemoteCandidate. Consider sending "+"an end-of-candidates notification");transceiver.iceTransport.addRemoteCandidate({})}})},4e3);return Promise.resolve()};RTCPeerConnection.prototype.close=function(){this.transceivers.forEach(function(transceiver){if(transceiver.iceTransport){transceiver.iceTransport.stop()}if(transceiver.dtlsTransport){transceiver.dtlsTransport.stop()}if(transceiver.rtpSender){transceiver.rtpSender.stop()}if(transceiver.rtpReceiver){transceiver.rtpReceiver.stop()}});this._isClosed=true;this._updateSignalingState("closed")};RTCPeerConnection.prototype._updateSignalingState=function(newState){this.signalingState=newState;var event=new Event("signalingstatechange");this._dispatchEvent("signalingstatechange",event)};RTCPeerConnection.prototype._maybeFireNegotiationNeeded=function(){var pc=this;if(this.signalingState!=="stable"||this.needNegotiation===true){return}this.needNegotiation=true;window.setTimeout(function(){if(pc.needNegotiation){pc.needNegotiation=false;var event=new Event("negotiationneeded");pc._dispatchEvent("negotiationneeded",event)}},0)};RTCPeerConnection.prototype._updateIceConnectionState=function(){var newState;var states={new:0,closed:0,checking:0,connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(transceiver){states[transceiver.iceTransport.state]++});newState="new";if(states.failed>0){newState="failed"}else if(states.checking>0){newState="checking"}else if(states.disconnected>0){newState="disconnected"}else if(states.new>0){newState="new"}else if(states.connected>0){newState="connected"}else if(states.completed>0){newState="completed"}if(newState!==this.iceConnectionState){this.iceConnectionState=newState;var event=new Event("iceconnectionstatechange");this._dispatchEvent("iceconnectionstatechange",event)}};RTCPeerConnection.prototype._updateConnectionState=function(){var newState;var states={new:0,closed:0,connecting:0,connected:0,completed:0,disconnected:0,failed:0};this.transceivers.forEach(function(transceiver){states[transceiver.iceTransport.state]++;states[transceiver.dtlsTransport.state]++});states.connected+=states.completed;newState="new";if(states.failed>0){newState="failed"}else if(states.connecting>0){newState="connecting"}else if(states.disconnected>0){newState="disconnected"}else if(states.new>0){newState="new"}else if(states.connected>0){newState="connected"}if(newState!==this.connectionState){this.connectionState=newState;var event=new Event("connectionstatechange");this._dispatchEvent("connectionstatechange",event)}};RTCPeerConnection.prototype.createOffer=function(){var pc=this;if(pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not call createOffer after close"))}var numAudioTracks=pc.transceivers.filter(function(t){return t.kind==="audio"}).length;var numVideoTracks=pc.transceivers.filter(function(t){return t.kind==="video"}).length;var offerOptions=arguments[0];if(offerOptions){if(offerOptions.mandatory||offerOptions.optional){throw new TypeError("Legacy mandatory/optional constraints not supported.")}if(offerOptions.offerToReceiveAudio!==undefined){if(offerOptions.offerToReceiveAudio===true){numAudioTracks=1}else if(offerOptions.offerToReceiveAudio===false){numAudioTracks=0}else{numAudioTracks=offerOptions.offerToReceiveAudio}}if(offerOptions.offerToReceiveVideo!==undefined){if(offerOptions.offerToReceiveVideo===true){numVideoTracks=1}else if(offerOptions.offerToReceiveVideo===false){numVideoTracks=0}else{numVideoTracks=offerOptions.offerToReceiveVideo}}}pc.transceivers.forEach(function(transceiver){if(transceiver.kind==="audio"){numAudioTracks--;if(numAudioTracks<0){transceiver.wantReceive=false}}else if(transceiver.kind==="video"){numVideoTracks--;if(numVideoTracks<0){transceiver.wantReceive=false}}});while(numAudioTracks>0||numVideoTracks>0){if(numAudioTracks>0){pc._createTransceiver("audio");numAudioTracks--}if(numVideoTracks>0){pc._createTransceiver("video");numVideoTracks--}}var sdp=SDPUtils.writeSessionBoilerplate(pc._sdpSessionId,pc._sdpSessionVersion++);pc.transceivers.forEach(function(transceiver,sdpMLineIndex){var track=transceiver.track;var kind=transceiver.kind;var mid=transceiver.mid||SDPUtils.generateIdentifier();transceiver.mid=mid;if(!transceiver.iceGatherer){transceiver.iceGatherer=pc._createIceGatherer(sdpMLineIndex,pc.usingBundle)}var localCapabilities=window.RTCRtpSender.getCapabilities(kind);if(edgeVersion<15019){localCapabilities.codecs=localCapabilities.codecs.filter(function(codec){return codec.name!=="rtx"})}localCapabilities.codecs.forEach(function(codec){if(codec.name==="H264"&&codec.parameters["level-asymmetry-allowed"]===undefined){codec.parameters["level-asymmetry-allowed"]="1"}if(transceiver.remoteCapabilities&&transceiver.remoteCapabilities.codecs){transceiver.remoteCapabilities.codecs.forEach(function(remoteCodec){if(codec.name.toLowerCase()===remoteCodec.name.toLowerCase()&&codec.clockRate===remoteCodec.clockRate){codec.preferredPayloadType=remoteCodec.payloadType}})}});localCapabilities.headerExtensions.forEach(function(hdrExt){var remoteExtensions=transceiver.remoteCapabilities&&transceiver.remoteCapabilities.headerExtensions||[];remoteExtensions.forEach(function(rHdrExt){if(hdrExt.uri===rHdrExt.uri){hdrExt.id=rHdrExt.id}})});var sendEncodingParameters=transceiver.sendEncodingParameters||[{ssrc:(2*sdpMLineIndex+1)*1001}];if(track){if(edgeVersion>=15019&&kind==="video"&&!sendEncodingParameters[0].rtx){sendEncodingParameters[0].rtx={ssrc:sendEncodingParameters[0].ssrc+1}}}if(transceiver.wantReceive){transceiver.rtpReceiver=new window.RTCRtpReceiver(transceiver.dtlsTransport,kind)}transceiver.localCapabilities=localCapabilities;transceiver.sendEncodingParameters=sendEncodingParameters});if(pc._config.bundlePolicy!=="max-compat"){sdp+="a=group:BUNDLE "+pc.transceivers.map(function(t){return t.mid}).join(" ")+"\r\n"}sdp+="a=ice-options:trickle\r\n";pc.transceivers.forEach(function(transceiver,sdpMLineIndex){sdp+=writeMediaSection(transceiver,transceiver.localCapabilities,"offer",transceiver.stream,pc._dtlsRole);sdp+="a=rtcp-rsize\r\n";if(transceiver.iceGatherer&&pc.iceGatheringState!=="new"&&(sdpMLineIndex===0||!pc.usingBundle)){transceiver.iceGatherer.getLocalCandidates().forEach(function(cand){cand.component=1;sdp+="a="+SDPUtils.writeCandidate(cand)+"\r\n"});if(transceiver.iceGatherer.state==="completed"){sdp+="a=end-of-candidates\r\n"}}});var desc=new window.RTCSessionDescription({type:"offer",sdp:sdp});return Promise.resolve(desc)};RTCPeerConnection.prototype.createAnswer=function(){var pc=this;if(pc._isClosed){return Promise.reject(makeError("InvalidStateError","Can not call createAnswer after close"))}if(!(pc.signalingState==="have-remote-offer"||pc.signalingState==="have-local-pranswer")){return Promise.reject(makeError("InvalidStateError","Can not call createAnswer in signalingState "+pc.signalingState))}var sdp=SDPUtils.writeSessionBoilerplate(pc._sdpSessionId,pc._sdpSessionVersion++);if(pc.usingBundle){sdp+="a=group:BUNDLE "+pc.transceivers.map(function(t){return t.mid}).join(" ")+"\r\n"}sdp+="a=ice-options:trickle\r\n";var mediaSectionsInOffer=SDPUtils.getMediaSections(pc._remoteDescription.sdp).length;pc.transceivers.forEach(function(transceiver,sdpMLineIndex){if(sdpMLineIndex+1>mediaSectionsInOffer){return}if(transceiver.rejected){if(transceiver.kind==="application"){if(transceiver.protocol==="DTLS/SCTP"){sdp+="m=application 0 DTLS/SCTP 5000\r\n"}else{sdp+="m=application 0 "+transceiver.protocol+" webrtc-datachannel\r\n"}}else if(transceiver.kind==="audio"){sdp+="m=audio 0 UDP/TLS/RTP/SAVPF 0\r\n"+"a=rtpmap:0 PCMU/8000\r\n"}else if(transceiver.kind==="video"){sdp+="m=video 0 UDP/TLS/RTP/SAVPF 120\r\n"+"a=rtpmap:120 VP8/90000\r\n"}sdp+="c=IN IP4 0.0.0.0\r\n"+"a=inactive\r\n"+"a=mid:"+transceiver.mid+"\r\n";return}if(transceiver.stream){var localTrack;if(transceiver.kind==="audio"){localTrack=transceiver.stream.getAudioTracks()[0]}else if(transceiver.kind==="video"){localTrack=transceiver.stream.getVideoTracks()[0]}if(localTrack){if(edgeVersion>=15019&&transceiver.kind==="video"&&!transceiver.sendEncodingParameters[0].rtx){transceiver.sendEncodingParameters[0].rtx={ssrc:transceiver.sendEncodingParameters[0].ssrc+1}}}}var commonCapabilities=getCommonCapabilities(transceiver.localCapabilities,transceiver.remoteCapabilities);var hasRtx=commonCapabilities.codecs.filter(function(c){return c.name.toLowerCase()==="rtx"}).length;if(!hasRtx&&transceiver.sendEncodingParameters[0].rtx){delete transceiver.sendEncodingParameters[0].rtx}sdp+=writeMediaSection(transceiver,commonCapabilities,"answer",transceiver.stream,pc._dtlsRole);if(transceiver.rtcpParameters&&transceiver.rtcpParameters.reducedSize){sdp+="a=rtcp-rsize\r\n"}});var desc=new window.RTCSessionDescription({type:"answer",sdp:sdp});return Promise.resolve(desc)};RTCPeerConnection.prototype.addIceCandidate=function(candidate){var pc=this;var sections;if(candidate&&!(candidate.sdpMLineIndex!==undefined||candidate.sdpMid)){return Promise.reject(new TypeError("sdpMLineIndex or sdpMid required"))}return new Promise(function(resolve,reject){if(!pc._remoteDescription){return reject(makeError("InvalidStateError","Can not add ICE candidate without a remote description"))}else if(!candidate||candidate.candidate===""){for(var j=0;j<pc.transceivers.length;j++){if(pc.transceivers[j].rejected){continue}pc.transceivers[j].iceTransport.addRemoteCandidate({});sections=SDPUtils.getMediaSections(pc._remoteDescription.sdp);sections[j]+="a=end-of-candidates\r\n";pc._remoteDescription.sdp=SDPUtils.getDescription(pc._remoteDescription.sdp)+sections.join("");if(pc.usingBundle){break}}}else{var sdpMLineIndex=candidate.sdpMLineIndex;if(candidate.sdpMid){for(var i=0;i<pc.transceivers.length;i++){if(pc.transceivers[i].mid===candidate.sdpMid){sdpMLineIndex=i;break}}}var transceiver=pc.transceivers[sdpMLineIndex];if(transceiver){if(transceiver.rejected){return resolve()}var cand=Object.keys(candidate.candidate).length>0?SDPUtils.parseCandidate(candidate.candidate):{};if(cand.protocol==="tcp"&&(cand.port===0||cand.port===9)){return resolve()}if(cand.component&&cand.component!==1){return resolve()}if(sdpMLineIndex===0||sdpMLineIndex>0&&transceiver.iceTransport!==pc.transceivers[0].iceTransport){if(!maybeAddCandidate(transceiver.iceTransport,cand)){return reject(makeError("OperationError","Can not add ICE candidate"))}}var candidateString=candidate.candidate.trim();if(candidateString.indexOf("a=")===0){candidateString=candidateString.substr(2)}sections=SDPUtils.getMediaSections(pc._remoteDescription.sdp);sections[sdpMLineIndex]+="a="+(cand.type?candidateString:"end-of-candidates")+"\r\n";pc._remoteDescription.sdp=SDPUtils.getDescription(pc._remoteDescription.sdp)+sections.join("")}else{return reject(makeError("OperationError","Can not add ICE candidate"))}}resolve()})};RTCPeerConnection.prototype.getStats=function(selector){if(selector&&selector instanceof window.MediaStreamTrack){var senderOrReceiver=null;this.transceivers.forEach(function(transceiver){if(transceiver.rtpSender&&transceiver.rtpSender.track===selector){senderOrReceiver=transceiver.rtpSender}else if(transceiver.rtpReceiver&&transceiver.rtpReceiver.track===selector){senderOrReceiver=transceiver.rtpReceiver}});if(!senderOrReceiver){throw makeError("InvalidAccessError","Invalid selector.")}return senderOrReceiver.getStats()}var promises=[];this.transceivers.forEach(function(transceiver){["rtpSender","rtpReceiver","iceGatherer","iceTransport","dtlsTransport"].forEach(function(method){if(transceiver[method]){promises.push(transceiver[method].getStats())}})});return Promise.all(promises).then(function(allStats){var results=new Map;allStats.forEach(function(stats){stats.forEach(function(stat){results.set(stat.id,stat)})});return results})};var ortcObjects=["RTCRtpSender","RTCRtpReceiver","RTCIceGatherer","RTCIceTransport","RTCDtlsTransport"];ortcObjects.forEach(function(ortcObjectName){var obj=window[ortcObjectName];if(obj&&obj.prototype&&obj.prototype.getStats){var nativeGetstats=obj.prototype.getStats;obj.prototype.getStats=function(){return nativeGetstats.apply(this).then(function(nativeStats){var mapStats=new Map;Object.keys(nativeStats).forEach(function(id){nativeStats[id].type=fixStatsType(nativeStats[id]);mapStats.set(id,nativeStats[id])});return mapStats})}}});var methods=["createOffer","createAnswer"];methods.forEach(function(method){var nativeMethod=RTCPeerConnection.prototype[method];RTCPeerConnection.prototype[method]=function(){var args=arguments;if(typeof args[0]==="function"||typeof args[1]==="function"){return nativeMethod.apply(this,[arguments[2]]).then(function(description){if(typeof args[0]==="function"){args[0].apply(null,[description])}},function(error){if(typeof args[1]==="function"){args[1].apply(null,[error])}})}return nativeMethod.apply(this,arguments)}});methods=["setLocalDescription","setRemoteDescription","addIceCandidate"];methods.forEach(function(method){var nativeMethod=RTCPeerConnection.prototype[method];RTCPeerConnection.prototype[method]=function(){var args=arguments;if(typeof args[1]==="function"||typeof args[2]==="function"){return nativeMethod.apply(this,arguments).then(function(){if(typeof args[1]==="function"){args[1].apply(null)}},function(error){if(typeof args[2]==="function"){args[2].apply(null,[error])}})}return nativeMethod.apply(this,arguments)}});["getStats"].forEach(function(method){var nativeMethod=RTCPeerConnection.prototype[method];RTCPeerConnection.prototype[method]=function(){var args=arguments;if(typeof args[1]==="function"){return nativeMethod.apply(this,arguments).then(function(){if(typeof args[1]==="function"){args[1].apply(null)}})}return nativeMethod.apply(this,arguments)}});return RTCPeerConnection}},{sdp:2}],2:[function(require,module,exports){"use strict";var SDPUtils={};SDPUtils.generateIdentifier=function(){return Math.random().toString(36).substr(2,10)};SDPUtils.localCName=SDPUtils.generateIdentifier();SDPUtils.splitLines=function(blob){return blob.trim().split("\n").map(function(line){return line.trim()})};SDPUtils.splitSections=function(blob){var parts=blob.split("\nm=");return parts.map(function(part,index){return(index>0?"m="+part:part).trim()+"\r\n"})};SDPUtils.getDescription=function(blob){var sections=SDPUtils.splitSections(blob);return sections&&sections[0]};SDPUtils.getMediaSections=function(blob){var sections=SDPUtils.splitSections(blob);sections.shift();return sections};SDPUtils.matchPrefix=function(blob,prefix){return SDPUtils.splitLines(blob).filter(function(line){return line.indexOf(prefix)===0})};SDPUtils.parseCandidate=function(line){var parts;if(line.indexOf("a=candidate:")===0){parts=line.substring(12).split(" ")}else{parts=line.substring(10).split(" ")}var candidate={foundation:parts[0],component:parseInt(parts[1],10),protocol:parts[2].toLowerCase(),priority:parseInt(parts[3],10),ip:parts[4],address:parts[4],port:parseInt(parts[5],10),type:parts[7]};for(var i=8;i<parts.length;i+=2){switch(parts[i]){case"raddr":candidate.relatedAddress=parts[i+1];break;case"rport":candidate.relatedPort=parseInt(parts[i+1],10);break;case"tcptype":candidate.tcpType=parts[i+1];break;case"ufrag":candidate.ufrag=parts[i+1];candidate.usernameFragment=parts[i+1];break;default:candidate[parts[i]]=parts[i+1];break}}return candidate};SDPUtils.writeCandidate=function(candidate){var sdp=[];sdp.push(candidate.foundation);sdp.push(candidate.component);sdp.push(candidate.protocol.toUpperCase());sdp.push(candidate.priority);sdp.push(candidate.address||candidate.ip);sdp.push(candidate.port);var type=candidate.type;sdp.push("typ");sdp.push(type);if(type!=="host"&&candidate.relatedAddress&&candidate.relatedPort){sdp.push("raddr");sdp.push(candidate.relatedAddress);sdp.push("rport");sdp.push(candidate.relatedPort)}if(candidate.tcpType&&candidate.protocol.toLowerCase()==="tcp"){sdp.push("tcptype");sdp.push(candidate.tcpType)}if(candidate.usernameFragment||candidate.ufrag){sdp.push("ufrag");sdp.push(candidate.usernameFragment||candidate.ufrag)}return"candidate:"+sdp.join(" ")};SDPUtils.parseIceOptions=function(line){return line.substr(14).split(" ")};SDPUtils.parseRtpMap=function(line){var parts=line.substr(9).split(" ");var parsed={payloadType:parseInt(parts.shift(),10)};parts=parts[0].split("/");parsed.name=parts[0];parsed.clockRate=parseInt(parts[1],10);parsed.channels=parts.length===3?parseInt(parts[2],10):1;parsed.numChannels=parsed.channels;return parsed};SDPUtils.writeRtpMap=function(codec){var pt=codec.payloadType;if(codec.preferredPayloadType!==undefined){pt=codec.preferredPayloadType}var channels=codec.channels||codec.numChannels||1;return"a=rtpmap:"+pt+" "+codec.name+"/"+codec.clockRate+(channels!==1?"/"+channels:"")+"\r\n"};SDPUtils.parseExtmap=function(line){var parts=line.substr(9).split(" ");return{id:parseInt(parts[0],10),direction:parts[0].indexOf("/")>0?parts[0].split("/")[1]:"sendrecv",uri:parts[1]}};SDPUtils.writeExtmap=function(headerExtension){return"a=extmap:"+(headerExtension.id||headerExtension.preferredId)+(headerExtension.direction&&headerExtension.direction!=="sendrecv"?"/"+headerExtension.direction:"")+" "+headerExtension.uri+"\r\n"};SDPUtils.parseFmtp=function(line){var parsed={};var kv;var parts=line.substr(line.indexOf(" ")+1).split(";");for(var j=0;j<parts.length;j++){kv=parts[j].trim().split("=");parsed[kv[0].trim()]=kv[1]}return parsed};SDPUtils.writeFmtp=function(codec){var line="";var pt=codec.payloadType;if(codec.preferredPayloadType!==undefined){pt=codec.preferredPayloadType}if(codec.parameters&&Object.keys(codec.parameters).length){var params=[];Object.keys(codec.parameters).forEach(function(param){if(codec.parameters[param]){params.push(param+"="+codec.parameters[param])}else{params.push(param)}});line+="a=fmtp:"+pt+" "+params.join(";")+"\r\n"}return line};SDPUtils.parseRtcpFb=function(line){var parts=line.substr(line.indexOf(" ")+1).split(" ");return{type:parts.shift(),parameter:parts.join(" ")}};SDPUtils.writeRtcpFb=function(codec){var lines="";var pt=codec.payloadType;if(codec.preferredPayloadType!==undefined){pt=codec.preferredPayloadType}if(codec.rtcpFeedback&&codec.rtcpFeedback.length){codec.rtcpFeedback.forEach(function(fb){lines+="a=rtcp-fb:"+pt+" "+fb.type+(fb.parameter&&fb.parameter.length?" "+fb.parameter:"")+"\r\n"})}return lines};SDPUtils.parseSsrcMedia=function(line){var sp=line.indexOf(" ");var parts={ssrc:parseInt(line.substr(7,sp-7),10)};var colon=line.indexOf(":",sp);if(colon>-1){parts.attribute=line.substr(sp+1,colon-sp-1);parts.value=line.substr(colon+1)}else{parts.attribute=line.substr(sp+1)}return parts};SDPUtils.parseSsrcGroup=function(line){var parts=line.substr(13).split(" ");return{semantics:parts.shift(),ssrcs:parts.map(function(ssrc){return parseInt(ssrc,10)})}};SDPUtils.getMid=function(mediaSection){var mid=SDPUtils.matchPrefix(mediaSection,"a=mid:")[0];if(mid){return mid.substr(6)}};SDPUtils.parseFingerprint=function(line){var parts=line.substr(14).split(" ");return{algorithm:parts[0].toLowerCase(),value:parts[1]}};SDPUtils.getDtlsParameters=function(mediaSection,sessionpart){var lines=SDPUtils.matchPrefix(mediaSection+sessionpart,"a=fingerprint:");return{role:"auto",fingerprints:lines.map(SDPUtils.parseFingerprint)}};SDPUtils.writeDtlsParameters=function(params,setupType){var sdp="a=setup:"+setupType+"\r\n";params.fingerprints.forEach(function(fp){sdp+="a=fingerprint:"+fp.algorithm+" "+fp.value+"\r\n"});return sdp};SDPUtils.getIceParameters=function(mediaSection,sessionpart){var lines=SDPUtils.splitLines(mediaSection);lines=lines.concat(SDPUtils.splitLines(sessionpart));var iceParameters={usernameFragment:lines.filter(function(line){return line.indexOf("a=ice-ufrag:")===0})[0].substr(12),password:lines.filter(function(line){return line.indexOf("a=ice-pwd:")===0})[0].substr(10)};return iceParameters};SDPUtils.writeIceParameters=function(params){return"a=ice-ufrag:"+params.usernameFragment+"\r\n"+"a=ice-pwd:"+params.password+"\r\n"};SDPUtils.parseRtpParameters=function(mediaSection){var description={codecs:[],headerExtensions:[],fecMechanisms:[],rtcp:[]};var lines=SDPUtils.splitLines(mediaSection);var mline=lines[0].split(" ");for(var i=3;i<mline.length;i++){var pt=mline[i];var rtpmapline=SDPUtils.matchPrefix(mediaSection,"a=rtpmap:"+pt+" ")[0];if(rtpmapline){var codec=SDPUtils.parseRtpMap(rtpmapline);var fmtps=SDPUtils.matchPrefix(mediaSection,"a=fmtp:"+pt+" ");codec.parameters=fmtps.length?SDPUtils.parseFmtp(fmtps[0]):{};codec.rtcpFeedback=SDPUtils.matchPrefix(mediaSection,"a=rtcp-fb:"+pt+" ").map(SDPUtils.parseRtcpFb);description.codecs.push(codec);switch(codec.name.toUpperCase()){case"RED":case"ULPFEC":description.fecMechanisms.push(codec.name.toUpperCase());break;default:break}}}SDPUtils.matchPrefix(mediaSection,"a=extmap:").forEach(function(line){description.headerExtensions.push(SDPUtils.parseExtmap(line))});return description};SDPUtils.writeRtpDescription=function(kind,caps){var sdp="";sdp+="m="+kind+" ";sdp+=caps.codecs.length>0?"9":"0";sdp+=" UDP/TLS/RTP/SAVPF ";sdp+=caps.codecs.map(function(codec){if(codec.preferredPayloadType!==undefined){return codec.preferredPayloadType}return codec.payloadType}).join(" ")+"\r\n";sdp+="c=IN IP4 0.0.0.0\r\n";sdp+="a=rtcp:9 IN IP4 0.0.0.0\r\n";caps.codecs.forEach(function(codec){sdp+=SDPUtils.writeRtpMap(codec);sdp+=SDPUtils.writeFmtp(codec);sdp+=SDPUtils.writeRtcpFb(codec)});var maxptime=0;caps.codecs.forEach(function(codec){if(codec.maxptime>maxptime){maxptime=codec.maxptime}});if(maxptime>0){sdp+="a=maxptime:"+maxptime+"\r\n"}sdp+="a=rtcp-mux\r\n";if(caps.headerExtensions){caps.headerExtensions.forEach(function(extension){sdp+=SDPUtils.writeExtmap(extension)})}return sdp};SDPUtils.parseRtpEncodingParameters=function(mediaSection){var encodingParameters=[];var description=SDPUtils.parseRtpParameters(mediaSection);var hasRed=description.fecMechanisms.indexOf("RED")!==-1;var hasUlpfec=description.fecMechanisms.indexOf("ULPFEC")!==-1;var ssrcs=SDPUtils.matchPrefix(mediaSection,"a=ssrc:").map(function(line){return SDPUtils.parseSsrcMedia(line)}).filter(function(parts){return parts.attribute==="cname"});var primarySsrc=ssrcs.length>0&&ssrcs[0].ssrc;var secondarySsrc;var flows=SDPUtils.matchPrefix(mediaSection,"a=ssrc-group:FID").map(function(line){var parts=line.substr(17).split(" ");return parts.map(function(part){return parseInt(part,10)})});if(flows.length>0&&flows[0].length>1&&flows[0][0]===primarySsrc){secondarySsrc=flows[0][1]}description.codecs.forEach(function(codec){if(codec.name.toUpperCase()==="RTX"&&codec.parameters.apt){var encParam={ssrc:primarySsrc,codecPayloadType:parseInt(codec.parameters.apt,10)};if(primarySsrc&&secondarySsrc){encParam.rtx={ssrc:secondarySsrc}}encodingParameters.push(encParam);if(hasRed){encParam=JSON.parse(JSON.stringify(encParam));encParam.fec={ssrc:primarySsrc,mechanism:hasUlpfec?"red+ulpfec":"red"};encodingParameters.push(encParam)}}});if(encodingParameters.length===0&&primarySsrc){encodingParameters.push({ssrc:primarySsrc})}var bandwidth=SDPUtils.matchPrefix(mediaSection,"b=");if(bandwidth.length){if(bandwidth[0].indexOf("b=TIAS:")===0){bandwidth=parseInt(bandwidth[0].substr(7),10)}else if(bandwidth[0].indexOf("b=AS:")===0){bandwidth=parseInt(bandwidth[0].substr(5),10)*1e3*.95-50*40*8}else{bandwidth=undefined}encodingParameters.forEach(function(params){params.maxBitrate=bandwidth})}return encodingParameters};SDPUtils.parseRtcpParameters=function(mediaSection){var rtcpParameters={};var remoteSsrc=SDPUtils.matchPrefix(mediaSection,"a=ssrc:").map(function(line){return SDPUtils.parseSsrcMedia(line)}).filter(function(obj){return obj.attribute==="cname"})[0];if(remoteSsrc){rtcpParameters.cname=remoteSsrc.value;rtcpParameters.ssrc=remoteSsrc.ssrc}var rsize=SDPUtils.matchPrefix(mediaSection,"a=rtcp-rsize");rtcpParameters.reducedSize=rsize.length>0;rtcpParameters.compound=rsize.length===0;var mux=SDPUtils.matchPrefix(mediaSection,"a=rtcp-mux");rtcpParameters.mux=mux.length>0;return rtcpParameters};SDPUtils.parseMsid=function(mediaSection){var parts;var spec=SDPUtils.matchPrefix(mediaSection,"a=msid:");if(spec.length===1){parts=spec[0].substr(7).split(" ");return{stream:parts[0],track:parts[1]}}var planB=SDPUtils.matchPrefix(mediaSection,"a=ssrc:").map(function(line){return SDPUtils.parseSsrcMedia(line)}).filter(function(msidParts){return msidParts.attribute==="msid"});if(planB.length>0){parts=planB[0].value.split(" ");return{stream:parts[0],track:parts[1]}}};SDPUtils.generateSessionId=function(){return Math.random().toString().substr(2,21)};SDPUtils.writeSessionBoilerplate=function(sessId,sessVer,sessUser){var sessionId;var version=sessVer!==undefined?sessVer:2;if(sessId){sessionId=sessId}else{sessionId=SDPUtils.generateSessionId()}var user=sessUser||"thisisadapterortc";return"v=0\r\n"+"o="+user+" "+sessionId+" "+version+" IN IP4 127.0.0.1\r\n"+"s=-\r\n"+"t=0 0\r\n"};SDPUtils.writeMediaSection=function(transceiver,caps,type,stream){var sdp=SDPUtils.writeRtpDescription(transceiver.kind,caps);sdp+=SDPUtils.writeIceParameters(transceiver.iceGatherer.getLocalParameters());sdp+=SDPUtils.writeDtlsParameters(transceiver.dtlsTransport.getLocalParameters(),type==="offer"?"actpass":"active");sdp+="a=mid:"+transceiver.mid+"\r\n";if(transceiver.direction){sdp+="a="+transceiver.direction+"\r\n"}else if(transceiver.rtpSender&&transceiver.rtpReceiver){sdp+="a=sendrecv\r\n"}else if(transceiver.rtpSender){sdp+="a=sendonly\r\n"}else if(transceiver.rtpReceiver){sdp+="a=recvonly\r\n"}else{sdp+="a=inactive\r\n"}if(transceiver.rtpSender){var msid="msid:"+stream.id+" "+transceiver.rtpSender.track.id+"\r\n";sdp+="a="+msid;sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" "+msid;if(transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" "+msid;sdp+="a=ssrc-group:FID "+transceiver.sendEncodingParameters[0].ssrc+" "+transceiver.sendEncodingParameters[0].rtx.ssrc+"\r\n"}}sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].ssrc+" cname:"+SDPUtils.localCName+"\r\n";if(transceiver.rtpSender&&transceiver.sendEncodingParameters[0].rtx){sdp+="a=ssrc:"+transceiver.sendEncodingParameters[0].rtx.ssrc+" cname:"+SDPUtils.localCName+"\r\n"}return sdp};SDPUtils.getDirection=function(mediaSection,sessionpart){var lines=SDPUtils.splitLines(mediaSection);for(var i=0;i<lines.length;i++){switch(lines[i]){case"a=sendrecv":case"a=sendonly":case"a=recvonly":case"a=inactive":return lines[i].substr(2);default:}}if(sessionpart){return SDPUtils.getDirection(sessionpart)}return"sendrecv"};SDPUtils.getKind=function(mediaSection){var lines=SDPUtils.splitLines(mediaSection);var mline=lines[0].split(" ");return mline[0].substr(2)};SDPUtils.isRejected=function(mediaSection){return mediaSection.split(" ",2)[1]==="0"};SDPUtils.parseMLine=function(mediaSection){var lines=SDPUtils.splitLines(mediaSection);var parts=lines[0].substr(2).split(" ");return{kind:parts[0],port:parseInt(parts[1],10),protocol:parts[2],fmt:parts.slice(3).join(" ")}};SDPUtils.parseOLine=function(mediaSection){var line=SDPUtils.matchPrefix(mediaSection,"o=")[0];var parts=line.substr(2).split(" ");return{username:parts[0],sessionId:parts[1],sessionVersion:parseInt(parts[2],10),netType:parts[3],addressType:parts[4],address:parts[5]}};SDPUtils.isValidSDP=function(blob){if(typeof blob!=="string"||blob.length===0){return false}var lines=SDPUtils.splitLines(blob);for(var i=0;i<lines.length;i++){if(lines[i].length<2||lines[i].charAt(1)!=="="){return false}}return true};if(typeof module==="object"){module.exports=SDPUtils}},{}],3:[function(require,module,exports){(function(global){"use strict";var adapterFactory=require("./adapter_factory.js");module.exports=adapterFactory({window:global.window})}).call(this,typeof global!=="undefined"?global:typeof self!=="undefined"?self:typeof window!=="undefined"?window:{})},{"./adapter_factory.js":4}],4:[function(require,module,exports){"use strict";var utils=require("./utils");module.exports=function(dependencies,opts){var window=dependencies&&dependencies.window;var options={shimChrome:true,shimFirefox:true,shimEdge:true,shimSafari:true};for(var key in opts){if(hasOwnProperty.call(opts,key)){options[key]=opts[key]}}var logging=utils.log;var browserDetails=utils.detectBrowser(window);var chromeShim=require("./chrome/chrome_shim")||null;var edgeShim=require("./edge/edge_shim")||null;var firefoxShim=require("./firefox/firefox_shim")||null;var safariShim=require("./safari/safari_shim")||null;var commonShim=require("./common_shim")||null;var adapter={browserDetails:browserDetails,commonShim:commonShim,extractVersion:utils.extractVersion,disableLog:utils.disableLog,disableWarnings:utils.disableWarnings};switch(browserDetails.browser){case"chrome":if(!chromeShim||!chromeShim.shimPeerConnection||!options.shimChrome){logging("Chrome shim is not included in this adapter release.");return adapter}logging("adapter.js shimming chrome.");adapter.browserShim=chromeShim;commonShim.shimCreateObjectURL(window);chromeShim.shimGetUserMedia(window);chromeShim.shimMediaStream(window);chromeShim.shimSourceObject(window);chromeShim.shimPeerConnection(window);chromeShim.shimOnTrack(window);chromeShim.shimAddTrackRemoveTrack(window);chromeShim.shimGetSendersWithDtmf(window);chromeShim.shimSenderReceiverGetStats(window);chromeShim.fixNegotiationNeeded(window);commonShim.shimRTCIceCandidate(window);commonShim.shimMaxMessageSize(window);commonShim.shimSendThrowTypeError(window);break;case"firefox":if(!firefoxShim||!firefoxShim.shimPeerConnection||!options.shimFirefox){logging("Firefox shim is not included in this adapter release.");return adapter}logging("adapter.js shimming firefox.");adapter.browserShim=firefoxShim;commonShim.shimCreateObjectURL(window);firefoxShim.shimGetUserMedia(window);firefoxShim.shimSourceObject(window);firefoxShim.shimPeerConnection(window);firefoxShim.shimOnTrack(window);firefoxShim.shimRemoveStream(window);firefoxShim.shimSenderGetStats(window);firefoxShim.shimReceiverGetStats(window);firefoxShim.shimRTCDataChannel(window);commonShim.shimRTCIceCandidate(window);commonShim.shimMaxMessageSize(window);commonShim.shimSendThrowTypeError(window);break;case"edge":if(!edgeShim||!edgeShim.shimPeerConnection||!options.shimEdge){logging("MS edge shim is not included in this adapter release.");return adapter}logging("adapter.js shimming edge.");adapter.browserShim=edgeShim;commonShim.shimCreateObjectURL(window);edgeShim.shimGetUserMedia(window);edgeShim.shimPeerConnection(window);edgeShim.shimReplaceTrack(window);commonShim.shimMaxMessageSize(window);commonShim.shimSendThrowTypeError(window);break;case"safari":if(!safariShim||!options.shimSafari){logging("Safari shim is not included in this adapter release.");return adapter}logging("adapter.js shimming safari.");adapter.browserShim=safariShim;commonShim.shimCreateObjectURL(window);safariShim.shimRTCIceServerUrls(window);safariShim.shimCreateOfferLegacy(window);safariShim.shimCallbacksAPI(window);safariShim.shimLocalStreamsAPI(window);safariShim.shimRemoteStreamsAPI(window);safariShim.shimTrackEventTransceiver(window);safariShim.shimGetUserMedia(window);commonShim.shimRTCIceCandidate(window);commonShim.shimMaxMessageSize(window);commonShim.shimSendThrowTypeError(window);break;default:logging("Unsupported browser!");break}return adapter}},{"./chrome/chrome_shim":5,"./common_shim":7,"./edge/edge_shim":8,"./firefox/firefox_shim":11,"./safari/safari_shim":13,"./utils":14}],5:[function(require,module,exports){"use strict";var utils=require("../utils.js");var logging=utils.log;function walkStats(stats,base,resultSet){if(!base||resultSet.has(base.id)){return}resultSet.set(base.id,base);Object.keys(base).forEach(function(name){if(name.endsWith("Id")){walkStats(stats,stats.get(base[name]),resultSet)}else if(name.endsWith("Ids")){base[name].forEach(function(id){walkStats(stats,stats.get(id),resultSet)})}})}function filterStats(result,track,outbound){var streamStatsType=outbound?"outbound-rtp":"inbound-rtp";var filteredResult=new Map;if(track===null){return filteredResult}var trackStats=[];result.forEach(function(value){if(value.type==="track"&&value.trackIdentifier===track.id){trackStats.push(value)}});trackStats.forEach(function(trackStat){result.forEach(function(stats){if(stats.type===streamStatsType&&stats.trackId===trackStat.id){walkStats(result,stats,filteredResult)}})});return filteredResult}module.exports={shimGetUserMedia:require("./getusermedia"),shimMediaStream:function(window){window.MediaStream=window.MediaStream||window.webkitMediaStream},shimOnTrack:function(window){if(typeof window==="object"&&window.RTCPeerConnection&&!("ontrack"in window.RTCPeerConnection.prototype)){Object.defineProperty(window.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(f){if(this._ontrack){this.removeEventListener("track",this._ontrack)}this.addEventListener("track",this._ontrack=f)},enumerable:true,configurable:true});var origSetRemoteDescription=window.RTCPeerConnection.prototype.setRemoteDescription;window.RTCPeerConnection.prototype.setRemoteDescription=function(){var pc=this;if(!pc._ontrackpoly){pc._ontrackpoly=function(e){e.stream.addEventListener("addtrack",function(te){var receiver;if(window.RTCPeerConnection.prototype.getReceivers){receiver=pc.getReceivers().find(function(r){return r.track&&r.track.id===te.track.id})}else{receiver={track:te.track}}var event=new Event("track");event.track=te.track;event.receiver=receiver;event.transceiver={receiver:receiver};event.streams=[e.stream];pc.dispatchEvent(event)});e.stream.getTracks().forEach(function(track){var receiver;if(window.RTCPeerConnection.prototype.getReceivers){receiver=pc.getReceivers().find(function(r){return r.track&&r.track.id===track.id})}else{receiver={track:track}}var event=new Event("track");event.track=track;event.receiver=receiver;event.transceiver={receiver:receiver};event.streams=[e.stream];pc.dispatchEvent(event)})};pc.addEventListener("addstream",pc._ontrackpoly)}return origSetRemoteDescription.apply(pc,arguments)}}else{utils.wrapPeerConnectionEvent(window,"track",function(e){if(!e.transceiver){Object.defineProperty(e,"transceiver",{value:{receiver:e.receiver}})}return e})}},shimGetSendersWithDtmf:function(window){if(typeof window==="object"&&window.RTCPeerConnection&&!("getSenders"in window.RTCPeerConnection.prototype)&&"createDTMFSender"in window.RTCPeerConnection.prototype){var shimSenderWithDtmf=function(pc,track){return{track:track,get dtmf(){if(this._dtmf===undefined){if(track.kind==="audio"){this._dtmf=pc.createDTMFSender(track)}else{this._dtmf=null}}return this._dtmf},_pc:pc}};if(!window.RTCPeerConnection.prototype.getSenders){window.RTCPeerConnection.prototype.getSenders=function(){this._senders=this._senders||[];return this._senders.slice()};var origAddTrack=window.RTCPeerConnection.prototype.addTrack;window.RTCPeerConnection.prototype.addTrack=function(track,stream){var pc=this;var sender=origAddTrack.apply(pc,arguments);if(!sender){sender=shimSenderWithDtmf(pc,track);pc._senders.push(sender)}return sender};var origRemoveTrack=window.RTCPeerConnection.prototype.removeTrack;window.RTCPeerConnection.prototype.removeTrack=function(sender){var pc=this;origRemoveTrack.apply(pc,arguments);var idx=pc._senders.indexOf(sender);if(idx!==-1){pc._senders.splice(idx,1)}}}var origAddStream=window.RTCPeerConnection.prototype.addStream;window.RTCPeerConnection.prototype.addStream=function(stream){var pc=this;pc._senders=pc._senders||[];origAddStream.apply(pc,[stream]);stream.getTracks().forEach(function(track){pc._senders.push(shimSenderWithDtmf(pc,track))})};var origRemoveStream=window.RTCPeerConnection.prototype.removeStream;window.RTCPeerConnection.prototype.removeStream=function(stream){var pc=this;pc._senders=pc._senders||[];origRemoveStream.apply(pc,[stream]);stream.getTracks().forEach(function(track){var sender=pc._senders.find(function(s){return s.track===track});if(sender){pc._senders.splice(pc._senders.indexOf(sender),1)}})}}else if(typeof window==="object"&&window.RTCPeerConnection&&"getSenders"in window.RTCPeerConnection.prototype&&"createDTMFSender"in window.RTCPeerConnection.prototype&&window.RTCRtpSender&&!("dtmf"in window.RTCRtpSender.prototype)){var origGetSenders=window.RTCPeerConnection.prototype.getSenders;window.RTCPeerConnection.prototype.getSenders=function(){var pc=this;var senders=origGetSenders.apply(pc,[]);senders.forEach(function(sender){sender._pc=pc});return senders};Object.defineProperty(window.RTCRtpSender.prototype,"dtmf",{get:function(){if(this._dtmf===undefined){if(this.track.kind==="audio"){this._dtmf=this._pc.createDTMFSender(this.track)}else{this._dtmf=null}}return this._dtmf}})}},shimSenderReceiverGetStats:function(window){if(!(typeof window==="object"&&window.RTCPeerConnection&&window.RTCRtpSender&&window.RTCRtpReceiver)){return}if(!("getStats"in window.RTCRtpSender.prototype)){var origGetSenders=window.RTCPeerConnection.prototype.getSenders;if(origGetSenders){window.RTCPeerConnection.prototype.getSenders=function(){var pc=this;var senders=origGetSenders.apply(pc,[]);senders.forEach(function(sender){sender._pc=pc});return senders}}var origAddTrack=window.RTCPeerConnection.prototype.addTrack;if(origAddTrack){window.RTCPeerConnection.prototype.addTrack=function(){var sender=origAddTrack.apply(this,arguments);sender._pc=this;return sender}}window.RTCRtpSender.prototype.getStats=function(){var sender=this;return this._pc.getStats().then(function(result){return filterStats(result,sender.track,true)})}}if(!("getStats"in window.RTCRtpReceiver.prototype)){var origGetReceivers=window.RTCPeerConnection.prototype.getReceivers;if(origGetReceivers){window.RTCPeerConnection.prototype.getReceivers=function(){var pc=this;var receivers=origGetReceivers.apply(pc,[]);receivers.forEach(function(receiver){receiver._pc=pc});return receivers}}utils.wrapPeerConnectionEvent(window,"track",function(e){e.receiver._pc=e.srcElement;return e});window.RTCRtpReceiver.prototype.getStats=function(){var receiver=this;return this._pc.getStats().then(function(result){return filterStats(result,receiver.track,false)})}}if(!("getStats"in window.RTCRtpSender.prototype&&"getStats"in window.RTCRtpReceiver.prototype)){return}var origGetStats=window.RTCPeerConnection.prototype.getStats;window.RTCPeerConnection.prototype.getStats=function(){var pc=this;if(arguments.length>0&&arguments[0]instanceof window.MediaStreamTrack){var track=arguments[0];var sender;var receiver;var err;pc.getSenders().forEach(function(s){if(s.track===track){if(sender){err=true}else{sender=s}}});pc.getReceivers().forEach(function(r){if(r.track===track){if(receiver){err=true}else{receiver=r}}return r.track===track});if(err||sender&&receiver){return Promise.reject(new DOMException("There are more than one sender or receiver for the track.","InvalidAccessError"))}else if(sender){return sender.getStats()}else if(receiver){return receiver.getStats()}return Promise.reject(new DOMException("There is no sender or receiver for the track.","InvalidAccessError"))}return origGetStats.apply(pc,arguments)}},shimSourceObject:function(window){var URL=window&&window.URL;if(typeof window==="object"){if(window.HTMLMediaElement&&!("srcObject"in window.HTMLMediaElement.prototype)){Object.defineProperty(window.HTMLMediaElement.prototype,"srcObject",{get:function(){return this._srcObject},set:function(stream){var self=this;this._srcObject=stream;if(this.src){URL.revokeObjectURL(this.src)}if(!stream){this.src="";return undefined}this.src=URL.createObjectURL(stream);stream.addEventListener("addtrack",function(){if(self.src){URL.revokeObjectURL(self.src)}self.src=URL.createObjectURL(stream)});stream.addEventListener("removetrack",function(){if(self.src){URL.revokeObjectURL(self.src)}self.src=URL.createObjectURL(stream)})}})}}},shimAddTrackRemoveTrackWithNative:function(window){window.RTCPeerConnection.prototype.getLocalStreams=function(){var pc=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};return Object.keys(this._shimmedLocalStreams).map(function(streamId){return pc._shimmedLocalStreams[streamId][0]})};var origAddTrack=window.RTCPeerConnection.prototype.addTrack;window.RTCPeerConnection.prototype.addTrack=function(track,stream){if(!stream){return origAddTrack.apply(this,arguments)}this._shimmedLocalStreams=this._shimmedLocalStreams||{};var sender=origAddTrack.apply(this,arguments);if(!this._shimmedLocalStreams[stream.id]){this._shimmedLocalStreams[stream.id]=[stream,sender]}else if(this._shimmedLocalStreams[stream.id].indexOf(sender)===-1){this._shimmedLocalStreams[stream.id].push(sender)}return sender};var origAddStream=window.RTCPeerConnection.prototype.addStream;window.RTCPeerConnection.prototype.addStream=function(stream){var pc=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};stream.getTracks().forEach(function(track){var alreadyExists=pc.getSenders().find(function(s){return s.track===track});if(alreadyExists){throw new DOMException("Track already exists.","InvalidAccessError")}});var existingSenders=pc.getSenders();origAddStream.apply(this,arguments);var newSenders=pc.getSenders().filter(function(newSender){return existingSenders.indexOf(newSender)===-1});this._shimmedLocalStreams[stream.id]=[stream].concat(newSenders)};var origRemoveStream=window.RTCPeerConnection.prototype.removeStream;window.RTCPeerConnection.prototype.removeStream=function(stream){this._shimmedLocalStreams=this._shimmedLocalStreams||{};delete this._shimmedLocalStreams[stream.id];return origRemoveStream.apply(this,arguments)};var origRemoveTrack=window.RTCPeerConnection.prototype.removeTrack;window.RTCPeerConnection.prototype.removeTrack=function(sender){var pc=this;this._shimmedLocalStreams=this._shimmedLocalStreams||{};if(sender){Object.keys(this._shimmedLocalStreams).forEach(function(streamId){var idx=pc._shimmedLocalStreams[streamId].indexOf(sender);if(idx!==-1){pc._shimmedLocalStreams[streamId].splice(idx,1)}if(pc._shimmedLocalStreams[streamId].length===1){delete pc._shimmedLocalStreams[streamId]}})}return origRemoveTrack.apply(this,arguments)}},shimAddTrackRemoveTrack:function(window){var browserDetails=utils.detectBrowser(window);if(window.RTCPeerConnection.prototype.addTrack&&browserDetails.version>=65){return this.shimAddTrackRemoveTrackWithNative(window)}var origGetLocalStreams=window.RTCPeerConnection.prototype.getLocalStreams;window.RTCPeerConnection.prototype.getLocalStreams=function(){var pc=this;var nativeStreams=origGetLocalStreams.apply(this);pc._reverseStreams=pc._reverseStreams||{};return nativeStreams.map(function(stream){return pc._reverseStreams[stream.id]})};var origAddStream=window.RTCPeerConnection.prototype.addStream;window.RTCPeerConnection.prototype.addStream=function(stream){var pc=this;pc._streams=pc._streams||{};pc._reverseStreams=pc._reverseStreams||{};stream.getTracks().forEach(function(track){var alreadyExists=pc.getSenders().find(function(s){return s.track===track});if(alreadyExists){throw new DOMException("Track already exists.","InvalidAccessError")}});if(!pc._reverseStreams[stream.id]){var newStream=new window.MediaStream(stream.getTracks());pc._streams[stream.id]=newStream;pc._reverseStreams[newStream.id]=stream;stream=newStream}origAddStream.apply(pc,[stream])};var origRemoveStream=window.RTCPeerConnection.prototype.removeStream;window.RTCPeerConnection.prototype.removeStream=function(stream){var pc=this;pc._streams=pc._streams||{};pc._reverseStreams=pc._reverseStreams||{};origRemoveStream.apply(pc,[pc._streams[stream.id]||stream]);delete pc._reverseStreams[pc._streams[stream.id]?pc._streams[stream.id].id:stream.id];delete pc._streams[stream.id]};window.RTCPeerConnection.prototype.addTrack=function(track,stream){var pc=this;if(pc.signalingState==="closed"){throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError")}var streams=[].slice.call(arguments,1);if(streams.length!==1||!streams[0].getTracks().find(function(t){return t===track})){throw new DOMException("The adapter.js addTrack polyfill only supports a single "+" stream which is associated with the specified track.","NotSupportedError")}var alreadyExists=pc.getSenders().find(function(s){return s.track===track});if(alreadyExists){throw new DOMException("Track already exists.","InvalidAccessError")}pc._streams=pc._streams||{};pc._reverseStreams=pc._reverseStreams||{};var oldStream=pc._streams[stream.id];if(oldStream){oldStream.addTrack(track);Promise.resolve().then(function(){pc.dispatchEvent(new Event("negotiationneeded"))})}else{var newStream=new window.MediaStream([track]);pc._streams[stream.id]=newStream;pc._reverseStreams[newStream.id]=stream;pc.addStream(newStream)}return pc.getSenders().find(function(s){return s.track===track})};function replaceInternalStreamId(pc,description){var sdp=description.sdp;Object.keys(pc._reverseStreams||[]).forEach(function(internalId){var externalStream=pc._reverseStreams[internalId];var internalStream=pc._streams[externalStream.id];sdp=sdp.replace(new RegExp(internalStream.id,"g"),externalStream.id)});return new RTCSessionDescription({type:description.type,sdp:sdp})}function replaceExternalStreamId(pc,description){var sdp=description.sdp;Object.keys(pc._reverseStreams||[]).forEach(function(internalId){var externalStream=pc._reverseStreams[internalId];var internalStream=pc._streams[externalStream.id];sdp=sdp.replace(new RegExp(externalStream.id,"g"),internalStream.id)});return new RTCSessionDescription({type:description.type,sdp:sdp})}["createOffer","createAnswer"].forEach(function(method){var nativeMethod=window.RTCPeerConnection.prototype[method];window.RTCPeerConnection.prototype[method]=function(){var pc=this;var args=arguments;var isLegacyCall=arguments.length&&typeof arguments[0]==="function";if(isLegacyCall){return nativeMethod.apply(pc,[function(description){var desc=replaceInternalStreamId(pc,description);args[0].apply(null,[desc])},function(err){if(args[1]){args[1].apply(null,err)}},arguments[2]])}return nativeMethod.apply(pc,arguments).then(function(description){return replaceInternalStreamId(pc,description)})}});var origSetLocalDescription=window.RTCPeerConnection.prototype.setLocalDescription;window.RTCPeerConnection.prototype.setLocalDescription=function(){var pc=this;if(!arguments.length||!arguments[0].type){return origSetLocalDescription.apply(pc,arguments)}arguments[0]=replaceExternalStreamId(pc,arguments[0]);return origSetLocalDescription.apply(pc,arguments)};var origLocalDescription=Object.getOwnPropertyDescriptor(window.RTCPeerConnection.prototype,"localDescription");Object.defineProperty(window.RTCPeerConnection.prototype,"localDescription",{get:function(){var pc=this;var description=origLocalDescription.get.apply(this);if(description.type===""){return description}return replaceInternalStreamId(pc,description)}});window.RTCPeerConnection.prototype.removeTrack=function(sender){var pc=this;if(pc.signalingState==="closed"){throw new DOMException("The RTCPeerConnection's signalingState is 'closed'.","InvalidStateError")}if(!sender._pc){throw new DOMException("Argument 1 of RTCPeerConnection.removeTrack "+"does not implement interface RTCRtpSender.","TypeError")}var isLocal=sender._pc===pc;if(!isLocal){throw new DOMException("Sender was not created by this connection.","InvalidAccessError")}pc._streams=pc._streams||{};var stream;Object.keys(pc._streams).forEach(function(streamid){var hasTrack=pc._streams[streamid].getTracks().find(function(track){return sender.track===track});if(hasTrack){stream=pc._streams[streamid]}});if(stream){if(stream.getTracks().length===1){pc.removeStream(pc._reverseStreams[stream.id])}else{stream.removeTrack(sender.track)}pc.dispatchEvent(new Event("negotiationneeded"))}}},shimPeerConnection:function(window){var browserDetails=utils.detectBrowser(window);if(!window.RTCPeerConnection&&window.webkitRTCPeerConnection){window.RTCPeerConnection=function(pcConfig,pcConstraints){logging("PeerConnection");if(pcConfig&&pcConfig.iceTransportPolicy){pcConfig.iceTransports=pcConfig.iceTransportPolicy}return new window.webkitRTCPeerConnection(pcConfig,pcConstraints)};window.RTCPeerConnection.prototype=window.webkitRTCPeerConnection.prototype;if(window.webkitRTCPeerConnection.generateCertificate){Object.defineProperty(window.RTCPeerConnection,"generateCertificate",{get:function(){return window.webkitRTCPeerConnection.generateCertificate}})}}var origGetStats=window.RTCPeerConnection.prototype.getStats;window.RTCPeerConnection.prototype.getStats=function(selector,successCallback,errorCallback){var pc=this;var args=arguments;if(arguments.length>0&&typeof selector==="function"){return origGetStats.apply(this,arguments)}if(origGetStats.length===0&&(arguments.length===0||typeof arguments[0]!=="function")){return origGetStats.apply(this,[])}var fixChromeStats_=function(response){var standardReport={};var reports=response.result();reports.forEach(function(report){var standardStats={id:report.id,timestamp:report.timestamp,type:{localcandidate:"local-candidate",remotecandidate:"remote-candidate"}[report.type]||report.type};report.names().forEach(function(name){standardStats[name]=report.stat(name)});standardReport[standardStats.id]=standardStats});return standardReport};var makeMapStats=function(stats){return new Map(Object.keys(stats).map(function(key){return[key,stats[key]]}))};if(arguments.length>=2){var successCallbackWrapper_=function(response){args[1](makeMapStats(fixChromeStats_(response)))};return origGetStats.apply(this,[successCallbackWrapper_,arguments[0]])}return new Promise(function(resolve,reject){origGetStats.apply(pc,[function(response){resolve(makeMapStats(fixChromeStats_(response)))},reject])}).then(successCallback,errorCallback)};if(browserDetails.version<51){["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(method){var nativeMethod=window.RTCPeerConnection.prototype[method];window.RTCPeerConnection.prototype[method]=function(){var args=arguments;var pc=this;var promise=new Promise(function(resolve,reject){nativeMethod.apply(pc,[args[0],resolve,reject])});if(args.length<2){return promise}return promise.then(function(){args[1].apply(null,[])},function(err){if(args.length>=3){args[2].apply(null,[err])}})}})}if(browserDetails.version<52){["createOffer","createAnswer"].forEach(function(method){var nativeMethod=window.RTCPeerConnection.prototype[method];window.RTCPeerConnection.prototype[method]=function(){var pc=this;if(arguments.length<1||arguments.length===1&&typeof arguments[0]==="object"){var opts=arguments.length===1?arguments[0]:undefined;return new Promise(function(resolve,reject){nativeMethod.apply(pc,[resolve,reject,opts])})}return nativeMethod.apply(this,arguments)}})}["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(method){var nativeMethod=window.RTCPeerConnection.prototype[method];window.RTCPeerConnection.prototype[method]=function(){arguments[0]=new(method==="addIceCandidate"?window.RTCIceCandidate:window.RTCSessionDescription)(arguments[0]);return nativeMethod.apply(this,arguments)}});var nativeAddIceCandidate=window.RTCPeerConnection.prototype.addIceCandidate;window.RTCPeerConnection.prototype.addIceCandidate=function(){if(!arguments[0]){if(arguments[1]){arguments[1].apply(null)}return Promise.resolve()}return nativeAddIceCandidate.apply(this,arguments)}},fixNegotiationNeeded:function(window){utils.wrapPeerConnectionEvent(window,"negotiationneeded",function(e){var pc=e.target;if(pc.signalingState!=="stable"){return}return e})},shimGetDisplayMedia:function(window,getSourceId){if("getDisplayMedia"in window.navigator){return}if(typeof getSourceId!=="function"){console.error("shimGetDisplayMedia: getSourceId argument is not "+"a function");return}navigator.getDisplayMedia=function(constraints){return getSourceId(constraints).then(function(sourceId){constraints.video={mandatory:{chromeMediaSource:"desktop",chromeMediaSourceId:sourceId,maxFrameRate:constraints.video.frameRate||3}};return navigator.mediaDevices.getUserMedia(constraints)})}}}},{"../utils.js":14,"./getusermedia":6}],6:[function(require,module,exports){"use strict";var utils=require("../utils.js");var logging=utils.log;module.exports=function(window){var browserDetails=utils.detectBrowser(window);var navigator=window&&window.navigator;var constraintsToChrome_=function(c){if(typeof c!=="object"||c.mandatory||c.optional){return c}var cc={};Object.keys(c).forEach(function(key){if(key==="require"||key==="advanced"||key==="mediaSource"){return}var r=typeof c[key]==="object"?c[key]:{ideal:c[key]};if(r.exact!==undefined&&typeof r.exact==="number"){r.min=r.max=r.exact}var oldname_=function(prefix,name){if(prefix){return prefix+name.charAt(0).toUpperCase()+name.slice(1)}return name==="deviceId"?"sourceId":name};if(r.ideal!==undefined){cc.optional=cc.optional||[];var oc={};if(typeof r.ideal==="number"){oc[oldname_("min",key)]=r.ideal;cc.optional.push(oc);oc={};oc[oldname_("max",key)]=r.ideal;cc.optional.push(oc)}else{oc[oldname_("",key)]=r.ideal;cc.optional.push(oc)}}if(r.exact!==undefined&&typeof r.exact!=="number"){cc.mandatory=cc.mandatory||{};cc.mandatory[oldname_("",key)]=r.exact}else{["min","max"].forEach(function(mix){if(r[mix]!==undefined){cc.mandatory=cc.mandatory||{};cc.mandatory[oldname_(mix,key)]=r[mix]}})}});if(c.advanced){cc.optional=(cc.optional||[]).concat(c.advanced)}return cc};var shimConstraints_=function(constraints,func){if(browserDetails.version>=61){return func(constraints)}constraints=JSON.parse(JSON.stringify(constraints));if(constraints&&typeof constraints.audio==="object"){var remap=function(obj,a,b){if(a in obj&&!(b in obj)){obj[b]=obj[a];delete obj[a]}};constraints=JSON.parse(JSON.stringify(constraints));remap(constraints.audio,"autoGainControl","googAutoGainControl");remap(constraints.audio,"noiseSuppression","googNoiseSuppression");constraints.audio=constraintsToChrome_(constraints.audio)}if(constraints&&typeof constraints.video==="object"){var face=constraints.video.facingMode;face=face&&(typeof face==="object"?face:{ideal:face});var getSupportedFacingModeLies=browserDetails.version<66;if(face&&(face.exact==="user"||face.exact==="environment"||face.ideal==="user"||face.ideal==="environment")&&!(navigator.mediaDevices.getSupportedConstraints&&navigator.mediaDevices.getSupportedConstraints().facingMode&&!getSupportedFacingModeLies)){delete constraints.video.facingMode;var matches;if(face.exact==="environment"||face.ideal==="environment"){matches=["back","rear"]}else if(face.exact==="user"||face.ideal==="user"){matches=["front"]}if(matches){return navigator.mediaDevices.enumerateDevices().then(function(devices){devices=devices.filter(function(d){return d.kind==="videoinput"});var dev=devices.find(function(d){return matches.some(function(match){return d.label.toLowerCase().indexOf(match)!==-1})});if(!dev&&devices.length&&matches.indexOf("back")!==-1){dev=devices[devices.length-1]}if(dev){constraints.video.deviceId=face.exact?{exact:dev.deviceId}:{ideal:dev.deviceId}}constraints.video=constraintsToChrome_(constraints.video);logging("chrome: "+JSON.stringify(constraints));return func(constraints)})}}constraints.video=constraintsToChrome_(constraints.video)}logging("chrome: "+JSON.stringify(constraints));return func(constraints)};var shimError_=function(e){if(browserDetails.version>=64){return e}return{name:{PermissionDeniedError:"NotAllowedError",PermissionDismissedError:"NotAllowedError",InvalidStateError:"NotAllowedError",DevicesNotFoundError:"NotFoundError",ConstraintNotSatisfiedError:"OverconstrainedError",TrackStartError:"NotReadableError",MediaDeviceFailedDueToShutdown:"NotAllowedError",MediaDeviceKillSwitchOn:"NotAllowedError",TabCaptureError:"AbortError",ScreenCaptureError:"AbortError",DeviceCaptureError:"AbortError"}[e.name]||e.name,message:e.message,constraint:e.constraint||e.constraintName,toString:function(){return this.name+(this.message&&": ")+this.message}}};var getUserMedia_=function(constraints,onSuccess,onError){shimConstraints_(constraints,function(c){navigator.webkitGetUserMedia(c,onSuccess,function(e){if(onError){onError(shimError_(e))}})})};navigator.getUserMedia=getUserMedia_;var getUserMediaPromise_=function(constraints){return new Promise(function(resolve,reject){navigator.getUserMedia(constraints,resolve,reject)})};if(!navigator.mediaDevices){navigator.mediaDevices={getUserMedia:getUserMediaPromise_,enumerateDevices:function(){return new Promise(function(resolve){var kinds={audio:"audioinput",video:"videoinput"};return window.MediaStreamTrack.getSources(function(devices){resolve(devices.map(function(device){return{label:device.label,kind:kinds[device.kind],deviceId:device.id,groupId:""}}))})})},getSupportedConstraints:function(){return{deviceId:true,echoCancellation:true,facingMode:true,frameRate:true,height:true,width:true}}}}if(!navigator.mediaDevices.getUserMedia){navigator.mediaDevices.getUserMedia=function(constraints){return getUserMediaPromise_(constraints)}}else{var origGetUserMedia=navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);navigator.mediaDevices.getUserMedia=function(cs){return shimConstraints_(cs,function(c){return origGetUserMedia(c).then(function(stream){if(c.audio&&!stream.getAudioTracks().length||c.video&&!stream.getVideoTracks().length){stream.getTracks().forEach(function(track){track.stop()});throw new DOMException("","NotFoundError")}return stream},function(e){return Promise.reject(shimError_(e))})})}}if(typeof navigator.mediaDevices.addEventListener==="undefined"){navigator.mediaDevices.addEventListener=function(){logging("Dummy mediaDevices.addEventListener called.")}}if(typeof navigator.mediaDevices.removeEventListener==="undefined"){navigator.mediaDevices.removeEventListener=function(){logging("Dummy mediaDevices.removeEventListener called.")}}}},{"../utils.js":14}],7:[function(require,module,exports){"use strict";var SDPUtils=require("sdp");var utils=require("./utils");module.exports={shimRTCIceCandidate:function(window){if(!window.RTCIceCandidate||window.RTCIceCandidate&&"foundation"in window.RTCIceCandidate.prototype){return}var NativeRTCIceCandidate=window.RTCIceCandidate;window.RTCIceCandidate=function(args){if(typeof args==="object"&&args.candidate&&args.candidate.indexOf("a=")===0){args=JSON.parse(JSON.stringify(args));args.candidate=args.candidate.substr(2)}if(args.candidate&&args.candidate.length){var nativeCandidate=new NativeRTCIceCandidate(args);var parsedCandidate=SDPUtils.parseCandidate(args.candidate);var augmentedCandidate=Object.assign(nativeCandidate,parsedCandidate);augmentedCandidate.toJSON=function(){return{candidate:augmentedCandidate.candidate,sdpMid:augmentedCandidate.sdpMid,sdpMLineIndex:augmentedCandidate.sdpMLineIndex,usernameFragment:augmentedCandidate.usernameFragment}};return augmentedCandidate}return new NativeRTCIceCandidate(args)};window.RTCIceCandidate.prototype=NativeRTCIceCandidate.prototype;utils.wrapPeerConnectionEvent(window,"icecandidate",function(e){if(e.candidate){Object.defineProperty(e,"candidate",{value:new window.RTCIceCandidate(e.candidate),writable:"false"})}return e})},shimCreateObjectURL:function(window){var URL=window&&window.URL;if(!(typeof window==="object"&&window.HTMLMediaElement&&"srcObject"in window.HTMLMediaElement.prototype&&URL.createObjectURL&&URL.revokeObjectURL)){return undefined}var nativeCreateObjectURL=URL.createObjectURL.bind(URL);var nativeRevokeObjectURL=URL.revokeObjectURL.bind(URL);var streams=new Map,newId=0;URL.createObjectURL=function(stream){if("getTracks"in stream){var url="polyblob:"+ ++newId;streams.set(url,stream);utils.deprecated("URL.createObjectURL(stream)","elem.srcObject = stream");return url}return nativeCreateObjectURL(stream)};URL.revokeObjectURL=function(url){nativeRevokeObjectURL(url);streams.delete(url)};var dsc=Object.getOwnPropertyDescriptor(window.HTMLMediaElement.prototype,"src");Object.defineProperty(window.HTMLMediaElement.prototype,"src",{get:function(){return dsc.get.apply(this)},set:function(url){this.srcObject=streams.get(url)||null;return dsc.set.apply(this,[url])}});var nativeSetAttribute=window.HTMLMediaElement.prototype.setAttribute;window.HTMLMediaElement.prototype.setAttribute=function(){if(arguments.length===2&&(""+arguments[0]).toLowerCase()==="src"){this.srcObject=streams.get(arguments[1])||null}return nativeSetAttribute.apply(this,arguments)}},shimMaxMessageSize:function(window){if(window.RTCSctpTransport||!window.RTCPeerConnection){return}var browserDetails=utils.detectBrowser(window);if(!("sctp"in window.RTCPeerConnection.prototype)){Object.defineProperty(window.RTCPeerConnection.prototype,"sctp",{get:function(){return typeof this._sctp==="undefined"?null:this._sctp}})}var sctpInDescription=function(description){var sections=SDPUtils.splitSections(description.sdp);sections.shift();return sections.some(function(mediaSection){var mLine=SDPUtils.parseMLine(mediaSection);return mLine&&mLine.kind==="application"&&mLine.protocol.indexOf("SCTP")!==-1})};var getRemoteFirefoxVersion=function(description){var match=description.sdp.match(/mozilla...THIS_IS_SDPARTA-(\d+)/);if(match===null||match.length<2){return-1}var version=parseInt(match[1],10);return version!==version?-1:version};var getCanSendMaxMessageSize=function(remoteIsFirefox){var canSendMaxMessageSize=65536;if(browserDetails.browser==="firefox"){if(browserDetails.version<57){if(remoteIsFirefox===-1){canSendMaxMessageSize=16384}else{canSendMaxMessageSize=2147483637}}else if(browserDetails.version<60){canSendMaxMessageSize=browserDetails.version===57?65535:65536}else{canSendMaxMessageSize=2147483637}}return canSendMaxMessageSize};var getMaxMessageSize=function(description,remoteIsFirefox){var maxMessageSize=65536;if(browserDetails.browser==="firefox"&&browserDetails.version===57){maxMessageSize=65535}var match=SDPUtils.matchPrefix(description.sdp,"a=max-message-size:");if(match.length>0){maxMessageSize=parseInt(match[0].substr(19),10)}else if(browserDetails.browser==="firefox"&&remoteIsFirefox!==-1){maxMessageSize=2147483637}return maxMessageSize};var origSetRemoteDescription=window.RTCPeerConnection.prototype.setRemoteDescription;window.RTCPeerConnection.prototype.setRemoteDescription=function(){var pc=this;pc._sctp=null;if(sctpInDescription(arguments[0])){var isFirefox=getRemoteFirefoxVersion(arguments[0]);var canSendMMS=getCanSendMaxMessageSize(isFirefox);var remoteMMS=getMaxMessageSize(arguments[0],isFirefox);var maxMessageSize;if(canSendMMS===0&&remoteMMS===0){maxMessageSize=Number.POSITIVE_INFINITY}else if(canSendMMS===0||remoteMMS===0){maxMessageSize=Math.max(canSendMMS,remoteMMS)}else{maxMessageSize=Math.min(canSendMMS,remoteMMS)}var sctp={};Object.defineProperty(sctp,"maxMessageSize",{get:function(){return maxMessageSize}});pc._sctp=sctp}return origSetRemoteDescription.apply(pc,arguments)}},shimSendThrowTypeError:function(window){if(!(window.RTCPeerConnection&&"createDataChannel"in window.RTCPeerConnection.prototype)){return}function wrapDcSend(dc,pc){var origDataChannelSend=dc.send;dc.send=function(){var data=arguments[0];var length=data.length||data.size||data.byteLength;if(dc.readyState==="open"&&pc.sctp&&length>pc.sctp.maxMessageSize){throw new TypeError("Message too large (can send a maximum of "+pc.sctp.maxMessageSize+" bytes)")}return origDataChannelSend.apply(dc,arguments)}}var origCreateDataChannel=window.RTCPeerConnection.prototype.createDataChannel;window.RTCPeerConnection.prototype.createDataChannel=function(){var pc=this;var dataChannel=origCreateDataChannel.apply(pc,arguments);wrapDcSend(dataChannel,pc);return dataChannel};utils.wrapPeerConnectionEvent(window,"datachannel",function(e){wrapDcSend(e.channel,e.target);return e})}}},{"./utils":14,sdp:2}],8:[function(require,module,exports){"use strict";var utils=require("../utils");var filterIceServers=require("./filtericeservers");var shimRTCPeerConnection=require("rtcpeerconnection-shim");module.exports={shimGetUserMedia:require("./getusermedia"),shimPeerConnection:function(window){var browserDetails=utils.detectBrowser(window);if(window.RTCIceGatherer){if(!window.RTCIceCandidate){window.RTCIceCandidate=function(args){return args}}if(!window.RTCSessionDescription){window.RTCSessionDescription=function(args){return args}}if(browserDetails.version<15025){var origMSTEnabled=Object.getOwnPropertyDescriptor(window.MediaStreamTrack.prototype,"enabled");Object.defineProperty(window.MediaStreamTrack.prototype,"enabled",{set:function(value){origMSTEnabled.set.call(this,value);var ev=new Event("enabled");ev.enabled=value;this.dispatchEvent(ev)}})}}if(window.RTCRtpSender&&!("dtmf"in window.RTCRtpSender.prototype)){Object.defineProperty(window.RTCRtpSender.prototype,"dtmf",{get:function(){if(this._dtmf===undefined){if(this.track.kind==="audio"){this._dtmf=new window.RTCDtmfSender(this)}else if(this.track.kind==="video"){this._dtmf=null}}return this._dtmf}})}if(window.RTCDtmfSender&&!window.RTCDTMFSender){window.RTCDTMFSender=window.RTCDtmfSender}var RTCPeerConnectionShim=shimRTCPeerConnection(window,browserDetails.version);window.RTCPeerConnection=function(config){if(config&&config.iceServers){config.iceServers=filterIceServers(config.iceServers)}return new RTCPeerConnectionShim(config)};window.RTCPeerConnection.prototype=RTCPeerConnectionShim.prototype},shimReplaceTrack:function(window){if(window.RTCRtpSender&&!("replaceTrack"in window.RTCRtpSender.prototype)){window.RTCRtpSender.prototype.replaceTrack=window.RTCRtpSender.prototype.setTrack}}}},{"../utils":14,"./filtericeservers":9,"./getusermedia":10,"rtcpeerconnection-shim":1}],9:[function(require,module,exports){"use strict";var utils=require("../utils");module.exports=function(iceServers,edgeVersion){var hasTurn=false;iceServers=JSON.parse(JSON.stringify(iceServers));return iceServers.filter(function(server){if(server&&(server.urls||server.url)){var urls=server.urls||server.url;if(server.url&&!server.urls){utils.deprecated("RTCIceServer.url","RTCIceServer.urls")}var isString=typeof urls==="string";if(isString){urls=[urls]}urls=urls.filter(function(url){var validTurn=url.indexOf("turn:")===0&&url.indexOf("transport=udp")!==-1&&url.indexOf("turn:[")===-1&&!hasTurn;if(validTurn){hasTurn=true;return true}return url.indexOf("stun:")===0&&edgeVersion>=14393&&url.indexOf("?transport=udp")===-1});delete server.url;server.urls=isString?urls[0]:urls;return!!urls.length}})}},{"../utils":14}],10:[function(require,module,exports){"use strict";module.exports=function(window){var navigator=window&&window.navigator;var shimError_=function(e){return{name:{PermissionDeniedError:"NotAllowedError"}[e.name]||e.name,message:e.message,constraint:e.constraint,toString:function(){return this.name}}};var origGetUserMedia=navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);navigator.mediaDevices.getUserMedia=function(c){return origGetUserMedia(c).catch(function(e){return Promise.reject(shimError_(e))})}}},{}],11:[function(require,module,exports){"use strict";var utils=require("../utils");module.exports={shimGetUserMedia:require("./getusermedia"),shimOnTrack:function(window){if(typeof window==="object"&&window.RTCPeerConnection&&!("ontrack"in window.RTCPeerConnection.prototype)){Object.defineProperty(window.RTCPeerConnection.prototype,"ontrack",{get:function(){return this._ontrack},set:function(f){if(this._ontrack){this.removeEventListener("track",this._ontrack);this.removeEventListener("addstream",this._ontrackpoly)}this.addEventListener("track",this._ontrack=f);this.addEventListener("addstream",this._ontrackpoly=function(e){e.stream.getTracks().forEach(function(track){var event=new Event("track");event.track=track;event.receiver={track:track};event.transceiver={receiver:event.receiver};event.streams=[e.stream];this.dispatchEvent(event)}.bind(this))}.bind(this))},enumerable:true,configurable:true})}if(typeof window==="object"&&window.RTCTrackEvent&&"receiver"in window.RTCTrackEvent.prototype&&!("transceiver"in window.RTCTrackEvent.prototype)){Object.defineProperty(window.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})}},shimSourceObject:function(window){if(typeof window==="object"){if(window.HTMLMediaElement&&!("srcObject"in window.HTMLMediaElement.prototype)){Object.defineProperty(window.HTMLMediaElement.prototype,"srcObject",{get:function(){return this.mozSrcObject},set:function(stream){this.mozSrcObject=stream}})}}},shimPeerConnection:function(window){var browserDetails=utils.detectBrowser(window);if(typeof window!=="object"||!(window.RTCPeerConnection||window.mozRTCPeerConnection)){return}if(!window.RTCPeerConnection){window.RTCPeerConnection=function(pcConfig,pcConstraints){if(browserDetails.version<38){if(pcConfig&&pcConfig.iceServers){var newIceServers=[];for(var i=0;i<pcConfig.iceServers.length;i++){var server=pcConfig.iceServers[i];if(server.hasOwnProperty("urls")){for(var j=0;j<server.urls.length;j++){var newServer={url:server.urls[j]};if(server.urls[j].indexOf("turn")===0){newServer.username=server.username;newServer.credential=server.credential}newIceServers.push(newServer)}}else{newIceServers.push(pcConfig.iceServers[i])}}pcConfig.iceServers=newIceServers}}return new window.mozRTCPeerConnection(pcConfig,pcConstraints)};window.RTCPeerConnection.prototype=window.mozRTCPeerConnection.prototype;if(window.mozRTCPeerConnection.generateCertificate){Object.defineProperty(window.RTCPeerConnection,"generateCertificate",{get:function(){return window.mozRTCPeerConnection.generateCertificate}})}window.RTCSessionDescription=window.mozRTCSessionDescription;window.RTCIceCandidate=window.mozRTCIceCandidate}["setLocalDescription","setRemoteDescription","addIceCandidate"].forEach(function(method){var nativeMethod=window.RTCPeerConnection.prototype[method];window.RTCPeerConnection.prototype[method]=function(){arguments[0]=new(method==="addIceCandidate"?window.RTCIceCandidate:window.RTCSessionDescription)(arguments[0]);return nativeMethod.apply(this,arguments)}});var nativeAddIceCandidate=window.RTCPeerConnection.prototype.addIceCandidate;window.RTCPeerConnection.prototype.addIceCandidate=function(){if(!arguments[0]){if(arguments[1]){arguments[1].apply(null)}return Promise.resolve()}return nativeAddIceCandidate.apply(this,arguments)};var makeMapStats=function(stats){var map=new Map;Object.keys(stats).forEach(function(key){map.set(key,stats[key]);map[key]=stats[key]});return map};var modernStatsTypes={inboundrtp:"inbound-rtp",outboundrtp:"outbound-rtp",candidatepair:"candidate-pair",localcandidate:"local-candidate",remotecandidate:"remote-candidate"};var nativeGetStats=window.RTCPeerConnection.prototype.getStats;window.RTCPeerConnection.prototype.getStats=function(selector,onSucc,onErr){return nativeGetStats.apply(this,[selector||null]).then(function(stats){if(browserDetails.version<48){stats=makeMapStats(stats)}if(browserDetails.version<53&&!onSucc){try{stats.forEach(function(stat){stat.type=modernStatsTypes[stat.type]||stat.type})}catch(e){if(e.name!=="TypeError"){throw e}stats.forEach(function(stat,i){stats.set(i,Object.assign({},stat,{type:modernStatsTypes[stat.type]||stat.type}))})}}return stats}).then(onSucc,onErr)}},shimSenderGetStats:function(window){if(!(typeof window==="object"&&window.RTCPeerConnection&&window.RTCRtpSender)){return}if(window.RTCRtpSender&&"getStats"in window.RTCRtpSender.prototype){return}var origGetSenders=window.RTCPeerConnection.prototype.getSenders;if(origGetSenders){window.RTCPeerConnection.prototype.getSenders=function(){var pc=this;var senders=origGetSenders.apply(pc,[]);senders.forEach(function(sender){sender._pc=pc});return senders}}var origAddTrack=window.RTCPeerConnection.prototype.addTrack;if(origAddTrack){window.RTCPeerConnection.prototype.addTrack=function(){var sender=origAddTrack.apply(this,arguments);sender._pc=this;return sender}}window.RTCRtpSender.prototype.getStats=function(){return this.track?this._pc.getStats(this.track):Promise.resolve(new Map)}},shimReceiverGetStats:function(window){if(!(typeof window==="object"&&window.RTCPeerConnection&&window.RTCRtpSender)){return}if(window.RTCRtpSender&&"getStats"in window.RTCRtpReceiver.prototype){return}var origGetReceivers=window.RTCPeerConnection.prototype.getReceivers;if(origGetReceivers){window.RTCPeerConnection.prototype.getReceivers=function(){var pc=this;var receivers=origGetReceivers.apply(pc,[]);receivers.forEach(function(receiver){receiver._pc=pc});return receivers}}utils.wrapPeerConnectionEvent(window,"track",function(e){e.receiver._pc=e.srcElement;return e});window.RTCRtpReceiver.prototype.getStats=function(){return this._pc.getStats(this.track)}},shimRemoveStream:function(window){if(!window.RTCPeerConnection||"removeStream"in window.RTCPeerConnection.prototype){return}window.RTCPeerConnection.prototype.removeStream=function(stream){var pc=this;utils.deprecated("removeStream","removeTrack");this.getSenders().forEach(function(sender){if(sender.track&&stream.getTracks().indexOf(sender.track)!==-1){pc.removeTrack(sender)}})}},shimRTCDataChannel:function(window){if(window.DataChannel&&!window.RTCDataChannel){window.RTCDataChannel=window.DataChannel}},shimGetDisplayMedia:function(window,preferredMediaSource){if("getDisplayMedia"in window.navigator){return}navigator.getDisplayMedia=function(constraints){if(!(constraints&&constraints.video)){var err=new DOMException("getDisplayMedia without video "+"constraints is undefined");err.name="NotFoundError";err.code=8;return Promise.reject(err)}if(constraints.video===true){constraints.video={mediaSource:preferredMediaSource}}else{constraints.video.mediaSource=preferredMediaSource}return navigator.mediaDevices.getUserMedia(constraints)}}}},{"../utils":14,"./getusermedia":12}],12:[function(require,module,exports){"use strict";var utils=require("../utils");var logging=utils.log;module.exports=function(window){var browserDetails=utils.detectBrowser(window);var navigator=window&&window.navigator;var MediaStreamTrack=window&&window.MediaStreamTrack;var shimError_=function(e){return{name:{InternalError:"NotReadableError",NotSupportedError:"TypeError",PermissionDeniedError:"NotAllowedError",SecurityError:"NotAllowedError"}[e.name]||e.name,message:{"The operation is insecure.":"The request is not allowed by the "+"user agent or the platform in the current context."}[e.message]||e.message,constraint:e.constraint,toString:function(){return this.name+(this.message&&": ")+this.message}}};var getUserMedia_=function(constraints,onSuccess,onError){var constraintsToFF37_=function(c){if(typeof c!=="object"||c.require){return c}var require=[];Object.keys(c).forEach(function(key){if(key==="require"||key==="advanced"||key==="mediaSource"){return}var r=c[key]=typeof c[key]==="object"?c[key]:{ideal:c[key]};if(r.min!==undefined||r.max!==undefined||r.exact!==undefined){require.push(key)}if(r.exact!==undefined){if(typeof r.exact==="number"){r.min=r.max=r.exact}else{c[key]=r.exact}delete r.exact}if(r.ideal!==undefined){c.advanced=c.advanced||[];var oc={};if(typeof r.ideal==="number"){oc[key]={min:r.ideal,max:r.ideal}}else{oc[key]=r.ideal}c.advanced.push(oc);delete r.ideal;if(!Object.keys(r).length){delete c[key]}}});if(require.length){c.require=require}return c};constraints=JSON.parse(JSON.stringify(constraints));if(browserDetails.version<38){logging("spec: "+JSON.stringify(constraints));if(constraints.audio){constraints.audio=constraintsToFF37_(constraints.audio)}if(constraints.video){constraints.video=constraintsToFF37_(constraints.video)}logging("ff37: "+JSON.stringify(constraints))}return navigator.mozGetUserMedia(constraints,onSuccess,function(e){onError(shimError_(e))})};var getUserMediaPromise_=function(constraints){return new Promise(function(resolve,reject){getUserMedia_(constraints,resolve,reject)})};if(!navigator.mediaDevices){navigator.mediaDevices={getUserMedia:getUserMediaPromise_,addEventListener:function(){},removeEventListener:function(){}}}navigator.mediaDevices.enumerateDevices=navigator.mediaDevices.enumerateDevices||function(){return new Promise(function(resolve){var infos=[{kind:"audioinput",deviceId:"default",label:"",groupId:""},{kind:"videoinput",deviceId:"default",label:"",groupId:""}];resolve(infos)})};if(browserDetails.version<41){var orgEnumerateDevices=navigator.mediaDevices.enumerateDevices.bind(navigator.mediaDevices);navigator.mediaDevices.enumerateDevices=function(){return orgEnumerateDevices().then(undefined,function(e){if(e.name==="NotFoundError"){return[]}throw e})}}if(browserDetails.version<49){var origGetUserMedia=navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);navigator.mediaDevices.getUserMedia=function(c){return origGetUserMedia(c).then(function(stream){if(c.audio&&!stream.getAudioTracks().length||c.video&&!stream.getVideoTracks().length){stream.getTracks().forEach(function(track){track.stop()});throw new DOMException("The object can not be found here.","NotFoundError")}return stream},function(e){return Promise.reject(shimError_(e))})}}if(!(browserDetails.version>55&&"autoGainControl"in navigator.mediaDevices.getSupportedConstraints())){var remap=function(obj,a,b){if(a in obj&&!(b in obj)){obj[b]=obj[a];delete obj[a]}};var nativeGetUserMedia=navigator.mediaDevices.getUserMedia.bind(navigator.mediaDevices);navigator.mediaDevices.getUserMedia=function(c){if(typeof c==="object"&&typeof c.audio==="object"){c=JSON.parse(JSON.stringify(c));remap(c.audio,"autoGainControl","mozAutoGainControl");remap(c.audio,"noiseSuppression","mozNoiseSuppression")}return nativeGetUserMedia(c)};if(MediaStreamTrack&&MediaStreamTrack.prototype.getSettings){var nativeGetSettings=MediaStreamTrack.prototype.getSettings;MediaStreamTrack.prototype.getSettings=function(){var obj=nativeGetSettings.apply(this,arguments);remap(obj,"mozAutoGainControl","autoGainControl");remap(obj,"mozNoiseSuppression","noiseSuppression");return obj}}if(MediaStreamTrack&&MediaStreamTrack.prototype.applyConstraints){var nativeApplyConstraints=MediaStreamTrack.prototype.applyConstraints;MediaStreamTrack.prototype.applyConstraints=function(c){if(this.kind==="audio"&&typeof c==="object"){c=JSON.parse(JSON.stringify(c));remap(c,"autoGainControl","mozAutoGainControl");remap(c,"noiseSuppression","mozNoiseSuppression")}return nativeApplyConstraints.apply(this,[c])}}}navigator.getUserMedia=function(constraints,onSuccess,onError){if(browserDetails.version<44){return getUserMedia_(constraints,onSuccess,onError)}utils.deprecated("navigator.getUserMedia","navigator.mediaDevices.getUserMedia");navigator.mediaDevices.getUserMedia(constraints).then(onSuccess,onError)}}},{"../utils":14}],13:[function(require,module,exports){"use strict";var utils=require("../utils");module.exports={shimLocalStreamsAPI:function(window){if(typeof window!=="object"||!window.RTCPeerConnection){return}if(!("getLocalStreams"in window.RTCPeerConnection.prototype)){window.RTCPeerConnection.prototype.getLocalStreams=function(){if(!this._localStreams){this._localStreams=[]}return this._localStreams}}if(!("getStreamById"in window.RTCPeerConnection.prototype)){window.RTCPeerConnection.prototype.getStreamById=function(id){var result=null;if(this._localStreams){this._localStreams.forEach(function(stream){if(stream.id===id){result=stream}})}if(this._remoteStreams){this._remoteStreams.forEach(function(stream){if(stream.id===id){result=stream}})}return result}}if(!("addStream"in window.RTCPeerConnection.prototype)){var _addTrack=window.RTCPeerConnection.prototype.addTrack;window.RTCPeerConnection.prototype.addStream=function(stream){if(!this._localStreams){this._localStreams=[]}if(this._localStreams.indexOf(stream)===-1){this._localStreams.push(stream)}var pc=this;stream.getTracks().forEach(function(track){_addTrack.call(pc,track,stream)})};window.RTCPeerConnection.prototype.addTrack=function(track,stream){if(stream){if(!this._localStreams){this._localStreams=[stream]}else if(this._localStreams.indexOf(stream)===-1){this._localStreams.push(stream)}}return _addTrack.call(this,track,stream)}}if(!("removeStream"in window.RTCPeerConnection.prototype)){window.RTCPeerConnection.prototype.removeStream=function(stream){if(!this._localStreams){this._localStreams=[]}var index=this._localStreams.indexOf(stream);if(index===-1){return}this._localStreams.splice(index,1);var pc=this;var tracks=stream.getTracks();this.getSenders().forEach(function(sender){if(tracks.indexOf(sender.track)!==-1){pc.removeTrack(sender)}})}}},shimRemoteStreamsAPI:function(window){if(typeof window!=="object"||!window.RTCPeerConnection){return}if(!("getRemoteStreams"in window.RTCPeerConnection.prototype)){window.RTCPeerConnection.prototype.getRemoteStreams=function(){return this._remoteStreams?this._remoteStreams:[]}}if(!("onaddstream"in window.RTCPeerConnection.prototype)){Object.defineProperty(window.RTCPeerConnection.prototype,"onaddstream",{get:function(){return this._onaddstream},set:function(f){if(this._onaddstream){this.removeEventListener("addstream",this._onaddstream)}this.addEventListener("addstream",this._onaddstream=f)}});var origSetRemoteDescription=window.RTCPeerConnection.prototype.setRemoteDescription;window.RTCPeerConnection.prototype.setRemoteDescription=function(){var pc=this;if(!this._onaddstreampoly){this.addEventListener("track",this._onaddstreampoly=function(e){e.streams.forEach(function(stream){if(!pc._remoteStreams){pc._remoteStreams=[]}if(pc._remoteStreams.indexOf(stream)>=0){return}pc._remoteStreams.push(stream);var event=new Event("addstream");event.stream=stream;pc.dispatchEvent(event)})})}return origSetRemoteDescription.apply(pc,arguments)}}},shimCallbacksAPI:function(window){if(typeof window!=="object"||!window.RTCPeerConnection){return}var prototype=window.RTCPeerConnection.prototype;var createOffer=prototype.createOffer;var createAnswer=prototype.createAnswer;var setLocalDescription=prototype.setLocalDescription;var setRemoteDescription=prototype.setRemoteDescription;var addIceCandidate=prototype.addIceCandidate;prototype.createOffer=function(successCallback,failureCallback){var options=arguments.length>=2?arguments[2]:arguments[0];var promise=createOffer.apply(this,[options]);if(!failureCallback){return promise}promise.then(successCallback,failureCallback);return Promise.resolve()};prototype.createAnswer=function(successCallback,failureCallback){var options=arguments.length>=2?arguments[2]:arguments[0];var promise=createAnswer.apply(this,[options]);if(!failureCallback){return promise}promise.then(successCallback,failureCallback);return Promise.resolve()};var withCallback=function(description,successCallback,failureCallback){var promise=setLocalDescription.apply(this,[description]);if(!failureCallback){return promise}promise.then(successCallback,failureCallback);return Promise.resolve()};prototype.setLocalDescription=withCallback;withCallback=function(description,successCallback,failureCallback){var promise=setRemoteDescription.apply(this,[description]);if(!failureCallback){return promise}promise.then(successCallback,failureCallback);return Promise.resolve()};prototype.setRemoteDescription=withCallback;withCallback=function(candidate,successCallback,failureCallback){var promise=addIceCandidate.apply(this,[candidate]);if(!failureCallback){return promise}promise.then(successCallback,failureCallback);return Promise.resolve()};prototype.addIceCandidate=withCallback},shimGetUserMedia:function(window){var navigator=window&&window.navigator;if(!navigator.getUserMedia){if(navigator.webkitGetUserMedia){navigator.getUserMedia=navigator.webkitGetUserMedia.bind(navigator)}else if(navigator.mediaDevices&&navigator.mediaDevices.getUserMedia){navigator.getUserMedia=function(constraints,cb,errcb){navigator.mediaDevices.getUserMedia(constraints).then(cb,errcb)}.bind(navigator)}}},shimRTCIceServerUrls:function(window){var OrigPeerConnection=window.RTCPeerConnection;window.RTCPeerConnection=function(pcConfig,pcConstraints){if(pcConfig&&pcConfig.iceServers){var newIceServers=[];for(var i=0;i<pcConfig.iceServers.length;i++){var server=pcConfig.iceServers[i];if(!server.hasOwnProperty("urls")&&server.hasOwnProperty("url")){utils.deprecated("RTCIceServer.url","RTCIceServer.urls");server=JSON.parse(JSON.stringify(server));server.urls=server.url;delete server.url;newIceServers.push(server)}else{newIceServers.push(pcConfig.iceServers[i])}}pcConfig.iceServers=newIceServers}return new OrigPeerConnection(pcConfig,pcConstraints)};window.RTCPeerConnection.prototype=OrigPeerConnection.prototype;if("generateCertificate"in window.RTCPeerConnection){Object.defineProperty(window.RTCPeerConnection,"generateCertificate",{get:function(){return OrigPeerConnection.generateCertificate}})}},shimTrackEventTransceiver:function(window){if(typeof window==="object"&&window.RTCPeerConnection&&"receiver"in window.RTCTrackEvent.prototype&&!window.RTCTransceiver){Object.defineProperty(window.RTCTrackEvent.prototype,"transceiver",{get:function(){return{receiver:this.receiver}}})}},shimCreateOfferLegacy:function(window){var origCreateOffer=window.RTCPeerConnection.prototype.createOffer;window.RTCPeerConnection.prototype.createOffer=function(offerOptions){var pc=this;if(offerOptions){if(typeof offerOptions.offerToReceiveAudio!=="undefined"){offerOptions.offerToReceiveAudio=!!offerOptions.offerToReceiveAudio}var audioTransceiver=pc.getTransceivers().find(function(transceiver){return transceiver.sender.track&&transceiver.sender.track.kind==="audio"});if(offerOptions.offerToReceiveAudio===false&&audioTransceiver){if(audioTransceiver.direction==="sendrecv"){if(audioTransceiver.setDirection){audioTransceiver.setDirection("sendonly")}else{audioTransceiver.direction="sendonly"}}else if(audioTransceiver.direction==="recvonly"){if(audioTransceiver.setDirection){audioTransceiver.setDirection("inactive")}else{audioTransceiver.direction="inactive"}}}else if(offerOptions.offerToReceiveAudio===true&&!audioTransceiver){pc.addTransceiver("audio")}if(typeof offerOptions.offerToReceiveVideo!=="undefined"){offerOptions.offerToReceiveVideo=!!offerOptions.offerToReceiveVideo}var videoTransceiver=pc.getTransceivers().find(function(transceiver){return transceiver.sender.track&&transceiver.sender.track.kind==="video"});if(offerOptions.offerToReceiveVideo===false&&videoTransceiver){if(videoTransceiver.direction==="sendrecv"){videoTransceiver.setDirection("sendonly")}else if(videoTransceiver.direction==="recvonly"){videoTransceiver.setDirection("inactive")}}else if(offerOptions.offerToReceiveVideo===true&&!videoTransceiver){pc.addTransceiver("video")}}return origCreateOffer.apply(pc,arguments)}}}},{"../utils":14}],14:[function(require,module,exports){"use strict";var logDisabled_=true;var deprecationWarnings_=true;function extractVersion(uastring,expr,pos){var match=uastring.match(expr);return match&&match.length>=pos&&parseInt(match[pos],10)}function wrapPeerConnectionEvent(window,eventNameToWrap,wrapper){if(!window.RTCPeerConnection){return}var proto=window.RTCPeerConnection.prototype;var nativeAddEventListener=proto.addEventListener;proto.addEventListener=function(nativeEventName,cb){if(nativeEventName!==eventNameToWrap){return nativeAddEventListener.apply(this,arguments)}var wrappedCallback=function(e){var modifiedEvent=wrapper(e);if(modifiedEvent){cb(modifiedEvent)}};this._eventMap=this._eventMap||{};this._eventMap[cb]=wrappedCallback;return nativeAddEventListener.apply(this,[nativeEventName,wrappedCallback])};var nativeRemoveEventListener=proto.removeEventListener;proto.removeEventListener=function(nativeEventName,cb){if(nativeEventName!==eventNameToWrap||!this._eventMap||!this._eventMap[cb]){return nativeRemoveEventListener.apply(this,arguments)}var unwrappedCb=this._eventMap[cb];delete this._eventMap[cb];return nativeRemoveEventListener.apply(this,[nativeEventName,unwrappedCb])};Object.defineProperty(proto,"on"+eventNameToWrap,{get:function(){return this["_on"+eventNameToWrap]},set:function(cb){if(this["_on"+eventNameToWrap]){this.removeEventListener(eventNameToWrap,this["_on"+eventNameToWrap]);delete this["_on"+eventNameToWrap]}if(cb){this.addEventListener(eventNameToWrap,this["_on"+eventNameToWrap]=cb)}},enumerable:true,configurable:true})}module.exports={extractVersion:extractVersion,wrapPeerConnectionEvent:wrapPeerConnectionEvent,disableLog:function(bool){if(typeof bool!=="boolean"){return new Error("Argument type: "+typeof bool+". Please use a boolean.")}logDisabled_=bool;return bool?"adapter.js logging disabled":"adapter.js logging enabled"},disableWarnings:function(bool){if(typeof bool!=="boolean"){return new Error("Argument type: "+typeof bool+". Please use a boolean.")}deprecationWarnings_=!bool;return"adapter.js deprecation warnings "+(bool?"disabled":"enabled")},log:function(){if(typeof window==="object"){if(logDisabled_){return}if(typeof console!=="undefined"&&typeof console.log==="function"){console.log.apply(console,arguments)}}},deprecated:function(oldMethod,newMethod){if(!deprecationWarnings_){return}console.warn(oldMethod+" is deprecated, please use "+newMethod+" instead.")},detectBrowser:function(window){var navigator=window&&window.navigator;var result={};result.browser=null;result.version=null;if(typeof window==="undefined"||!window.navigator){result.browser="Not a browser.";return result}if(navigator.mozGetUserMedia){result.browser="firefox";result.version=extractVersion(navigator.userAgent,/Firefox\/(\d+)\./,1)}else if(navigator.webkitGetUserMedia){result.browser="chrome";result.version=extractVersion(navigator.userAgent,/Chrom(e|ium)\/(\d+)\./,2)}else if(navigator.mediaDevices&&navigator.userAgent.match(/Edge\/(\d+).(\d+)$/)){result.browser="edge";result.version=extractVersion(navigator.userAgent,/Edge\/(\d+).(\d+)$/,2)}else if(window.RTCPeerConnection&&navigator.userAgent.match(/AppleWebKit\/(\d+)\./)){result.browser="safari";result.version=extractVersion(navigator.userAgent,/AppleWebKit\/(\d+)\./,1)}else{result.browser="Not a supported browser.";return result}return result}}},{}]},{},[3])(3)});function findKey(obj,value,compare=(a,b)=>a===b){return Object.keys(obj).find(k=>compare(obj[k],value))}function getKeyByValueFromMap(map,searchValue){let keys=[];for(let[key,value]of map.entries()){if(value===searchValue)keys.push(key)}return keys}Date.prototype.Format=function(fmt){var o={"M+":this.getMonth()+1,"d+":this.getDate(),"H+":this.getHours(),"m+":this.getMinutes(),"s+":this.getSeconds(),"q+":Math.floor((this.getMonth()+3)/3),S:this.getMilliseconds()};if(/(y+)/.test(fmt))fmt=fmt.replace(RegExp.$1,(this.getFullYear()+"").substr(4-RegExp.$1.length));for(var k in o)if(new RegExp("("+k+")").test(fmt))fmt=fmt.replace(RegExp.$1,RegExp.$1.length==1?o[k]:("00"+o[k]).substr((""+o[k]).length));return fmt};var language={LocalizedString:{appName_xj:localStorage.getItem("language")!=1?"小京云会议":"ຊຽວຈິງຢຸນກອງປະຊຸມ",company_name:localStorage.getItem("language")!=1?"天地阳光":"ຕາເວັນໃນທ້ອງຟ້າ",appName_cd:localStorage.getItem("language")!=1?"指挥调度客户端":"ຄຳສັ່ງແລະຄວບຄຸມລູກຄ້າ",login_title:localStorage.getItem("language")!=1?"登录":"ເຂົ້າ​ສູ່​ລະ​ບົບ",video_call:localStorage.getItem("language")!=1?"视频通话...":"ການໂທດ້ວຍວິດີໂອ",audio_call:localStorage.getItem("language")!=1?"语⾳通话…":"ການໂທດ້ວຍສຽງ",in_link:localStorage.getItem("language")!=1?"正在接通...":"ກຳລັງເຊື່ອມຕໍ່",call_busy:localStorage.getItem("language")!=1?"对⽅忙...":"ຝ່າຍອື່ນຄາວຽກ",gateway:localStorage.getItem("language")!=1?"⽹关":"ປະຕູເນັດ",server:localStorage.getItem("language")!=1?"服务器":"ເຊີບເວີ",lost_server_connect:localStorage.getItem("language")!=1?"与服务器断开连接":"ຕັດການເຊື່ອມຕໍ່ຈາກເຊີບເວີ",incomming_call_name:localStorage.getItem("language")!=1?"来电名称":"ຊື່ຜູ້ໂທ",recoding:localStorage.getItem("language")!=1?"正在录播":"ກຳລັງບັນທຶກ",show_chinese:localStorage.getItem("language")!=1?"显示中文":"ສະແດງພາສາຈີນ",view_title:localStorage.getItem("language")!=1?"视图":"ເບິ່ງ",living:localStorage.getItem("language")!=1?"正在直播":"ກຳລັງຖ່າຍທອດສົດ",play_back:localStorage.getItem("language")!=1?"点播回放":"ການສະແດງຄວາມຕ້ອງການ",recording_live:localStorage.getItem("language")!=1?"直播+录播":"ຖ່າຍທອດສົດ + ບັນທຶກ",start_live:localStorage.getItem("language")!=1?"开始直播":"ເລີ່ມການຖ່າຍທອດສົດ",invite_joined:localStorage.getItem("language")!=1?"邀请加⼊":"ເຊີນເຂົ້າຮ່ວມ",room:localStorage.getItem("language")!=1?"房间":"ຫ້ອງ",invite_joinedroom:localStorage.getItem("language")!=1?"邀请加⼊房间":"ເຊີນເຂົ້າຮ່ວມຫ້ອງ",input_user_name:localStorage.getItem("language")!=1?"请输⼊⽤户名":"ກະລຸນາຕື່ມໃສ່ຊື່ຜູ້ໃຊ້",input_password:localStorage.getItem("language")!=1?"请输⼊密码":"ກະລຸນາຕື່ມໃສ່ລະຫັດຜ່ານ",input_server_adress:localStorage.getItem("language")!=1?"请输⼊服务器地址":"ກະລຸນາຕື່ມໃສ່ທີ່ຢູ່ຂອງເຊີບເວີ",input_gateway:localStorage.getItem("language")!=1?"请输⼊⽹关地址":"ກະລຸນາຕື່ມໃສ່ທີ່ຢູ່ຂອງປະຕູເນັດ",out_line:localStorage.getItem("language")!=1?"下线通知":"ການແຈ້ງເຕືອນແບບອອຟໄລ",login_other_device:localStorage.getItem("language")!=1?"您的账号在其他地⽅登录,请确认账号安全":"ບັນຊີຂອງທ່ານເຂົ້າສູ່ລະບົບຢູ່ບ່ອນອື່ນ, ກະລຸນາກວດສອບໃຫ້ແນ່ໃຈວ່າບັນຊີຂອງທ່ານຍັງມີຄວາມປອດໄພ",exite:localStorage.getItem("language")!=1?"退出":"ອອກໄປ",relogin:localStorage.getItem("language")!=1?"重新登录":"ເຊີນເຂົ້າສູ່ລະບົບໃຫມ່",no_hoster:localStorage.getItem("language")!=1?"您是主持⼈,离开房间,房间将被销毁,结束会议":"ທ່ານແມ່ນເຈົ້າພາບ, ຖ້າທ່ານອອກຈາກຫ້ອງ, ຫ້ອງຈະຖືກທຳລາຍແລະກອງປະຊຸມຈະຈົບລົງ",cancle:localStorage.getItem("language")!=1?"取消":"ຍົກເລີກ",confirm:localStorage.getItem("language")!=1?"确定":"ຕົກລົງ",remove_room:localStorage.getItem("language")!=1?"您被移出群聊":"ທ່ານຖືກຍ້າຍອອກຈາກການສົນທະນາເປັນກຸ່ມ",exite_room:localStorage.getItem("language")!=1?"退出会议房间":"ອອກຈາກຫ້ອງປະຊຸມ",server_down:localStorage.getItem("language")!=1?"与服务器已断开连接,请刷新重新登录或等待⾃动登录成功":"ຍົກເລີກການເຊື່ອມຕໍ່ຈາກເຊີບເວແລ້ວີ, ກະລຸນາເສບສົດແລະເຂົ້າສູ່ລະບົບອີກຄັ້ງຫລືລໍຖ້າການເຂົ້າສູ່ລະບົບແບບອັດຕະໂນມັດສຳເລັດ",no_camera_use:localStorage.getItem("language")!=1?"摄像头不可⽤,请确认摄像头连接状态":"ເວັບແຄມໃຊ້ບໍ່ໄດ້, ກະລຸນາເຮັດໃຫ້ແນ່ໃຈວ່າການເຊື່ອມຕໍ່ເວັບແຄມເປັນທີ່ຖືກຕ້ອງ",no_register_sip:localStorage.getItem("language")!=1?"未注册会议,不能呼叫会议⽤户号":"ຍັງບໍ່ທັນລົງທະບຽນກອງປະຊຸມ, ບໍ່ສາມາດໂທຫາຜູ້ເຂົ້າຮ່ວມກອງປະຊຸມ",sip_server_down:localStorage.getItem("language")!=1?"会议服务器连接出错,不能呼叫会议⽤户号":"ການເຊື່ອມຕໍ່ເຊີບເວີກອງປະຊຸມມີຄວາມຜິດ, ບໍ່ສາມາດໂທຫາຜູ້ເຂົ້າຮ່ວມກອງປະຊຸມ ",no_support_video_code:localStorage.getItem("language")!=1?"会议呼叫视频编码格式不⽀持VP9":"ຮູບແບບເຂົ້າລະຫັດຂອງວິດີໂອທີ່ກອງປະຊຸມສາມາດໂທຫາບໍ່ໄດ້ຮັບVP9",no_support_audio_code:localStorage.getItem("language")!=1?"会议呼叫⾳频编码格式不⽀持opus":"ຮູບແບບເຂົ້າລະຫັດຂອງສຽງທີ່ກອງປະຊຸມສາມາດໂທຫາບໍ່ໄດ້ຮັບopus",wating_accepted:localStorage.getItem("language")!=1?"等待接听...":"ລໍຖ້າຜູ້ຕອບ...",input_call_number:localStorage.getItem("language")!=1?"请输⼊呼叫号码":"ກະລຸນາຕື່ມໃສ່ເບີໂທ",input_meeting_number:localStorage.getItem("language")!=1?"请输⼊会议房间号":"ກະລຸນາຕື່ມໃສ່ຫມາຍເລກຂອງຫ້ອງປະຊຸມ",camera_micphone:localStorage.getItem("language")!=1?"摄像头和⻨克⻛必须都可使⽤才能开启会议":"ພຽງແຕ່ເມື່ອເວັບແຄມແລະໄມໂຄຣໂຟນກໍສາມາດໃຊ້ໄດ້ຈຶ່ງສາມາດເລີ່ມກອງປະຊຸມ",no_micphone:localStorage.getItem("language")!=1?"⻨克⻛不可⽤":"ໄມໂຄຣໂຟນໃຊ້ບໍ່ໄດ້",no_camera:localStorage.getItem("language")!=1?"摄像头不可⽤":"ເວັບແຄມໃຊ້ບໍ່ໄດ້",must_be_numeric:localStorage.getItem("language")!=1?"会议号必须是数字":"ຫມາຍເລກຂອງກອງປະຊຸມຕ້ອງເປັນຕົວເລກ",room_destory_exit:localStorage.getItem("language")!=1?"房间已被销毁,⾃动退出房间":"ຫ້ອງໄດ້ຖືກທຳລາຍ, ຈະອອກໄປເປັນອັດຕະໂນມັດ",local_user:localStorage.getItem("language")!=1?"此⽤户,为本地⽤户":"ຜູ້ໃຊ້ນີ້, ເປັນຜູ້ໃຊ້ທ້ອງຖິ່ນ",reminder:localStorage.getItem("language")!=1?"提示":"ການກະຕຸ້ນ",confirm_kicked:localStorage.getItem("language")!=1?"确认将踢出参会者":"ແນ່ໃຈວ່າຈະໃຫ້ຜູ້ເຂົ້າຮ່ວມອອກໄປ",enter_live_room_id:localStorage.getItem("language")!=1?"请输⼊直播房间的ID号码":"ກະລຸນາຕື່ມໃສ່ເບີIdຂອງຫ້ອງຖ່າຍທອດສົດ",already_in_the_room:localStorage.getItem("language")!=1?"当前⽤户已在房间内":"ຜູ້ໃຊ້ນີ້ເຂົ້າໃນຫ້ອງແລ້ວ",cannot_invite_yourself:localStorage.getItem("language")!=1?"不能邀请⾃⼰":"ບໍ່ສາມາດເຊີນຕົນເອງ",repeat_input:localStorage.getItem("language")!=1?"重复输⼊":"ເຮັດຊ້ຳການຕື່ມໃສ່",invite_send_success:localStorage.getItem("language")!=1?"邀请发送成功":"ການເຊື້ອເຊີນໄດ້ຖືກສົ່ງໄປຢ່າງສຳເລັດຜົນ",add_invite_number:localStorage.getItem("language")!=1?"请添加邀请⼈号码":"ກະລຸນາຕື່ມເບີຂອງຜູ້ເຊີນ",input_server_adress:localStorage.getItem("language")!=1?"请输⼊服务器地址":"ກະລຸນາຕື່ມໃສ່ທີ່ຢູ່ຂອງເວັບໄຊທ໌",input_gateway_adress:localStorage.getItem("language")!=1?"请输⼊⽹关地址":"ກະລຸນາຕື່ມໃສ່ທີ່ຢູ່ຂອງປະຕູເນັດ",input_userName:localStorage.getItem("language")!=1?"请输⼊⽤户名":"ກະລຸນາຕື່ມໃສ່ຊື່ຂອງຜູ້ໃຊ້",input_password:localStorage.getItem("language")!=1?"请输⼊密码":"ກະລຸນາຕື່ມໃສ່ລະຫັດຜ່ານ",legal_user_name:localStorage.getItem("language")!=1?"输⼊⽤户名不合法":"ຊື່ຜູ້ໃຊ້ທີ່ຕື່ມໃສ່ນີ້ຜິດກົດຫມາຍ",meeting_server:localStorage.getItem("language")!=1?"会议服务器":"ເວັບໄຊທ໌ກອງປະຊຸມ",gateway:localStorage.getItem("language")!=1?"⽹关":"ປະຕູເນັດ",protocol_type:localStorage.getItem("language")!=1?"传输协议类型":"ປະເພດຂໍ້ຕົກລົງ",start_conference_server:localStorage.getItem("language")!=1?"开启会议服务器":"ເປີດເວັບໄຊທ໌ກອງປະຊຸມ",system_setting:localStorage.getItem("language")!=1?"系统设置":"ການຕັ້ງຄ່າລະບົບ",video_setting:localStorage.getItem("language")!=1?"视频设置":"ການຕັ້ງຄ່າວິດີໂອ",video_resolution:localStorage.getItem("language")!=1?"视频分辨率":"ອັດຕາລະບຸຄວາມແຕກຕ່າງກັນຂອງວິດີໂອ",frame_rate:localStorage.getItem("language")!=1?"视频帧率":"ອັດຕາເຟຣມຂອງວິດີໂອ",bandwidth:localStorage.getItem("language")!=1?"带宽(kb)":"ແບນວິດ(kb)",conference_number:localStorage.getItem("language")!=1?"会议视频⼈数":"ຈຳນວນຄົນໃນວິດີໂອຂອງກອງປະຊຸມ",video_ice_protol:localStorage.getItem("language")!=1?"默认传输协议":"ໂປໂຕຄອນການສົ່ງຕໍ່ແບບເລີ່ມຕົ້ນ",video_audio_codeType:localStorage.getItem("language")!=1?"默认⾳视频编码格式":"ຮູບແບບຖອດລະຫັດຂອງສຽງແລະວິດີໂອເລີ່ມຕົ້ນ",video_codeType:localStorage.getItem("language")!=1?"视频编解码格式":"ຮູບແບບເຂົ້າແລະຖອດລະຫັດຂອງວິດີໂອ",audio_codeType:localStorage.getItem("language")!=1?"⾳频编解码格式":"ຮູບແບບເຂົ້າແລະຖອດລະຫັດຂອງສຽງ",select_one_code_Type:localStorage.getItem("language")!=1?"最少选择一种编码格式":"ເລືອກຮູບແບບການເຂົ້າລະຫັດຢ່າງໜ້ອຍໜຶ່ງຮູບແບບ",audio_priority:localStorage.getItem("language")!=1?"⾳频优先":"ສຽງເປັນບໍລິມະສິດ",room_type:localStorage.getItem("language")!=1?"房间类型":"ປະເພດຂອງຫ້ອງ",create_room_number:localStorage.getItem("language")!=1?"创建房间数":"ຈຳນວນຫ້ອງທີ່ສ້າງຂຶ້ນ",input_create_room_number:localStorage.getItem("language")!=1?"请输⼊创建房间数":"ກະລຸນາຕື່ມໃສ່ຈຳນວນຫ້ອງທີ່ສ້າງຂຶ້ນ",start_number:localStorage.getItem("language")!=1?"房间号开始数":"ເລກເລີ່ມຕົ້ນຂອງຫມາຍເລກຫ້ອງ",input_start_number:localStorage.getItem("language")!=1?"请输⼊房间号开始数":"ກະລຸນາຕື່ມໃສ່ເລກເລີ່ມຕົ້ນຂອງຫມາຍເລກຫ້ອງ",room_user_number:localStorage.getItem("language")!=1?"单个房间⽤户数":"ຈຳນວນຜູ້ໃຊ້ຂອງຫ້ອງດຽວ",input_user_number:localStorage.getItem("language")!=1?"请输⼊⽤户数":"ກະລຸນາຕື່ມໃສ່ຈຳນວນຜູ້ໃຊ້",use_default_users:localStorage.getItem("language")!=1?"使⽤默认⽤户":"ໃຊ້ຜູ້ໃຊ້ເລີ່ມຕົ້ນ",add_default_users:localStorage.getItem("language")!=1?"添加默认⽤户数":"ຕື່ມຈຳນວນຜູ້ໃຊ້ເລີ່ມຕົ້ນ",please_add:localStorage.getItem("language")!=1?"请添加":"ກະລຸນາຕື່ມ",export_user_account:localStorage.getItem("language")!=1?"导⼊⽤户账号":"ນຳເຂົ້າບັນຊີຂອງຜູ້ໃຊ້",logout:localStorage.getItem("language")!=1?"注销":"ຍົກເລີກບັນຊີ",user_account:localStorage.getItem("language")!=1?"⽤户名":"ຊື່ຜູ້ໃຊ້",live_room:localStorage.getItem("language")!=1?"直播房间":"ຫ້ອງຖ່າຍທອດສົດ",record_room:localStorage.getItem("language")!=1?"录播房间":"ຫ້ອງບັນທຶກ",start_timer:localStorage.getItem("language")!=1?"请选择开始时间":"ກະລຸນາເລືອກເວລາເລີ່ມຕົ້ນ",finish_timer:localStorage.getItem("language")!=1?"请选择结束时间":"ກະລຸນາເລືອກເວລາສິ້ນສຸດ",input_key_number:localStorage.getItem("language")!=1?"请输⼊关键字":"ກະລຸນາຕື່ມໃສ່ຄຳສຳຄັນ",search:localStorage.getItem("language")!=1?"搜索":"ຄົ້ນຫາ",no_data:localStorage.getItem("language")!=1?"暂⽆数据":"ບໍ່ມີຂໍ້ມູນໃນຊົ່ວຄາວ",enter_called_number:localStorage.getItem("language")!=1?"输⼊被叫号码":"ຕື່ມໃສ່ເບີທີ່ຖືກໂທຫາ",called_number:localStorage.getItem("language")!=1?"被叫号码":"ເບີທີ່ຖືກໂທຫາ",login_out:localStorage.getItem("language")!=1?"退出登录":"ອອກຈາກລະບົບ",cannot_remove:localStorage.getItem("language")!=1?"您不是主持⼈,不能移除房间内⽤户":"ທ່ານບໍ່ແມ່ນເຈົ້າພາບ, ບໍ່ສາມາດເອົາຜູ້ໃຊ້ໃນຫ້ອງອອກ",network_connecting:localStorage.getItem("language")!=1?"⽹络已连接":"ເຄືອຂ່າຍມີການເຊື່ອມຕໍ່ແລ້ວ",lost_video_source:localStorage.getItem("language")!=1?"检测到丢失视频源,会话结束":"ກວດພົບແຫລ່ງວິດີໂອທີ່ເສຍຫາຍ, ການສົນທະນາສິ້ນສຸດລົງ",network_disconnect:localStorage.getItem("language")!=1?"⽹络连接断开":"ການເຊື່ອມຕໍ່ເຄືອຂ່າຍຕັດຂາດ",network_not_working_right:localStorage.getItem("language")!=1?"当前通话您的⽹络不佳":"ການໂທປະຈຸບັນ ເຄືອຂ່າຍຂອງທ່ານບໍ່ດີ",call_network_not_working_right:localStorage.getItem("language")!=1?"当前通话对⽅⽹络不佳":"ການໂທປະຈຸບັນ ເຄືອຂ່າຍຂອງຝ່າຍອີກບໍ່ດີ",video_close_cannot_share:localStorage.getItem("language")!=1?"视频已关闭,不能打开屏幕共享":"ວິດີໂອໄດ້ປິດແລ້ວ, ບໍ່ສາມາດເປີດການແບ່ງປັນຫນ້າຈໍ",micphone_off:localStorage.getItem("language")!=1?"⻨克⻛关闭":"ປິດໄມໂຄຣໂຟນ",micphone_on:localStorage.getItem("language")!=1?"⻨克⻛打开":"ເປີດໄມໂຄຣໂຟນ",video_on:localStorage.getItem("language")!=1?"视频打开":"ເປີດວິດີໂອ",video_off:localStorage.getItem("language")!=1?"视频关闭":"ປິດວິດີໂອ",loudspeaker_off:localStorage.getItem("language")!=1?"扬声器关闭":"ປິດລຳໂຟງ",loudspeaker_on:localStorage.getItem("language")!=1?"扬声器打开":"ເປີດລຳໂຟງ",no_support_live_record:localStorage.getItem("language")!=1?"会议呼叫暂不⽀持直播与录播功能":"ການໂທຂອງກອງປະຊຸມຍັງບໍ່ສະຫນັບສະຫນູນການຖ່າຍທອດສົດແລະການບັນທຶກ",live_and_record:localStorage.getItem("language")!=1?"直播,录播,直播&录播,取消":"ການຖ່າຍທອດສົດ ການບັນທຶກ ການຖ່າຍທອດສົດແລະການບັນທຶກ ຍົກເລີກ",only_live:localStorage.getItem("language")!=1?"直播":"ຖ່າຍທອດສົດ",only_record:localStorage.getItem("language")!=1?"录播":"ບັນທຶກ",live_record:localStorage.getItem("language")!=1?"直播&录播":"ສົດແລະບັນທຶກ",finish:localStorage.getItem("language")!=1?"结束":"ສິ້ນສຸດລົງ",attendees:localStorage.getItem("language")!=1?"参会者":"ຜູ້ເຂົ້າຮ່ວມກອງປະຊຸມ",attendees_cannot_open_video:localStorage.getItem("language")!=1?"参会者不能打开摄像头":"ຜູ້ເຂົ້າຮ່ວມກອງປະຊຸມບໍ່ສາມາດເປີດເວັບແຄມ",maximum_number:localStorage.getItem("language")!=1?"已达到最⼤可开启视频数":"ໄດ້ຮອດຈຳນວນສູງສຸດທີ່ສາມາດເປີດວິດີໂອໄດ້",spokesman:localStorage.getItem("language")!=1?"发⾔⼈":"ຜູ້ເວົ້າ",attend:localStorage.getItem("language")!=1?"参会 ":"ເຂົ້າຮ່ວມ ",people:localStorage.getItem("language")!=1?" 人":" ຄົນ",leave_room:localStorage.getItem("language")!=1?"离开房间":"ອອກຈາກຫ້ອງ",enter_liveroom_number:localStorage.getItem("language")!=1?"请输⼊直播房间号":"ກະລຸນາຕື່ມໃສ່ເບີຂອງຫ້ອງຖ່າຍທອດສົດ",finish_live:localStorage.getItem("language")!=1?"直播结束":"ການຖ່າຍທອດສົດສິ້ນສຸດລົງແລ້ວ",password_incorrect_reenter:localStorage.getItem("language")!=1?"密码错误请重新输⼊":"ລະຫັດຜ່ານບໍ່ຖືກ ເຊີນຕື່ມໃສ່ໃຫມ່",recording_not_ready:localStorage.getItem("language")!=1?"录制未就绪,稍后在试":"ການບັນທຶກຍັງບໍ່ທັນກຽມພ້ອມ, ເຊີນລອງອີກເທື່ອໃນຊົ່ວຄາວ",enter_max_open_video:localStorage.getItem("language")!=1?"请输⼊最⼤可开启视频数":"ກະລຸນາຕື່ມໃສ່ຈຳນວນສູງສຸດທີ່ສາມາດເປີດວິດີໂອໄດ້",create:localStorage.getItem("language")!=1?"创建":"ສ້າງ",allows_max_spoker:localStorage.getItem("language")!=1?"允许创建最多9个发⾔者房间":"ອະນຸຍາດສ້າງຫ້ອງທີ່ສາມາດໃຫ້ຜູ້ເຂົ້າຮ່ວມບໍ່ເກີນ 9 ຄົນເຂົ້າຮ່ວມ",mic_not_allow:localStorage.getItem("language")!=1?"⻨克⻛不可⽤,确认连接后在尝试呼叫":"ໄມໂຄຣໂຟນໃຊ້ບໍ່ໄດ້, ເຊີນແນ່ໃຈວ່າການເຊື່ອມຕໍ່ບໍ່ມີບັນຫາແລ້ວຈຶ່ງລອງໂທອີກເທື່ອ",meeting_not_allow_live_record:localStorage.getItem("language")!=1?"会议呼叫暂不⽀持直播与录播功能":"ການໂທຂອງກອງປະຊຸມຍັງບໍ່ສະຫນັບສະຫນູນການຖ່າຍທອດສົດແລະການບັນທຶກ",setting:localStorage.getItem("language")!=1?"设置":"ການຕັ້ງຄ່າ",save_success:localStorage.getItem("language")!=1?"保存成功":"ການປະຢັດໄດ້ສຳເລັດ",save_title:localStorage.getItem("language")!=1?"保存":"ປະຢັດ",log_out:localStorage.getItem("language")!=1?"注销登录":"ອອກຈາກລະບົບ",semple_setting:localStorage.getItem("language")!=1?"简单设置":"ການຕັ້ງຄ່າທີ່ງ່າຍດາຍ",hight_setting:localStorage.getItem("language")!=1?"⾼级设置":"ການຕັ້ງຄ່າຂັ້ງສູງ",search_finish_start:localStorage.getItem("language")!=1?"搜索结束时间不能⼩于开始时间":"ເວລາສິ້ນສຸດການຄົ້ນຫາບໍ່ສາມາດຫນ້ອຍກວ່າເວລາເລີ່ມຕົ້ນ",search_data_success:localStorage.getItem("language")!=1?"查询数据成功":"ການຄົ້ນຫາຂໍ້ມູນໄດ້ສຳເລັດຜົນ",search_no_data:localStorage.getItem("language")!=1?"未查询到数据":"ບໍ່ໄດ້ຄົ້ນພົບຂໍ້ມູນ",calling_wating_retry:localStorage.getItem("language")!=1?"对⽅正在通话,稍后再试":"ຝ່າຍອື່ນກຳລັງໃນການໂທ, ກະລຸນາລອງອີກເທື່ອໃນຊົ່ວຄາວ",cannot_call_yourself:localStorage.getItem("language")!=1?"您不能呼叫⾃⼰":"ທ່ານບໍ່ສາມາດໂທຫາຕົນເອງ",choose_call_type:localStorage.getItem("language")!=1?"选择呼叫模式":"ເລືອກຮູບແບບການໂທ",audio_call:localStorage.getItem("language")!=1?"语⾳呼叫":"ໂທດ້ວຍສຽງ",vidoe_call:localStorage.getItem("language")!=1?"视频呼叫":"ໂທດ້ວຍວິດີໂອ",large_than:localStorage.getItem("language")!=1?"⼤于":"ໃຫຍ່ກວ່າ",small_than:localStorage.getItem("language")!=1?"小于":"ຫນ້ອຍກວ່າ",when_close_video:localStorage.getItem("language")!=1?"ms时，关闭视频":"ໃນ ມິນລິວິນາທີ, ປິດວິດີໂອ",reduce_resolution_frame_rate:localStorage.getItem("language")!=1?"ms时，降低分辨率、帧率":"ໃນ ມິນລິວິນາທີ ຫລຸດຜ່ອນອັດຕາລະບຸຄວາມແຕກຕ່າງກັນແລະອັດຕາເຟຣມ",network_test:localStorage.getItem("language")!=1?"⽹络检测":"ກວດພົບເຄືອຂ່າຍ",decrease_video_quality:localStorage.getItem("language")!=1?"次，降低视频质量":"ຄັ້ງ ຫລຸດຜ່ອນຄຸນນະພາບຂອງວິດີໂອ",Open_the_video:localStorage.getItem("language")!=1?"ms时，开启视频":"ໃນ ມິນລິວິນາທີ, ເປີດວິດີໂອ",Improve_resolution_frame_rate:localStorage.getItem("language")!=1?"ms时，提⾼分辨率、帧率":"ໃນ ມິນລິວິນາທີ ປັບປຸງອັດຕາລະບຸຄວາມແຕກຕ່າງກັນແລະອັດຕາເຟຣມ",improve_video_quality:localStorage.getItem("language")!=1?"次，提⾼视频质量":"ຄັ້ງ ປັບປຸງຄຸນນະພາບຂອງວິດີໂອ",ice_server:localStorage.getItem("language")!=1?"ICE服务器":"ເຊີບເວີ ICE",TURN_account:localStorage.getItem("language")!=1?"TURN⽤户名":"ຊື່ຜູ້ໃຊ້ TURN",TURN_password:localStorage.getItem("language")!=1?"TURN密码":"ລະຫັດຜ່ານ TURN",TURN_transport:localStorage.getItem("language")!=1?"TURN传输⽅式":"ທ່າທີການໂອນ TURN ",auto_accepted:localStorage.getItem("language")!=1?"⾃动接听":"ຕອບໂດຍອັດຕະໂນມັດ",default_screen:localStorage.getItem("language")!=1?"默认全屏":"ຫນ້າຈໍເຕັມໂດຍມາດຕະຖານ",soft_keyboard:localStorage.getItem("language")!=1?"软键盘":"ກະດານອ່ອນ",start_auto_record:localStorage.getItem("language")!=1?"开启⾃动录制":"ເປີດການບັນທຶກອັດຕະໂນມັດ",show_local_preview:localStorage.getItem("language")!=1?"显示本地图像":"ສະແດງຮູບພາບທ້ອງຖິ່ນ",standard:localStorage.getItem("language")!=1?"标准":"ມາດຕະຖານ",amplification:localStorage.getItem("language")!=1?"放⼤":"ຂະຫຍາຍ",test_parameters:localStorage.getItem("language")!=1?"测试参数":"ທົດສອບພາລາມິເຕີ",start_test:localStorage.getItem("language")!=1?"开启测试":"ເລີ່ມຕົ້ນການທົດສອບ",shut_down:localStorage.getItem("language")!=1?"关闭":"ປິດ",open:localStorage.getItem("language")!=1?"开启":"ເປີດ",server_address:localStorage.getItem("language")!=1?"服务器地址":"ທີ່ຢູ່ຂອງເຊີບເວີ",gateway_address:localStorage.getItem("language")!=1?"⽹关地址":"ທີ່ຢູ່ຂອງປະຕູເນັດ",audio_call:localStorage.getItem("language")!=1?"⾳频通话":"ການໂທດ້ວຍສຽງ",micPhone:localStorage.getItem("language")!=1?"⻨克⻛":"ໄມໂຄຣໂຟນ",speaker:localStorage.getItem("language")!=1?"扬声器":"ລຳໂພງ",hang_up:localStorage.getItem("language")!=1?"挂断":"ວາງສາຍ",Open_a_live:localStorage.getItem("language")!=1?"开启直播":"ເລີ່ມການຖ່າຍທອດສົດ",receive:localStorage.getItem("language")!=1?"接收":"ຮັບ",send:localStorage.getItem("language")!=1?"发送":"ສົ່ງ",audio:localStorage.getItem("language")!=1?"⾳频":"ສຽງ",video:localStorage.getItem("language")!=1?"视频":"ວິດີໂອ",resolution:localStorage.getItem("language")!=1?"分辨率":"ອັດຕາລະບຸຄວາມແຕກຕ່າງກັນ",share_screen:localStorage.getItem("language")!=1?"屏幕共享":"ການແບ່ງປັນຫນ້າຈໍ",on_line_user:localStorage.getItem("language")!=1?"在线⽤户":"ຜູ້ໃຊ້ອອນໄລ",invite_number_type:localStorage.getItem("language")!=1?"邀请号(sip号格式:sip:110@127.0.0.1) 并按enter键继续":"ເບີເຊີນ （ຮູບແບບຂອງເບີsip:sip:110@127.0.0.1） ແລະກົດ ເພື່ອດຳເນີນການຕໍ່ໄປ",invite:localStorage.getItem("language")!=1?"邀请":"ເຊີນ",list_of_attendees:localStorage.getItem("language")!=1?"参会⼈列表":"ບັນຊີລາຍຊື່ຜູ້ເຂົ້າຮ່ວມກອງປະຊຸມ",invite_joined_room:localStorage.getItem("language")!=1?"邀请⼊会":"ເຊີນເຂົ້າຮ່ວມກອງປະຊຸມ",answer:localStorage.getItem("language")!=1?"接听":"ຕອບ",safari_support:localStorage.getItem("language")!=1?"H265编解码仅在Safari Technology Preview 版本上支持":"ລະຫັດ code H265 ຖືກຮອງຮັບໃນລຸ້ນ Safari Technology Preview ເທົ່ານັ້ນ",camera_call_button:localStorage.getItem("language")!=1?"呼叫":"ໂທ",call_camera_stop_back:localStorage.getItem("language")!=1?"结束返回":"ສິ້ນສຸດການກັບຄືນ",call_camera_no_user:localStorage.getItem("language")!=1?"还没有用户":"ບໍ່ມີຜູ້ໃຊ້ເທື່ອ",call_camera_exist:localStorage.getItem("language")!=1?"呼叫用户已存在":"ມີຜູ້ໃຊ້ການໂທຢູ່ແລ້ວ",call_camera_hold:localStorage.getItem("language")!=1?"请输入呼叫号":"ກະລຸນາໃສ່ເບີໂທ",new_work_error:localStorage.getItem("language")!=1?"网络连接出错":"ການເຊື່ອມຕໍ່ເຄືອຂ່າຍຜິດພາດ"}};var venus_manager=function(){var server;var port;var domain;var venusUrl;var name;var password;var exten;var audioId;var localVideoId;var remoteVideoId;var venus;var sipcall;var echoTestHandle;var incoming;var currentJsep;var localStream;var remoteStreamAudio;var remoteStreamVideo;var counterpartNum;var started=false;var registered=false;var sipRegister=false;var callTypeIsLive=false;var call_number=null;var GateWay;var supportedDevices={};var ringing=new Audio;var calling=new Audio;calling.loop=true;var issipCall;var VideoCall;var audioEnable=false;var videoEnable=false;var commingName;var incomingType;var timeInter;var callErrorresult;var callState;let callStatefuntionback;var callStateBack;let RoomisMutAudio;var VideoLocalStream;var VideoRemoteStream;var isNormalVideo=false;let isScreen=false;var Roomvenus;var videoRoomCall;var myusername=null;var myid=null;var creater_id=null;var mystream=null;var opaqueId="videoroomtest-"+Math.random().toString;var isInRoom=false;var roomParticipants={};var mypvtid=null;var feeds=[];var remoteList={};var RoomhandleRemote={};var remoteNameList={};var VideoRoomStream;var RoomNumber;var RoomLeav=false;var maxPublisher=0;var ownUerMuteVideo;var invite_media_type=2;var roomDomIds=[];var roomDomIdSets=new Set;var roomDomFree=[];var Streaming;var StreamVenus;var remoteWatchVideo;let role=null;var PlayVenus=null;let currentvenus=null;var PlayCall=null;var recordingId=null;let Streamtype=null;var selectedRecording=null;var showLocalvideo=false;var liverectype="";var isVideoCalling=false;var isSipCalling=false;var bandwidth=1024*1024;var hasCamer=false;var hasMic=true;var bell=true;var userInCall="";var _factory_id="";var _call_reqinfo=null;var getSupportedDevices=function(origCallback){if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices){navigator.enumerateDevices=function(callback){navigator.mediaDevices.enumerateDevices().then(callback)}}var MediaDevices=[];var isHTTPs=location.protocol==="https:";var canEnumerate=false;if(typeof MediaStreamTrack!=="undefined"&&"getSources"in MediaStreamTrack){canEnumerate=true}else if(navigator.mediaDevices&&!!navigator.mediaDevices.enumerateDevices){canEnumerate=true}var hasMicrophone=false;var hasSpeakers=false;var hasWebcam=false;var isMicrophoneAlreadyCaptured=false;var isWebcamAlreadyCaptured=false;function checkDeviceSupport(callback){if(!canEnumerate){return}if(!navigator.enumerateDevices&&window.MediaStreamTrack&&window.MediaStreamTrack.getSources){navigator.enumerateDevices=window.MediaStreamTrack.getSources.bind(window.MediaStreamTrack)}if(!navigator.enumerateDevices&&navigator.enumerateDevices){navigator.enumerateDevices=navigator.enumerateDevices.bind(navigator)}if(!navigator.enumerateDevices){if(callback){callback()}return}MediaDevices=[];navigator.enumerateDevices(function(devices){devices.forEach(function(_device){var device={};for(var d in _device){device[d]=_device[d]}if(device.kind==="audio"){device.kind="audioinput"}if(device.kind==="video"){device.kind="videoinput"}var skip;MediaDevices.forEach(function(d){if(d.id===device.id&&d.kind===device.kind){skip=true}});if(skip){return}if(!device.deviceId){device.deviceId=device.id}if(!device.id){device.id=device.deviceId}if(!device.label){device.label="Please invoke getUserMedia once.";if(!isHTTPs){device.label="HTTPs is required to get label of this "+device.kind+" device."}}else{if(device.kind==="videoinput"&&!isWebcamAlreadyCaptured){isWebcamAlreadyCaptured=true}if(device.kind==="audioinput"&&!isMicrophoneAlreadyCaptured){isMicrophoneAlreadyCaptured=true}}if(device.kind==="audioinput"){hasMicrophone=true}if(device.kind==="audiooutput"){hasSpeakers=true}if(device.kind==="videoinput"){hasWebcam=true}MediaDevices.push(device)});if(callback){callback()}})}checkDeviceSupport(function(){supportedDevices={audio:hasMicrophone,audioCap:isMicrophoneAlreadyCaptured,video:hasWebcam,videoCap:isWebcamAlreadyCaptured};hasMic=supportedDevices["audio"];hasCamer=supportedDevices["video"];$(document).trigger("supportedDevices",supportedDevices);console.log("=======");origCallback()})};var ReloadDevice=function(origCallback){if(navigator.mediaDevices&&navigator.mediaDevices.enumerateDevices){navigator.enumerateDevices=function(callback){navigator.mediaDevices.enumerateDevices().then(callback)}}var MediaDevices=[];var isHTTPs=location.protocol==="https:";var canEnumerate=false;if(typeof MediaStreamTrack!=="undefined"&&"getSources"in MediaStreamTrack){canEnumerate=true}else if(navigator.mediaDevices&&!!navigator.mediaDevices.enumerateDevices){canEnumerate=true}var hasMicrophone=false;var hasSpeakers=false;var hasWebcam=false;var isMicrophoneAlreadyCaptured=false;var isWebcamAlreadyCaptured=false;function checkDeviceSupport(callback){if(!canEnumerate){return}if(!navigator.enumerateDevices&&window.MediaStreamTrack&&window.MediaStreamTrack.getSources){navigator.enumerateDevices=window.MediaStreamTrack.getSources.bind(window.MediaStreamTrack)}if(!navigator.enumerateDevices&&navigator.enumerateDevices){navigator.enumerateDevices=navigator.enumerateDevices.bind(navigator)}if(!navigator.enumerateDevices){if(callback){callback()}return}MediaDevices=[];navigator.enumerateDevices(function(devices){devices.forEach(function(_device){var device={};for(var d in _device){device[d]=_device[d]}if(device.kind==="audio"){device.kind="audioinput"}if(device.kind==="video"){device.kind="videoinput"}var skip;MediaDevices.forEach(function(d){if(d.id===device.id&&d.kind===device.kind){skip=true}});if(skip){return}if(!device.deviceId){device.deviceId=device.id}if(!device.id){device.id=device.deviceId}if(!device.label){device.label="Please invoke getUserMedia once.";if(!isHTTPs){device.label="HTTPs is required to get label of this "+device.kind+" device."}}else{if(device.kind==="videoinput"&&!isWebcamAlreadyCaptured){isWebcamAlreadyCaptured=true}if(device.kind==="audioinput"&&!isMicrophoneAlreadyCaptured){isMicrophoneAlreadyCaptured=true}}if(device.kind==="audioinput"){hasMicrophone=true}if(device.kind==="audiooutput"){hasSpeakers=true}if(device.kind==="videoinput"){hasWebcam=true}MediaDevices.push(device)});if(callback){callback()}})}checkDeviceSupport(function(){supportedDevices={audio:hasMicrophone,audioCap:isMicrophoneAlreadyCaptured,video:hasWebcam,videoCap:isWebcamAlreadyCaptured};hasMic=supportedDevices["audio"];hasCamer=supportedDevices["video"];share_manager.setSupportAudio(hasMicrophone);share_manager.setSupportVideo(hasWebcam);$(document).trigger("supportedDevices",supportedDevices)})};var call_reqinfo=function(factory_id,call_reqinfo){_factory_id=factory_id;_call_reqinfo=call_reqinfo};function initAndLogin(data,back,errorback,callStateMessage,backJsonMsg,jspMsg,messageCallback){if(!CheckUrl(data.name)){return}console.log("initAndLogin"+JSON.stringify(data));share_manager.setCallParam("callStateMessageBack",callStateMessage);venusUrl=data.GateWay;var str=venusUrl;var mid=str.match(/:(\S*)/)[1];if(mid.indexOf(":")>=0){str=str.match(/:(\S*):/)[1];str=str.substr(2,str.length)}else{if(mid.indexOf("/")>=0){mid=mid.substr(2,str.length);mid=mid.substr(0,mid.indexOf("/"))}else{mid=mid.substr(2,str.length)}str=mid}domain=str;var serverIP=data.server;if(serverIP.indexOf(":")<0){errorback("会议服务器输入地址输入有误,规定格式为127.0.0.1:5060");return}var serverPort=serverIP;serverIP=serverIP.match(/(\S*):/)[1];serverPort=serverPort.match(/:(\S*)/)[1];if(serverPort.length==0){errorback("会议服务器未输入端口号");return}port=serverPort;server=serverIP;name=data.name;exten=data.exten;GateWay=data.GateWay;password=data.password;audioId=data.audioId;localVideoId=data.localVideoId;remoteVideoId=data.remoteVideoId;localStream=localVideoId;remoteStreamAudio=audioId;remoteStreamVideo=remoteVideoId;registered=false;sipRegister=false;registerVideoCall(function(success){console.log(success);return back(success)},function(error){console.log(error);return errorback(error)},function(callStateMs){console.log(callStateMs);return callStateMessage(callStateMs)},function(jsonMsg){console.log(jsonMsg);return backJsonMsg(jsonMsg)},function(jsp){console.log(jsp);return jspMsg(jsp)},function(data,type){return messageCallback(data,type)})}function CheckUrl(name){if(name.indexOf("//")>=0||name.indexOf(":")>=0||name.indexOf("/")>=0||name.indexOf("\\")>=0){alert("输入用户名不合法");return 0}else{return 1}}function registSipCall(registSipBack,errorBack,CallStateMessage,backJsonMsg,jspMsg,messageCallback){venus.attach({plugin:"venus.plugin.sip",opaqueId:opaqueId,success:function(pluginHandle){sipcall=pluginHandle;Venus.log("Plugin attached! ("+sipcall.getPlugin()+", id="+sipcall.getId()+")");login()},error:function(error){sipRegister=false;if(bell){calling.pause();ringing.pause()}$(document).trigger("sipNetError");Venus.error("  -- Error attaching plugin...",error)},mediaState:function(medium,on){Venus.log("Venus "+(on?"started":"stopped")+" receiving our "+medium);if(medium=="video"&&on==false){}},onmessage:function(msg,jsep){console.log(msg);if(msg!=null&&msg!=undefined){let resultRecord=msg["videocall"];if(resultRecord==="event"){msg["type"]=liverectype;backJsonMsg(msg)}else{backJsonMsg(msg)}}if(jsep!=null&&jsep!=undefined){jspMsg(jsep)}Venus.debug(" ::: Got a message :::");Venus.debug(JSON.stringify(msg));var error=msg["error"];if(error!=null&&error!=undefined){if(bell){calling.pause();ringing.pause()}callErrorresult=error;errorBack(error);if(!sipRegister){Venus.log("User is not registered")}else{sipcall.hangup()}if(callStatefuntionback){return callStatefuntionback(0)}}var result=msg["result"];if(result!==null&&result!==undefined&&result["event"]!==undefined&&result["event"]!==null){var event=result["event"];switch(event){case"registration_failed":$(document).trigger("sipNetError");share_manager.setSipRegistStatus(false);registSipBack(0);errorBack("sip  "+result["reason"]);Venus.error("Registration failed: "+result["code"]+" "+result["reason"]);return;break;case"registered":share_manager.setSipRegistStatus(true);console.log("==******==");if(!sipRegister){Venus.log("Successfully registered as "+result["username"]+"!");registSipBack(1)}sipRegister=true;break;case"unregistered":errorBack("Successfully unregistered as "+result["username"]+"!");Venus.log("Successfully unregistered as "+result["username"]+"!");share_manager.setSipRegistStatus(false);if(sipRegister){sipRegister=false;$(document).trigger("unregistered");registSipBack(0)}break;case"calling":issipCall=true;Venus.log("Waiting for the peer to answer...");$(document).trigger("calling");CallStateMessage("calling");share_manager.setCallParam("status","ringing");if(callStatefuntionback){return callStatefuntionback(1)}break;case"incomingcall":if(isVideoCalling){var body={request:"decline",code:486};sipcall.send({message:body});share_manager.setCallParam("rejectTheCall",true);return}if(isSipCalling){var body={request:"decline"};sipcall.send({message:body});return}isSipCalling=true;issipCall=true;currentJsep=jsep;counterpartNum=msg.result.username.split("@")[0].split(":")[1];incoming=true;if(bell){ringing.play()}commingName=result["username"];incomingType=currentJsep.sdp.indexOf("m=video ")>-1?"video":"audio";userInCall=commingName;Venus.log("Incoming call from "+result["username"]+"!");$(document).trigger("incomingCall",counterpartNum);CallStateMessage("incomingCall");break;case"progress":Venus.log("There's early media from "+result["username"]+", wairing for the call!");if(jsep!==null&&jsep!==undefined){handleRemote(jsep)}break;case"accepted":share_manager.setCallParam("status","onCall");isSipCalling=true;issipCall=true;if(bell){calling.pause();ringing.pause()}Venus.log(result["username"]+" accepted the call!");var user_type=result["user_type"];if(user_type!=null&&user_type!=undefined){callTypeIsLive=true}if(!incoming){if(VideoLocalStream){var videoTracks=VideoLocalStream.getVideoTracks();if(videoTracks){if(videoTracks.length>0&&remoteStreamVideo&&callTypeIsLive){Venus.attachMediaStream($("#"+remoteStreamVideo).get(0),VideoLocalStream);let remoteVideo=document.getElementById(remoteStreamVideo);if(remoteVideo){remoteVideo.muted=true;$(document).trigger("is_living")}}}}}if(jsep!==null&&jsep!==undefined){console.log(jsep.sdp);jsep=change_ice_sdp(jsep);handleRemote(jsep)}$(document).trigger("callAccepted");CallStateMessage("accepted");break;case"hangup":issipCall=false;isSipCalling=false;isNormalVideo=false;incoming=false;if(bell){calling.pause();ringing.pause()}Venus.log("Call hung up ("+result["code"]+" "+result["reason"]+")!");if(sipcall){sipcall.hangup()}if(result["reason"]&&result["reason"].toLowerCase()==="busy here"){$(document).trigger("Busy");CallStateMessage({errCode:486,errMsg:result["reason"]})}if(!share_manager.getCallParam()["rejectTheCall"]){$(document).trigger("hangup",[result["code"],result["reason"]]);CallStateMessage("hangup")}share_manager.cleanCallParam();break;case"decline":share_manager.cleanCallParam();case"slowlink":console.log("=========");console.log(result);break;break;default:break}}else{if(msg["venus"]!=null&&msg["venus"]!=undefined&&msg["venus"]=="hangup"){isSipCalling=false;isNormalVideo=false;incoming=false;if(bell){calling.pause();ringing.pause()}Venus.log("Call hung up ("+"1"+" "+msg["reason"]+")!");if(sipcall){sipcall.hangup()}if(msg["reason"]&&msg["reason"].toLowerCase()==="busy here"){$(document).trigger("Busy");CallStateMessage({errCode:486,errMsg:msg["reason"]})}if(!share_manager.getCallParam()["rejectTheCall"]){$(document).trigger("hangup",["1",msg["reason"]]);CallStateMessage("hangup")}}}messageCallback(msg,"sip")},onlocalstream:function(stream){VideoLocalStream=stream;Venus.debug(" ::: Got a local stream :::");var videoTracks=stream.getVideoTracks();if(videoTracks){if(videoTracks.length>0){videoCallLocalStream(stream,true)}}},onremotestream:function(stream){console.log("sip call on remote stream");VideoRemoteStream=stream;var audioTracks=stream.getAudioTracks();var videoTracks=stream.getVideoTracks();if(videoTracks){if(videoTracks.length>0){videoCallRemoteStream(VideoRemoteStream,true)}else{Venus.attachMediaStream($("#"+remoteStreamAudio).get(0),new MediaStream(audioTracks))}}else{Venus.attachMediaStream($("#"+remoteStreamAudio).get(0),new MediaStream(audioTracks))}},oncleanup:function(){Venus.log(" ::: Got a cleanup notification :::")}})}function registEchoTest(){venus.attach({plugin:"venus.plugin.echotest",success:function(pluginHandle){echoTestHandle=pluginHandle;var body={audio:true,video:true,audiocodec:["opus","g722","pcma","pcmu"],videocodec:["vp8","vp9","h264"]};echoTestHandle.send({message:body});Venus.debug("Trying a createOffer too (audio/video sendrecv)");echoTestHandle.createOffer({media:{data:true},simulcast:true,simulcast2:true,success:function(jsep){Venus.debug("Got SDP!");Venus.debug(jsep);echoTestHandle.send({message:body,jsep:jsep})},error:function(error){Venus.error("WebRTC error:",error)}});iceRestart()},error:function(cause){},consentDialog:function(on){},onmessage:function(msg,jsep){},onlocalstream:function(stream){console.log("=====")},onremotestream:function(stream){console.log("=====")},oncleanup:function(){},detached:function(){}})}function iceRestart(){echoTestHandle.createOffer({iceRestart:true,media:{data:true,audioSend:false,audioRecv:false,videoSend:false,videoRecv:false},success:function(jsep){}})}function sipInfo(){let body={request:"info",type:"application/dtmf-relay",content:"signal=#\nduration=100\n"};if(issipCall){sipcall.send({message:body})}}let delayTime;$(document).on("VideoPlayed",function(ev){isScreen=false});$("#local_stream_video").dblclick(function(){isNormalVideo=!isNormalVideo;changeLocalStream(isNormalVideo);if(isNormalVideo){let remoteVideo=document.getElementById(remoteStreamVideo);let localVideo=document.getElementById(localStream);if(isScreen){if(remoteVideo){remoteVideo.style.transform="rotateY(0deg)"}if(localVideo){localVideo.style.transform="rotateY(0deg)"}}else{if(remoteVideo){remoteVideo.style.transform="rotateY(180deg)"}if(localVideo){localVideo.style.transform="rotateY(0deg)"}}}else{let remoteVideo=document.getElementById(remoteStreamVideo);let localVideo=document.getElementById(localStream);if(isScreen){if(remoteVideo){remoteVideo.style.transform="rotateY(0deg)"}if(localVideo){localVideo.style.transform="rotateY(0deg)"}}else{if(remoteVideo){remoteVideo.style.transform="rotateY(0deg)"}if(localVideo){localVideo.style.transform="rotateY(180deg)"}}}});function changeLocalVideo(){let remoteVideo=document.getElementById(remoteStreamVideo);let localVideo=document.getElementById(localStream);if(remoteVideo){remoteVideo.muted=true}if(localVideo){localVideo.muted=true}if(VideoLocalStream){var videoTracks=VideoLocalStream.getVideoTracks();if(videoTracks){if(videoTracks.length>0){Venus.attachMediaStream($("#"+remoteStreamVideo).get(0),VideoLocalStream)}}}if(isScreen&&remoteVideo){if(remoteVideo){remoteVideo.style.transform="rotateY(0deg)"}}else{if(remoteVideo){remoteVideo.style.transform="rotateY(180deg)"}}}function changeLocalStream(islocal){isNormalVideo=islocal;let remoteVideo=document.getElementById(remoteStreamVideo);let localVideo=document.getElementById(localStream);if(islocal){videoCallLocalStream(VideoRemoteStream,false);videoCallRemoteStream(VideoLocalStream,false);if(remoteVideo){remoteVideo.muted=true}if(localVideo){localVideo.muted=false}}else{videoCallRemoteStream(VideoRemoteStream,true);videoCallLocalStream(VideoLocalStream,true);if(remoteVideo){remoteVideo.muted=false}if(localVideo){localVideo.muted=true}}}function videoCallLocalStream(stream,type){if(stream){var videoTracks=stream.getVideoTracks();if(videoTracks){if(videoTracks.length>0&&localStream){Venus.attachMediaStream($("#"+localStream).get(0),stream)}}}}function videoCallRemoteStream(stream,type){let remoteVideo=document.getElementById(remoteStreamVideo);let localVideo=document.getElementById(localStream);if(type){if(remoteVideo){remoteVideo.muted=false}if(localVideo){localVideo.muted=true}}else{if(remoteVideo){remoteVideo.muted=true}if(localVideo){localVideo.muted=false}}if(delayTime){clearTimeout(delayTime)}share_manager.setShowLoading(true);showLocalvideo=true;if(share_manager.getLiveRecord()){}$("#video-call-camera-img").removeAttr("disabled");if(!isNormalVideo){videoCallLocalStream(VideoLocalStream,true)}if(remoteVideo){remoteVideo.style.transform="rotateY(0deg)"}if(stream){var audioTracks=stream.getAudioTracks();var videoTracks=stream.getVideoTracks();if(videoTracks){if(videoTracks.length>0&&remoteStreamVideo){Venus.attachMediaStream($("#"+remoteStreamVideo).get(0),stream)}}}$(document).trigger("startGetVideoMessage");setTimeout(()=>{getStats2(getVideoCall())},1e3)}let timerOut;function videoCallInCommingStream(stream){share_manager.setShowLoading(false);clearInterval(timerOut);timerOut=setInterval(()=>{getStats(venus_manager.getVideoCall(),function(status){if(status){clearInterval(timeInter);clearInterval(timerOut);share_manager.setShowLoading(true);if(delayTime){clearTimeout(delayTime)}showLocalvideo=true;if(share_manager.getLiveRecord()){}$("#video-call-camera-img").removeAttr("disabled");if(!isNormalVideo){videoCallLocalStream(VideoLocalStream,true)}let remoteVideo=document.getElementById(remoteStreamVideo);remoteVideo.style.transform="rotateY(0deg)";var audioTracks=stream.getAudioTracks();Venus.attachMediaStream($("#"+remoteStreamVideo).get(0),stream);$(document).trigger("startGetVideoMessage");setTimeout(()=>{getStats2(getVideoCall())},1e3)}})},100)}let callMessageTimer;function changeVideoGetStats(peer){getLocalStats(peer,function(results){for(var i=0;i<results.length;++i){var res=results[i];getLocalMessage(res)}clearInterval(callMessageTimer);callMessageTimer=setInterval(()=>{changeVideoGetStats(peer)},1e3)})}function getLocalMessage(message){if(parseInt(message["audioInputLevel"])>0){if(parseInt(message["googRtt"])>0){getAudioInfoTimerLocal(false,message["googRtt"])}}}function getStats(peer,callback){myGetStats(peer,function(results){for(var i=0;i<results.length;++i){var res=results[i];getCallMessage(res,function(status){callback(status)})}})}function getStats2(peer){myGetStats(peer,function(results){clearInterval(share_manager.getCallParam()["statisticsTimer"]);var timer=setInterval(()=>{getStats2(peer,function(message){})},1e3);share_manager.setCallParam("statisticsTimer",timer)})}function getCallMessage(message,callback){if(message["googContentType"]==="realtime"){if(parseInt(message["googRtt"])>0){share_manager.setCallParam("videoTextSend","\n");Object.keys(message).map((key,index)=>{if(key!=="googTimingFrameInfo"){let value=message[key];share_manager.setCallParam("videoTextSend",share_manager.getCallParam()["videoTextSend"]+key+" : "+value+"\n")}})}else{share_manager.setCallParam("videoTextReceive","\n");Object.keys(message).map((key,index)=>{if(key!=="googTimingFrameInfo"){let value=message[key];share_manager.setCallParam("videoTextReceive",share_manager.getCallParam()["videoTextReceive"]+key+" : "+value+"\n");if(key=="googFrameWidthReceived"){let value=message[key];if(parseInt(value)>50){callback(true)}}}})}}if(parseInt(message["audioInputLevel"])>0){if(parseInt(message["googRtt"])>0){getAudioInfoTimerLocal(false,message["googRtt"]);share_manager.setCallParam("audioTextSend","\n");Object.keys(message).map(key=>{let value=message[key];if(key==="googCodecName"){if(!share_manager.getIsComming()){Object.keys(getAudioCode()).map(()=>{switch(getAudioCode()[0]){case"opus":break;case"g722":value="G.722";break;case 2:value="AAC-LC";break;case 3:value="AAC-LD";break;case"alaw":value="G711a";break;case"mulaw":value="G711u";break}})}else{switch(parseInt(share_manager.getAudioType())){case 0:break;case 1:break;case 2:value="AAC-LC";break;case 3:value="AAC-LD";break;case 4:value="G711a";break;case 5:value="G711u";break}}}share_manager.setCallParam("audioTextSend",share_manager.getCallParam()["audioTextSend"]+key+" : "+value+"\n")})}else{share_manager.setCallParam("audioTextReceive","\n");Object.keys(message).map(key=>{let value=message[key];if(key==="googCodecName"){if(!share_manager.getIsComming()){Object.keys(getAudioCode()).map(()=>{switch(getAudioCode()[0]){case"opus":break;case"g722":value="G.722";break;case 2:value="AAC-LC";break;case 3:value="AAC-LD";break;case"alaw":value="G711a";break;case"mulaw":value="G711u";break}})}else{switch(parseInt(share_manager.getAudioType())){case 0:break;case 1:break;case 2:value="AAC-LC";break;case 3:value="AAC-LD";break;case 4:value="G711a";break;case 5:value="G711u";break}}}share_manager.setCallParam("audioTextReceive",share_manager.getCallParam()["audioTextReceive"]+key+" : "+value+"\n")})}}}function getVideoInfo(backMessage){VideoCall.getVideoInfo(function(callback){backMessage(callback)})}function getLocalStats(peer,callback){if(!!navigator.mozGetUserMedia){peer.getStats(function(res){var items=[];res.forEach(function(result){items.push(result)});callback(items)})}else{peer.getStats(function(res){var items=[];res.result().forEach(function(result){var item={};result.names().forEach(function(name){item[name]=result.stat(name)});item.id=result.id;item.type=result.type;item.timestamp=result.timestamp;items.push(item)});callback(items)})}}function getAudioCode(){let CodeType=localStorage.getItem("audioCodec");if(CodeType){return CodeType.split(",")}else{return["opus","g722","alaw","mulaw"]}}function myGetStats(peer,callback){if(!!navigator.mozGetUserMedia){peer.getStats(null).then(res=>{var items=[];res.forEach(function(result){items.push(result)});callback(items)},callback)}else{peer.getStats(null).then(stats=>{statsClassification(stats);callback()})}}function statsClassification(stats){share_manager.setCallParam("audioTextReceive","\n");share_manager.setCallParam("videoTextReceive","\n");share_manager.setCallParam("audioTextSend","\n");share_manager.setCallParam("videoTextSend","\n");let paramName="";stats.forEach(report=>{switch(report.type){case"inbound-rtp":switch(report.mediaType){case"audio":paramName="audioTextReceive";break;case"video":paramName="videoTextReceive";break;default:break}break;case"outbound-rtp":switch(report.mediaType){case"audio":paramName="audioTextSend";break;case"video":paramName="videoTextSend";break;default:break}break;case"candidate-pair":getAudioInfoTimerLocal(false,report["currentRoundTripTime"]*1e3);break;default:break}if(paramName!=""){Object.keys(report).forEach(statName=>{let value=report[statName];switch(statName){case"timestamp":statName="Date";value=new Date(value).toLocaleString();break;case"qualityLimitationDurations":value=JSON.stringify(value);break}share_manager.setCallParam(paramName,share_manager.getCallParam()[paramName]+statName+" : "+value+"\n")});paramName=""}})}let checkDeviceType;function registerVideoCall(registBack,errorBack,callStateMessage,backJsonMsg,jspMsg,messageCallback){ReloadDevice();clearInterval(checkDeviceType);checkDeviceType=setInterval(()=>{ReloadDevice()},5e3);if(VideoCall){for(var s in Venus.sessions){if(Venus.sessions[s]!==null&&Venus.sessions[s]!==undefined&&Venus.sessions[s].destroyOnUnload){Venus.log("Destroying session "+s);Venus.sessions[s].destroy({asyncRequest:false,notifyDestroyed:false})}}}Venus.init({debug:"all",callback:function(){started=true;if(!Venus.isWebrtcSupported()){return}venus=new Venus({server:venusUrl,success:function(){venus.attach({plugin:"venus.plugin.videocall",success:function(pluginHandle){VideoCall=pluginHandle;Venus.log("Plugin attached! ("+VideoCall.getPlugin()+", id="+VideoCall.getId()+")");let register={request:"register",username:name,domain:domain};VideoCall.send({message:register})},error:function(error){if(bell){calling.pause();ringing.pause()}Venus.error("  -- Error attaching plugin...",error);errorBack(error)},mediaState:function(medium,on){Venus.log("Venus "+(on?"started":"stopped")+" receiving our "+medium);if(medium=="video"&&on==false){}},onmessage:function(msg,jsep){console.log(msg);Venus.debug(" ::: Got a message :::");Venus.debug(JSON.stringify(msg));if(msg!=null&&msg!=undefined){let resultRecord=msg["videocall"];if(resultRecord==="event"){msg["type"]=liverectype;backJsonMsg(msg);if(msg["result"]!=null&&msg["result"]!=undefined){if(msg["result"]["list"]!=null&&msg["result"]["list"]!=undefined){Object.keys(msg["result"]["list"]).map((key,index)=>{var dict=msg["result"]["list"][key];if(dict["username"]==call_number){if(dict["user_type"]=="live_endpoint"||dict["user_type"]=="liverecord_endpoint"){callTypeIsLive=true}}})}}var errCode=msg["error_code"];if(errCode&&parseInt(errCode)===480){callStateMessage({errCode:486,errMsg:msg["error"]})}if(msg["event"]!=null&&msg["event"]!=undefined&&msg["event"]=="invite_meeting"){commingName=msg["room"];incomingType="room";callStateMessage("incomingCall")}}else{backJsonMsg(msg)}if(msg["venus"]&&msg["venus"]==="hangup"){isVideoCalling=false;incoming=false;isNormalVideo=false;if(bell){calling.pause();ringing.pause()}if(VideoCall){}if(msg["reason"]&&msg["reason"].toLowerCase()==="busy here"){$(document).trigger("Busy");callStateMessage({errCode:486,errMsg:msg["reason"]})}if(!share_manager.getCallParam()["rejectTheCall"]){$(document).trigger("hangup",[msg["reason_code"],msg["reason"]]);callStateMessage("hangup")}$("#video_cover_amplesky").hide();share_manager.cleanCallParam()}}if(msg.event!=null&&msg.event!=undefined){invite_media_type=msg.media_type}if(jsep!=null&&jsep!=undefined){jspMsg(jsep)}var error=msg["error"];if(error!=null&&error!=undefined){callErrorresult=error;if(bell){calling.pause();ringing.pause()}errorBack(error);if(!registered){Venus.log("User is not registered")}else{VideoCall.hangup()}if(callStatefuntionback){return callStatefuntionback(0)}}var result=msg["result"];if(result!==null&&result!==undefined&&result["event"]!==undefined&&result["event"]!==null){var event=result["event"];switch(event){case"registration_failed":share_manager.setVideoCallRegistStatus(0);Venus.error("Registration failed: "+result["code"]+" "+result["reason"]);registBack(0);errorback("mcu  "+result["reason"]);return;break;case"registered":if(!registered){Venus.log("Successfully registered as "+result["username"]+"!");registered=true;share_manager.setVideoCallRegistStatus(1);if(parseInt(localStorage.getItem("SetMeetServer"))!=0){registSipCall(function(sipRegist){registBack(sipRegist)},function(sipError){errorBack(sipError)},function(callMessage){callStateMessage(callMessage)},function(jsonMsg){backJsonMsg(jsonMsg)},function(jsp){jspMsg(jsp)},function(msg,type){messageCallback(msg,type)})}else{registBack("success")}}registered=true;break;case"unregistered":Venus.log("Successfully unregistered as "+result["username"]+"!");if(registered){share_manager.setVideoCallRegistStatus(0);registered=false;$(document).trigger("unregistered");registBack(0)}break;case"calling":issipCall=false;Venus.log("Waiting for the peer to answer...");$(document).trigger("calling");callStateMessage("calling");share_manager.setCallParam("status","ringing");if(callStatefuntionback){return callStatefuntionback(1)}break;case"incomingcall":if(share_manager.getCallParam()["status"]!=="ready"){var hangup={request:"hangup",code:486};VideoCall.send({message:hangup});share_manager.setCallParam("rejectTheCall",true);return}isVideoCalling=true;currentJsep=jsep;issipCall=false;commingName=result["username"];incomingType=currentJsep.sdp.indexOf("m=video ")>-1?"video":"audio";userInCall=commingName;counterpartNum=msg.result.username.split("@")[0].split(":")[1];incoming=true;if(bell){ringing.play()}Venus.log("Incoming call from "+result["username"]+"!");$(document).trigger("incomingCall",counterpartNum);callStateMessage("incomingCall");break;case"progress":Venus.log("There's early media from "+result["username"]+", wairing for the call!");if(jsep!==null&&jsep!==undefined){handleRemote(jsep)}break;case"accepted":share_manager.setCallParam("status","onCall");issipCall=false;if(bell){calling.pause();ringing.pause()}if(jsep!==null&&jsep!==undefined){jsep=change_ice_sdp(jsep);console.log(jsep.sdp);if(!incoming){}handleRemote(jsep)}$(document).trigger("callAccepted");callStateMessage("accepted");if(!incoming){if(VideoLocalStream){var videoTracks=VideoLocalStream.getVideoTracks();if(videoTracks){if(videoTracks.length>0&&remoteStreamVideo&&callTypeIsLive){Venus.attachMediaStream($("#"+remoteStreamVideo).get(0),VideoLocalStream);let remoteVideo=document.getElementById(remoteStreamVideo);if(remoteVideo){remoteVideo.muted=true;$(document).trigger("is_living")}}}}}break;case"hangup":if(isSipCalling){return}isVideoCalling=false;incoming=false;isNormalVideo=false;if(bell){calling.pause();ringing.pause()}if(VideoCall){}if(result["reason"]&&result["reason"].toLowerCase()==="busy here"){$(document).trigger("Busy");callStateMessage({errCode:486,errMsg:result["reason"]})}if(!share_manager.getCallParam()["rejectTheCall"]){$(document).trigger("hangup",[result["code"],result["reason"]]);callStateMessage("hangup")}$("#video_cover_amplesky").hide();share_manager.cleanCallParam();break;case"decline":incoming=false;if(bell){calling.pause();ringing.pause()}$(document).trigger("declineCall");share_manager.cleanCallParam();case"updatingcall":break;case"slowlink":console.log("=========");console.log(result);break;default:break}}else{if(result!=undefined&&result!=null){if(result["videocall"]!==undefined&&result["videocall"]!=null){backJsonMsg(result)}}else{if(msg["venus"]!=null&&msg["venus"]!=undefined&&msg["venus"]=="hangup"){isVideoCalling=false;incoming=false;isNormalVideo=false;if(bell){calling.pause();ringing.pause()}if(VideoCall){VideoCall.hangup()}if(msg["reason"]&&msg["reason"].toLowerCase()==="busy here"){$(document).trigger("Busy");callStateMessage({errCode:486,errMsg:msg["reason"]})}if(!share_manager.getCallParam()["rejectTheCall"]){$(document).trigger("hangup",["3",msg["reason"]]);callStateMessage("hangup")}share_manager.cleanCallParam()}}}messageCallback(msg,"videoCall")},onlocalstream:function(stream){Venus.debug(" ::: Got a local stream :::");Venus.debug(JSON.stringify(stream));var videoTracks=stream.getVideoTracks();VideoLocalStream=stream;if(videoTracks){if(videoTracks.length>0){videoCallLocalStream(stream,true)}}},onremotestream:function(stream){Venus.debug(" ::: Got a remote stream :::");Venus.debug(JSON.stringify(stream));VideoRemoteStream=stream;var audioTracks=stream.getAudioTracks();var videoTracks=stream.getVideoTracks();if(videoTracks){if(videoTracks.length>0){videoCallRemoteStream(stream,true)}else{var imgSrc=share_manager.getImageByUser(userInCall);if(!imgSrc){imgSrc="./photo/audio.gif"}var videoCoverDiv=$("#video_cover_amplesky");var videoCoverImg=$("#video_cover_img");if(videoCoverDiv.length>0&&videoCoverImg.length>0){videoCoverImg.attr("src",imgSrc);videoCoverDiv.show()}else{var remoteStreamVideoElement=$("#"+remoteStreamVideo);videoCoverDiv=$("<div id='video_cover_amplesky'></div>");videoCoverImg=$("<img id='video_cover_img' src='"+imgSrc+"'/>");videoCoverDiv.append(videoCoverImg);videoCoverDiv.width(remoteStreamVideoElement.width());videoCoverDiv.height(remoteStreamVideoElement.height());videoCoverDiv.css("text-align","center");videoCoverDiv.css("vertical-align","middle");videoCoverDiv.css("display","flex");videoCoverDiv.css("align-items","center");videoCoverDiv.css("position","absolute");videoCoverDiv.css("z-index","1");videoCoverImg.css("max-width","100%");videoCoverImg.css("max-height","100%");videoCoverImg.css("margin","auto");remoteStreamVideoElement.before(videoCoverDiv)}Venus.attachMediaStream($("#"+remoteStreamAudio).get(0),new MediaStream(stream))}}else{Venus.attachMediaStream($("#"+remoteStreamAudio).get(0),new MediaStream(stream))}},oncleanup:function(){}})},error:function(error){$(document).trigger("netError");started=false;registered=false;registBack(0);errorBack(error);Venus.warn("videocall plugin error:\n"+JSON.stringify(error))},destroyed:function(){started=false;registered=false}})}})}function start_talkback(isRoom){var body={request:"info",type:"application/dtmf-relay",content:"signal=#\nduration=100\n"};if(sipcall&&issipCall){sipcall.send({message:body})}if(!!isRoom){var channel=parseInt(RoomNumber);var participants=roomParticipants[channel+""];if(participants){Object.keys(participants).forEach(function(id){if(participants[id].display&&roomParticipants[channel+""]){var body={request:"info",room:channel,feed:participants[id].id,type:"application/dtmf-relay",content:"signal=#\nduration=100\n"};videoRoomCall.send({message:body});console.log("room cancle sip info: "+JSON.stringify(body))}})}}if(share_manager.getIsWxs()&&globalVariable.call.callType===1){var channel=globalVariable.call.channel;var participants=globalVariable.roomParticipants[channel+""];if(participants){Object.keys(participants).forEach(function(id){if(participants[id].display){var body={request:"info",room:channel,feed:participants[id].id,type:"application/dtmf-relay",content:"signal=#\nduration=100\n"};videoRoomCall.send({message:body});console.log("room cancle sip info: "+JSON.stringify(body))}})}}}function stop_talkback(isRoom){var body={request:"info",type:"application/dtmf-relay",content:"signal=*\nduration=100\n"};if(sipcall&&issipCall){sipcall.send({message:body})}if(!!isRoom){var channel=parseInt(RoomNumber);var participants=roomParticipants[channel+""];if(participants){Object.keys(participants).forEach(function(id){if(participants[id].display&&roomParticipants[channel+""]){var body={request:"info",room:channel,feed:participants[id].id,type:"application/dtmf-relay",content:"signal=*\nduration=100\n"};videoRoomCall.send({message:body});console.log("room sip info: "+JSON.stringify(body))}})}}if(share_manager.getIsWxs()&&globalVariable.call.callType===1){var channel=globalVariable.call.channel;var participants=globalVariable.roomParticipants[channel+""];if(participants){Object.keys(participants).forEach(function(id){if(participants[id].display){var body={request:"info",room:channel,feed:participants[id].id,type:"application/dtmf-relay",content:"signal=*\nduration=100\n"};videoRoomCall.send({message:body});console.log("room sip info:"+JSON.stringify(body))}})}}}function change_ice_sdp(jsep){var transport_type=parseInt(localStorage.getItem("transportType"));if(transport_type==0){jsep=edit_sdp(jsep,"udp");var reCheck=jsep.sdp.indexOf("a=candidate:");var sub=jsep.sdp.substring(reCheck,reCheck+23);var next_data=sub.indexOf("udp");if(reCheck>0&&next_data>0){change_ice_sdp(jsep)}console.log(jsep.sdp)}else{jsep=edit_sdp(jsep,"tcp");var reCheck=jsep.sdp.indexOf("a=candidate:");var sub=jsep.sdp.substring(reCheck,reCheck+23);var next_data=sub.indexOf("tcp");if(reCheck>0&&next_data>0){change_ice_sdp(jsep)}console.log(jsep.sdp)}return jsep}function edit_sdp(jsep,type){var index=jsep.sdp.indexOf("a=candidate:1 1");jsep=edit_sdp_string(jsep,index,type);var index2=jsep.sdp.indexOf("a=candidate:2 1");jsep=edit_sdp_string(jsep,index2,type);var index3=jsep.sdp.indexOf("a=candidate:3 1");jsep=edit_sdp_string(jsep,index3,type);var index4=jsep.sdp.indexOf("a=candidate:4 1");jsep=edit_sdp_string(jsep,index4,type);var index5=jsep.sdp.indexOf("a=candidate:5 1");jsep=edit_sdp_string(jsep,index5,type);var index6=jsep.sdp.indexOf("a=candidate:6 1");jsep=edit_sdp_string(jsep,index6,type);var index7=jsep.sdp.indexOf("a=candidate:7 1");jsep=edit_sdp_string(jsep,index7,type);var index8=jsep.sdp.indexOf("a=candidate:8 1");jsep=edit_sdp_string(jsep,index8,type);var index9=jsep.sdp.indexOf("a=candidate:9 1");jsep=edit_sdp_string(jsep,index9,type);return jsep}function edit_sdp_string(jsep,index,type){if(index>0){for(var i=index;i<jsep.sdp.length;i++){var substr1=jsep.sdp.substr(i,11);var check=substr1.indexOf("\r\n");if(check>0){var subIndex=jsep.sdp.substring(index,i+check+2);if(subIndex.indexOf(type)>0){jsep.sdp=jsep.sdp.replace(subIndex,"");jsep.sdp=jsep.sdp.replace(subIndex,"");break}break}}}return jsep}function p2pTransform(typea,callType){let Livetype=typea;if(Livetype=="live"){liverectype="hliveloc"}else{liverectype="mrecord"}var requestList={request:"virtual_endpoint",request_cmd:"rtp_forward",endpoint_type:Livetype};if(callType==="videoCall"){if(issipCall){if(sipcall){sipcall.send({message:requestList})}}else{if(VideoCall){VideoCall.send({message:requestList})}}}else if(callType==="roomCall"){let paramer={request:"virtual_endpoint",request_cmd:"rtp_forward",endpoint_type:Livetype,publisher_id:myid,room:parseInt(RoomNumber),secre:"adminpwd"};if(videoRoomCall){videoRoomCall.send({message:paramer})}}}function p2pLiveRecord(callType){liverectype="liverecord";let requestlist={request:"virtual_endpoint",request_cmd:"rtp_forward",endpoint_type:"liverecord"};if(callType==="videoCall"){if(issipCall){if(sipcall){sipcall.send({message:requestlist})}}else{if(VideoCall){VideoCall.send({message:requestlist})}}}else if(callType==="roomCall"){let paramer={request:"virtual_endpoint",request_cmd:"rtp_forward",endpoint_type:"liverecord",publisher_id:myid,room:parseInt(RoomNumber),secret:"adminpwd"};if(videoRoomCall){videoRoomCall.send({message:paramer})}}}function StopTransform(callType){return;let requestlist={request:"virtual_endpoint",request_cmd:"stop_rtp_forward"};if(callType==="videoCall"){if(issipCall){if(sipcall){sipcall.send({message:requestlist})}}else{if(VideoCall){VideoCall.send({message:requestlist})}}}else if(callType==="roomCall"){let paramer={request:"virtual_endpoint",request_cmd:"stop_rtp_forward",publisher_id:myid,room:RoomNumber};if(videoRoomCall){videoRoomCall.send({message:paramer})}}}function requesUserOnLineList(){if(VideoCall){let message={request:"list"};VideoCall.send({message:message})}}function login(){var register={username:"sip:"+name+"@"+server,display_name:name,authuser:name,secret:password,proxy:"sip:"+server+":"+port,sips:false,request:"register"};if(sipcall){sipcall.send({message:register})}}function logout(){if(issipCall){if(sipcall){var unregister={request:"unregister"};sipcall.send({message:unregister})}}else{}if(videoRoomCall){videoRoomCall=null}if(VideoCall){for(var s in Venus.sessions){if(Venus.sessions[s]!==null&&Venus.sessions[s]!==undefined&&Venus.sessions[s].destroyOnUnload){Venus.sessions[s].destroy({asyncRequest:false,notifyDestroyed:false})}}}venus=null;sipRegister=false;registered=false;if(StreamVenus){StreamVenus.destroy()}if(Streaming){Streaming=null}if(Roomvenus){exitRoom()}playRecordStop();if(PartyListInterval){clearInterval(PartyListInterval);PartyListInterval=0}venus=null}function refreshLogOut(){if(issipCall){if(sipcall){var unregister={request:"unregister"};sipcall.send({message:unregister})}}else{}if(videoRoomCall){}if(VideoCall){Venus.destroy()}sipRegister=false;registered=false}function getSipStatus(){return sipRegister}function getVideoCallStatus(){return registered}function getStr(string,str){var str_before=string.split(str)[0];var str_after=string.split(str)[1];if(str_before.length>0){return str_before}else{return str_after}}function is_bell(enabel){bell=enabel}function call(to,isScreenVideo,videoSend,videoRecv,audioSend,audioRecv,callstatesback){call_number=to;if(to.indexOf("@")!=-1&&to.indexOf("+")!=-1){call_number=to.split("+")[1]}userInCall=call_number;callTypeIsLive=false;requesUserOnLineList();showLocalvideo=false;isNormalVideo=false;if(bell){calling.play()}callStatefuntionback=callstatesback;issipCall=to.indexOf("@")!=-1||to.indexOf("+")!=-1;isVideoCalling=!issipCall;let name=getStr(to,"@");var IP;var sipUri;if(to.indexOf("@")!=-1){IP=to.split("@")[1];sipUri="sip:"+name+"@"+IP}else if(to.indexOf("+")!=-1){IP=to.split("+")[1];sipUri="sip:"+IP+"@"+server+":"+port}let SpecName;if(to.indexOf("@")!=-1&&to.indexOf("+")!=-1){SpecName=to.split("+")[1];SpecName=getStr(SpecName,"@");IP=to.split("@")[1];sipUri="sip:"+SpecName+"@"+IP+":"+port}if(issipCall){share_manager.setCallParam("otherSide",sipUri);incoming=false;sipcall.createOffer({media:isScreenVideo?{video:"screen",captureDesktopAudio:hasMic,audioSend:hasMic?audioSend:false,audioRecv:audioRecv,videoSend:videoSend,videoRecv:videoRecv}:{audioSend:hasMic?audioSend:false,audioRecv:audioRecv,videoSend:hasCamer?videoSend:false,videoRecv:videoRecv},success:function(jsep){Venus.debug("Got SDP!");jsep=changeSDP(jsep);Venus.log(jsep.sdp);jsep=change_ice_sdp(jsep);var body={request:"call",uri:sipUri,factory_id:_factory_id,call_reqinfo:_call_reqinfo};sipcall.send({message:body,jsep:jsep});counterpartNum=to;share_manager.setCallParam("status","ringing");callTimeOut(sipUri)},error:function(error){Venus.error("WebRTC error...",error);if(callStatefuntionback){return callStatefuntionback(0)}}})}else{share_manager.setCallParam("otherSide",name);incoming=false;VideoCall.createOffer({media:isScreenVideo?{video:"screen",captureDesktopAudio:hasMic?audioSend:false,audioSend:hasMic?audioSend:false,audioRecv:audioRecv,videoSend:videoSend,videoRecv:videoRecv}:{audioSend:hasMic?audioSend:false,audioRecv:true,videoSend:hasCamer?videoSend:false,videoRecv:videoRecv},simulcast:false,success:function(jsep){jsep=changeSDP(jsep);Venus.debug("Got SDP!");Venus.debug(jsep);jsep=change_ice_sdp(jsep);var body={request:"call",username:name,factory_id:_factory_id};console.log("venus_manage:call body------------------",body);VideoCall.send({message:body,jsep:jsep});share_manager.setCallParam("status","ringing");callTimeOut(name)},error:function(error){Venus.error("WebRTC error...",error);if(callStatefuntionback){return callStatefuntionback(0)}}})}}function setPeerBitrate(bitrate){let parater={request:"set",bitrate:parseInt(bitrate)*1e3};if(!issipCall){if(bitrate!=0){VideoCall.send({message:parater})}}}function setbitrate(sdp,media,bitrate){var lines=sdp.split("\n");var line=-1;for(var i=0;lines.length;i++){if(lines[i].indexOf("m="+media)==0){line=i;break}}if(line===-1){return sdp}line++;while(lines[line].indexOf("i=")===0||lines[line].indexOf("c=")===0){line++}if(lines[line].indexOf("b")===0){lines[line]="b=AS:"+bitrate;return lines.join("\n")}var newLines=lines.slice(0,line);newLines.push("b=AS:"+bitrate);newLines=newLines.concat(lines.slice(line,lines.length));return newLines.join("\n")}function set_call_codec_type(audio_type,video_type,use_default){if(use_default){localStorage.setItem("defaultCodec",1)}else{localStorage.setItem("defaultCodec",0)}localStorage.setItem("videoCodec",video_type);localStorage.setItem("audioCodec",audio_type)}function changeSDP(jsep){var defaultCodeFlag=false;if(parseInt(localStorage.getItem("defaultCodec"))){defaultCodeFlag=true}jsep.sdp=jsep.sdp.replace("a=ice-options:trickle\r\n","");jsep.sdp=jsep.sdp.replace("a=extmap:13 urn:3gpp:video-orientation","");var sdpLines=jsep.sdp.split("\n");var audioCodec=localStorage.getItem("audioCodec");if(!audioCodec){audioCodec=["opus","g722","alaw","mulaw"]}var opus=audioCodec.indexOf("opus");var g722=audioCodec.indexOf("g722");var pcma=audioCodec.indexOf("alaw");var pcmu=audioCodec.indexOf("mulaw");var videoCodec=localStorage.getItem("videoCodec");var videoCodecs=[];if(videoCodec==!undefined||videoCodec==null){videoCodec=["vp8","vp9","h264"]}var vp8=videoCodec.indexOf("vp8");var vp9=videoCodec.indexOf("vp9");var h264=videoCodec.indexOf("h264");var removePayload=[];var mAudioLine=-1;var mVideoLine=-1;for(var ii=0,len=sdpLines.length;ii<len;ii++){if(sdpLines[ii].indexOf("m=audio")===0){mAudioLine=ii}if(sdpLines[ii].indexOf("m=video")===0){mVideoLine=ii}}if(mAudioLine===-1&&mVideoLine===-1){return jsep}if(mAudioLine>=0&&!defaultCodeFlag){var mAudio=sdpLines[mAudioLine].split("UDP/TLS/RTP/SAVPF")[1];if(mAudio){var audioCodecs=mAudio.replace("\r","").split(" ");var a_103=audioCodecs.indexOf("103");if(a_103>0){audioCodecs.splice(a_103,1)}var a_104=audioCodecs.indexOf("104");if(a_104>0){audioCodecs.splice(a_104,1)}if(opus<0){var a_111=audioCodecs.indexOf("111");if(a_111>0){audioCodecs.splice(a_111,1)}}if(g722<0){var a_9=audioCodecs.indexOf("9");if(a_9>0){audioCodecs.splice(a_9,1)}}if(pcma<0){var a_8=audioCodecs.indexOf("8");if(a_8>0){audioCodecs.splice(a_8,1)}}if(pcmu<0){var a_0=audioCodecs.indexOf("0");if(a_0>0){audioCodecs.splice(a_0,1)}}sdpLines[mAudioLine]="m=audio 9 UDP/TLS/RTP/SAVPF"+audioCodecs.join(" ")}}if(mVideoLine>=0){var mVideo=sdpLines[mVideoLine].split("UDP/TLS/RTP/SAVPF")[1];if(mVideo){videoCodecs=mVideo.replace("\r","").split(" ")}}var interceptPayload=null;if(!isNaN(share_manager.getIntercept())){interceptPayload=share_manager.getIntercept()}for(var i=0,length=sdpLines.length;i<length;i++){var sdpLine=sdpLines[i];if(sdpLine&&sdpLine.trim().length>0){if(sdpLine.indexOf("a=extmap-allow-mixed")===0){sdpLines.splice(i,1);i--;continue}if(mAudioLine>=0&&!defaultCodeFlag){if(a_103>0&&sdpLine.indexOf("a=rtpmap:103 ")===0){sdpLines.splice(i,1);i--;continue}if(a_104>0&&sdpLine.indexOf("a=rtpmap:104 ")===0){sdpLines.splice(i,1);i--;continue}if(opus<0&&(sdpLine.indexOf("a=rtpmap:111 ")===0||sdpLine.indexOf("a=rtcp-fb:111 ")===0||sdpLine.indexOf("a=fmtp:111 ")===0)){sdpLines.splice(i,1);i--;continue}if(g722<0&&(sdpLine.indexOf("a=rtpmap:9 ")===0||sdpLine.indexOf("a=rtcp-fb:9 ")===0||sdpLine.indexOf("a=fmtp:9 ")===0)){sdpLines.splice(i,1);i--;continue}if(pcma<0&&(sdpLine.indexOf("a=rtpmap:8 ")===0||sdpLine.indexOf("a=rtcp-fb:8 ")===0||sdpLine.indexOf("a=fmtp:8 ")===0)){sdpLines.splice(i,1);i--;continue}if(pcmu<0&&(sdpLine.indexOf("a=rtpmap:0 ")===0||sdpLine.indexOf("a=rtcp-fb:0 ")===0||sdpLine.indexOf("a=fmtp:0 ")===0)){sdpLines.splice(i,1);i--;continue}}if(mVideoLine>=0){if(!defaultCodeFlag&&vp8<0&&videoCodecs&&/a=rtpmap:(\w+) VP8\/90000/.test(sdpLine)){sdpLines.splice(i,1);i--;var vp8Payload=RegExp.$1;videoCodecs.splice(videoCodecs.indexOf(vp8Payload),1);removePayload.push(vp8Payload);continue}if(!defaultCodeFlag&&vp9<0&&videoCodecs&&/a=rtpmap:(\w+) VP9\/90000/.test(sdpLine)){sdpLines.splice(i,1);i--;var vp9Payload=RegExp.$1;videoCodecs.splice(videoCodecs.indexOf(vp9Payload),1);removePayload.push(vp9Payload);continue}if(!defaultCodeFlag&&h264<0&&videoCodecs&&/a=rtpmap:(\w+) H264\/90000/.test(sdpLine)){sdpLines.splice(i,1);i--;var h264Payload=RegExp.$1;videoCodecs.splice(videoCodecs.indexOf(h264Payload),1);removePayload.push(h264Payload);continue}if(!isNaN(interceptPayload)&&sdpLine.indexOf("a=rtpmap:"+interceptPayload+" ")===0){sdpLines.splice(i,1);videoCodecs.splice(videoCodecs.indexOf(interceptPayload+""),1);removePayload.push(interceptPayload+"");i--;continue}}}}mVideoLine=sdpLines.findIndex(item=>{return item.indexOf("m=video 9 UDP/TLS/RTP/SAVPF")===0});if(mVideoLine>=0){sdpLines[mVideoLine]="m=video 9 UDP/TLS/RTP/SAVPF"+videoCodecs.join(" ")}removePayload.forEach(payload=>{var list=sdpLines.filter(item=>{return item.indexOf("a=rtcp-fb:"+payload+" ")===0||item.indexOf("a=fmtp:"+payload+" ")===0});list.forEach(item=>{sdpLines.splice(sdpLines.indexOf(item),1)})});jsep.sdp=sdpLines.join("\n");console.log("修改后：\n"+jsep.sdp);return jsep}function videoCallType(callBack){if(issipCall){callBack(true)}else{callBack(false)}}function handleRemote(jsep){if(issipCall){Venus.log(jsep.sdp);sipcall.handleRemoteJsep({jsep:jsep,error:function(){var hangup={request:"hangup"};sipcall.send({message:hangup});sipcall.hangup()}})}else{VideoCall.handleRemoteJsep({jsep:jsep,error:function(){var hangup={request:"hangup"};VideoCall.send({message:hangup});VideoCall.hangup()}})}}function answer(isScreenVideo,callJsp){isNormalVideo=false;let isVideo=currentJsep.sdp.indexOf("m=video ")>-1;if(isVideo&&!hasCamer||isVideo&&isScreenVideo){isVideo="screen"}currentJsep=change_ice_sdp(currentJsep);incoming=true;if(issipCall){sipcall.createAnswer({jsep:currentJsep,media:isScreenVideo?{video:"screen",captureDesktopAudio:hasMic,audioSend:hasMic?true:false,audioRecv:true,videoSend:true,videoRecv:true}:{audioSend:hasMic?true:false,audioRecv:true,videoSend:hasCamer,videoRecv:true},success:function(jsep){Venus.debug("Got SDP! audio="+true+", video="+true);Venus.debug(jsep);if(currentJsep.sdp.indexOf("opus")>=0){share_manager.setAudioType(0)}else if(currentJsep.sdp.indexOf("G722")>=0){share_manager.setAudioType(1)}else if(currentJsep.sdp.indexOf("G722ca")>=0){share_manager.setAudioType(2)}else if(currentJsep.sdp.indexOf("G722cd")>=0){share_manager.setAudioType(3)}else if(currentJsep.sdp.indexOf("PCMA")>=0){share_manager.setAudioType(4)}else if(currentJsep.sdp.indexOf("PCMU")>=0){share_manager.setAudioType(5)}var body={request:"accept"};jsep=change_ice_sdp(jsep);sipcall.send({message:body,jsep:jsep})},error:function(error){Venus.error("WebRTC error:",error);var body={request:"decline",code:480};sipcall.send({message:body})}})}else{VideoCall.createAnswer({jsep:currentJsep,media:isScreenVideo?{video:"screen",captureDesktopAudio:hasMic,audioSend:hasMic?true:false,audioRecv:true,videoSend:true,videoRecv:true}:{audioSend:hasMic?true:false,audioRecv:true,videoSend:hasCamer,videoRecv:true},success:function(jsep){jsep=change_ice_sdp(jsep);share_manager.setCodeInfo(0);if(currentJsep.sdp.indexOf("H265")>=0){share_manager.setCodeInfo(1);console.log(jsep.sdp)}if(currentJsep.sdp.indexOf("opus")>=0){share_manager.setAudioType(0)}else if(currentJsep.sdp.indexOf("G722")>=0){share_manager.setAudioType(1)}else if(currentJsep.sdp.indexOf("G722ca")>=0){share_manager.setAudioType(2)}else if(currentJsep.sdp.indexOf("G722cd")>=0){share_manager.setAudioType(3)}else if(currentJsep.sdp.indexOf("PCMA")>=0){share_manager.setAudioType(4)}else if(currentJsep.sdp.indexOf("PCMU")>=0){share_manager.setAudioType(5)}Venus.debug("Got SDP! audio="+true+", video="+true);Venus.debug(jsep);var body={request:"accept"};VideoCall.send({message:body,jsep:jsep})},error:function(error){if(String(error).indexOf("DOMException: Permission denied")!=0){return}Venus.error("WebRTC error:",error);var body={request:"decline",code:480};VideoCall.send({message:body})}})}}function hangup(){clearTimeout(timerOut);CloseLocalVideoExit();if(bell){calling.pause();ringing.pause()}$("#video_cover_amplesky").hide();let remoteVideo=document.getElementById(remoteStreamVideo);let localVideo=document.getElementById(localStream);if(remoteVideo){remoteVideo.muted=true}if(localVideo){localVideo.muted=true}var hangup={request:"hangup"};if(issipCall){sipcall.send({message:hangup});sipcall.hangup()}else{if(VideoCall){VideoCall.send({message:hangup});VideoCall.hangup()}}if(incoming){incoming=false;decline()}share_manager.cleanCallParam()}function sip_switch_other_mic(mute,isRoom){var body={request:"message",content:mute?"sip_mute_mic":"sip_unmute_mic"};if(issipCall){sipcall.send({message:body})}if(isRoom){var body={request:"message",content:mute?"sip_mute_mic":"sip_unmute_mic"};videoRoomCall.send({message:body})}}function getCommingCall(jespCall){return jespCall(commingName,incomingType)}function decline(){incoming=false;var body={request:"decline"};if(issipCall){sipcall.send({message:body})}else{var hangup={request:"hangup"};VideoCall.send({message:hangup});VideoCall.hangup()}}function getCounterpartNum(){return counterpartNum}function SwitchAudio(mute){audioEnable=mute;if(issipCall){if(audioEnable){sipcall.muteAudio()}else{sipcall.unmuteAudio()}}else{if(audioEnable){VideoCall.muteAudio()}else{VideoCall.unmuteAudio()}}}function videoCallSwitch(mute){if(issipCall){if(mute){sipcall.muteAudio()}else{sipcall.unmuteAudio()}}else{if(mute){VideoCall.muteAudio()}else{VideoCall.unmuteAudio()}}}function SDKSwitchAudio(mute){globalVariable.local.mute=mute;if(globalVariable.call.callType){if(videoRoomCall){if(mute){videoRoomCall.muteAudio()}else{videoRoomCall.unmuteAudio()}}}else{if(issipCall){if(mute){sipcall.muteAudio()}else{sipcall.unmuteAudio()}}else{if(mute){VideoCall.muteAudio()}else{VideoCall.unmuteAudio()}}}}function AllOpen(){audioEnable=false;videoEnable=false}function switchVideo(mute){console.log("issipCall--------------------: ",issipCall);if(issipCall){if(mute){sipcall.muteVideo()}else{sipcall.unmuteVideo()}}else{console.log("mute--------------------",mute);if(mute){VideoCall.muteVideo()}else{VideoCall.unmuteVideo()}}}function muteRoomSpeaker(muted){Venus.log("Mute room speaker: muted="+muted);if(muted){videoRoomCall.muteAudio()}else{videoRoomCall.unmuteAudio()}}function SDKSwitchVideo(mute){if(issipCall){if(mute){sipcall.muteVideo()}else{sipcall.unmuteVideo()}}else{if(mute){VideoCall.muteVideo()}else{VideoCall.unmuteVideo()}}}function SDKSwitchRoomCamera(mute){if(mute){videoRoomCall.muteVideo()}else{videoRoomCall.unmuteVideo()}}let cameraVideo=false;let checkDevice;function ampleskyRegistVideoRoom(localVideoID,server,isCameraVideo,roomNumber,maxPubliser,userName,avType,successCallback,errorCallback,remoteStreamCallback,messageCallback){ReloadDevice();clearInterval(checkDevice);if(share_manager.getIsSDK()){roomDomFree=[...roomDomIdSets]}checkDevice=setInterval(()=>{ReloadDevice()},5e3);cameraVideo=isCameraVideo;if(Roomvenus){attachRoom(localVideoID,isCameraVideo,roomNumber,maxPubliser,userName,avType,successCallback,errorCallback,remoteStreamCallback,messageCallback)}else{Venus.init({debug:"all",callback:function(){Roomvenus=new Venus({server:server,success:function(){attachRoom(localVideoID,isCameraVideo,roomNumber,maxPubliser,userName,avType,successCallback,errorCallback,remoteStreamCallback,messageCallback)}})}})}}function attachRoom(localVideoID,isCameraVideo,roomNumber,maxPubliser,userName,avType,successCallback,errorCallback,remoteStreamCallback,messageCallback){Roomvenus.attach({plugin:"venus.plugin.videoroom",opaqueId:opaqueId,success:function(pluginHandle){videoRoomCall=pluginHandle;checkRoom(roomNumber);if(successCallback){successCallback(pluginHandle)}},error:function(error){Venus.error("  -- Error attaching plugin...",error);exitRoom();if(errorCallback){errorCallback(error)}},consentDialog:function(on){if(on){}},mediaState:function(medium,on){Venus.log("Venus "+(on?"started":"stopped")+" receiving our "+medium)},webrtcState:function(on){Venus.log("Venus says our WebRTC PeerConnection is "+(on?"up":"down")+" now");if(!on)return false},onmessage:function(msg,jsep){Venus.debug("room msg:");Venus.debug(msg);var feeds=[];let event=msg["videoroom"];if(event!=undefined&&event!=null){if(event=="success"){PartyList(roomNumber);getRoomMaxPublisher(msg,roomNumber);if(msg["exists"]==false){if(parseInt(maxPubliser)>9||parseInt(maxPubliser)<0){maxPubliser=9}createRoom(parseInt(roomNumber),parseInt(maxPubliser))}else{if(!isInRoom){Venus.log("Room "+roomNumber+" exists, join it.");joinRoom(parseInt(roomNumber),userName)}}}else if(event=="created"){if(roomNumber){Venus.log("Room "+roomNumber+" is created, join it.");joinRoom(parseInt(roomNumber),userName)}}if(event=="participants"&&isInRoom){checkRoomTotalPublisher(msg,avType=="video"?2:1)}if(event=="joined"){myid=msg["id"];mypvtid=msg["private_id"];isInRoom=true;RoomList()}if(msg["publishers"]!==undefined&&msg["publishers"]!==null){var list=msg["publishers"];creater_id=msg["creater_id"]}else if(msg["unpublished"]!==undefined&&msg["unpublished"]!==null){var unpublished=msg["unpublished"];if(unpublished==="ok"){videoRoomCall.hangup();return}var remoteFeed=null;for(var i=0;i<8;i++){if(feeds[i]!=null&&feeds[i]!=undefined&&feeds[i].rfid==unpublished){remoteFeed=feeds[i];break}}if(remoteFeed!=null){feeds[remoteFeed.rfindex]=null;remoteFeed.detach()}}else if(msg["leaving"]!==undefined&&msg["leaving"]!==null){isInRoom=false;var leaving=msg["leaving"];var remoteFeed=null;var feeds=[];var remoteName=remoteNameList[leaving];console.log("room--leaving--",leaving,roomDomIds[leaving],...roomDomFree);roomDomFree.unshift(roomDomIds[leaving]);delete roomDomIds[leaving];delete roomDomIds[remoteNameList[leaving]];if(share_manager.getIsWxs()){var videoId=globalVariable.domIdMapWithUser[remoteName];var video=document.getElementById(videoId);if(video){video.muted=true}}for(var i=0;i<8;i++){if(feeds[i]!=null&&feeds[i]!=undefined&&feeds[i].rfid==leaving){remoteFeed=feeds[i];break}}if(remoteFeed!=null){feeds[remoteFeed.rfindex]=null;remoteFeed.detach()}}else if(msg["kicked"]!=undefined&&msg["kicked"]!=null){var kickedUser=msg["kicked"];roomDomFree.unshift(roomDomIds[kickedUser]);console.log("room--kicked--",kickedUser,roomDomIds[kickedUser],...roomDomFree);delete roomDomIds[kickedUser];delete roomDomIds[remoteNameList[kickedUser]]}else if(event=="destroyed"){clearInterval(PartyListInterval);isInRoom=false;roomDomIds.forEach(value=>{document.getElementById(value).src="";document.getElementById(value).srcObject=null});roomDomIds=[]}else if(event=="participants"){if(share_manager.getIsWxs()){globalVariable.roomCreater=msg["creater_name"];globalVariable.roomParticipants[msg.room+""]={};globalVariable.roomParticipantsIdByDisplayName[msg.room+""]={};for(var i=0;i<msg.participants.length;i++){globalVariable.roomParticipants[msg.room+""][msg.participants[i].id]=msg.participants[i];globalVariable.roomParticipantsIdByDisplayName[msg.room+""][msg.participants[i].display]=[msg.participants[i].id]}}else{roomParticipants[msg.room+""]={};for(var i=0;i<msg.participants.length;i++){roomParticipants[msg.room+""][msg.participants[i].id]=msg.participants[i]}}}if(msg["publishers"]!=undefined&&msg["publishers"]!==null){var list=msg["publishers"];for(var f in list){var id=list[f]["id"];var display=list[f]["display"];var audio=list[f]["audio_codec"];var video=list[f]["video_codec"];newRemoteFeed(id,display,audio,video,remoteStreamCallback)}}messageCallback(msg)}if(jsep!==undefined&&jsep!==null){videoRoomCall.handleRemoteJsep({jsep:jsep});var audio=msg["audio_codec"];var video=msg["video_codec"]}},onlocalstream:function(stream){if(localVideoID){Venus.attachMediaStream($("#"+localVideoID).get(0),stream)}},oncleanup:function(){Venus.log(" ::: Got a cleanup notification: we are unpublished now :::")},destroyed:function(){}})}let ownLeave=false;let joinedRoom=false;function registVideoRoom(LocaStramID,callback){exitRoom();ownLeave=false;let createdRoom=false;localVideoId=LocaStramID;localStream=LocaStramID;var videoObjc=document.getElementById("room_local_video");var remoteDiv=document.getElementById("room_local_video_view");remoteDiv.style.display="none";videoObjc.style.display="none";Roomvenus=new Venus({server:venusUrl,success:function(){Roomvenus.attach({plugin:"venus.plugin.videoroom",opaqueId:opaqueId,success:function(pluginHandle){videoRoomCall=pluginHandle;$(document).trigger("videoRoomSuccess")},error:function(error){Venus.error("  -- Error attaching plugin...",error);exitRoom();if(Roomvenus){Roomvenus=null}},consentDialog:function(on){if(on){}},mediaState:function(medium,on){Venus.log("Venus "+(on?"started":"stopped")+" receiving our "+medium);if(medium=="video"&&on==false){}},webrtcState:function(on){Venus.log("Venus says our WebRTC PeerConnection is "+(on?"up":"down")+" now");if(!on)return false},onmessage:function(msg,jsep){Venus.debug(" ::: Got a message (publisher) :::");Venus.debug(msg);var event=msg["videoroom"];console.log("roomMsg",msg);if(event=="created"){createdRoom=true}if(event!=undefined&&event!=null){let resultRecord=msg["videoroom"];let resultStatus=msg["endpoint_name"];if((resultStatus!=null||resultStatus!=undefined)&&resultRecord==="rtp_forward"){msg["type"]=liverectype;callback(msg)}else{callback(msg)}if(event=="participants"&&joinedRoom){roomParticipants[msg.room+""]={};for(var i=0;i<msg.participants.length;i++){roomParticipants[msg.room+""][msg.participants[i].id]=msg.participants[i]}checkRoomTotalPublisher(msg,2)}else if(event=="success"){PartyList(RoomNumber);getRoomMaxPublisher(msg,RoomNumber)}else if(event==="joined"){joinedRoom=true;RoomList();myid=msg["id"];mypvtid=msg["private_id"];if(msg["publishers"]!=undefined&&msg["publishers"]!==null){var list=msg["publishers"];creater_id=msg["creater_id"];for(var f in list){var id=list[f]["id"];var display=list[f]["display"];var audio=list[f]["audio_codec"];var video=list[f]["video_codec"];if(display!=myusername){newRemoteFeed(id,display,audio,video)}else{console.log(id)}}if(list.lenght>0){share_manager.setLocalPublish(list.lenght)}}}else if(event==="destroyed"){joinedRoom=false;callback("destroyed")}else if(event==="event"){if(msg["publishers"]!==undefined&&msg["publishers"]!==null){creater_id=msg["creater_id"];var list=msg["publishers"];for(var f in list){var id=list[f]["id"];var display=list[f]["display"];var audio=list[f]["audio_codec"];var video=list[f]["video_codec"];if(display!=myusername){newRemoteFeed(id,display,audio,video)}}}else if(msg["leaving"]!==undefined&&msg["leaving"]!==null){var leaving=msg["leaving"];var leaveNum=msg["leaving"];joinedRoom=false;if(remoteList.hasOwnProperty(leaveNum)){Object.keys(remoteList).map((key,index)=>{var remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());var videoObjc=document.getElementById("room_remote_stream_"+index.toString());remoteDiv.style.display="none";videoObjc.style.display="none"});RoomLeav=true;console.log("room--leaving--",leaveNum,roomDomIds[leaveNum],...roomDomFree);roomDomFree.unshift(roomDomIds[leaveNum]);delete roomDomIds[leaveNum];delete roomDomIds[remoteNameList[leaveNum]];delete remoteList[leaveNum];delete RoomhandleRemote[leaveNum];delete remoteNameList[leaveNum];let remoteView=document.getElementById("room_remote_view");if(Object.keys(remoteList).length<=3){remoteView.style.width="740px"}else{remoteView.style.width="1090px"}share_manager.setLocalPublish(Object.keys(remoteList).length);reloadRemoteStream()}if(leaving=="ok"){var videoObjc=document.getElementById("room_local_video");var remoteDiv=document.getElementById("room_local_video_view");videoObjc.style.display="none";remoteDiv.style.display="none";videoRoomCall.hangup();$(document).trigger("ownKickedOut");return}var remoteFeed=null;for(var i=0;i<8;i++){if(feeds[i]!=null&&feeds[i]!=undefined&&feeds[i].rfid==leaving){remoteFeed=feeds[i];break}}if(remoteFeed!=null){feeds[remoteFeed.rfindex]=null;remoteFeed.detach()}}else if(msg["unpublished"]!==undefined&&msg["unpublished"]!==null){var unpublished=msg["unpublished"];var roomUnpublished=msg["unpublished"];if(remoteList.hasOwnProperty(roomUnpublished)){Object.keys(remoteList).map((key,index)=>{var remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());var videoObjc=document.getElementById("room_remote_stream_"+index.toString());remoteDiv.style.display="none";videoObjc.style.display="none"});delete remoteList[roomUnpublished];delete RoomhandleRemote[roomUnpublished];delete remoteNameList[roomUnpublished];let remoteView=document.getElementById("room_remote_view");if(Object.keys(remoteList).length<=3){remoteView.style.width="740px"}else{remoteView.style.width="1090px"}share_manager.setLocalPublish(Object.keys(remoteList).length);reloadRemoteStream()}if(unpublished=="ok"){var videoObjc=document.getElementById("room_local_video");var remoteDiv=document.getElementById("room_local_video_view");videoObjc.style.display="none";remoteDiv.style.display="none";videoRoomCall.hangup();return}var remoteFeed=null;for(var i=0;i<8;i++){if(feeds[i]!=null&&feeds[i]!=undefined&&feeds[i].rfid==unpublished){remoteFeed=feeds[i];break}}if(remoteFeed!=null){feeds[remoteFeed.rfindex]=null;remoteFeed.detach()}}else if(msg["error"]!==undefined&&msg["error"]!==null){}else if(msg["kicked"]!=undefined&&msg["kicked"]!=null){joinedRoom=false;var kickedUser=msg["kicked"];if(remoteList.hasOwnProperty(kickedUser)){Object.keys(remoteList).map((key,index)=>{var remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());var videoObjc=document.getElementById("room_remote_stream_"+index.toString());remoteDiv.style.display="none";videoObjc.style.display="none"});roomDomFree.unshift(roomDomIds[kickedUser]);console.log("room--kicked--",kickedUser,roomDomIds[kickedUser],...roomDomFree);delete roomDomIds[kickedUser];delete roomDomIds[remoteNameList[kickedUser]];delete remoteList[kickedUser];delete RoomhandleRemote[kickedUser];delete remoteNameList[kickedUser];let remoteView=document.getElementById("room_remote_view");if(Object.keys(remoteList).length<=3){remoteView.style.width="740px"}else{remoteView.style.width="1090px"}share_manager.setLocalPublish(Object.keys(remoteList).length)}reloadRemoteStream()}}else{}}if(jsep!==undefined&&jsep!==null){if(videoRoomCall){videoRoomCall.handleRemoteJsep({jsep:jsep})}var audio=msg["audio_codec"];var video=msg["video_codec"]}},onlocalstream:function(stream){console.log("regist video room: on local stream");var videoTrack=stream.getVideoTracks();var room_local_audio_img=document.getElementById("room_local_audio");if(videoTrack.length>0){room_local_audio_img.style.display="none"}else{room_local_audio_img.style.display="block"}createRoomLocalStream(stream,true);let congig=videoRoomCall.webrtcStuff},onremotestream:function(stream){},oncleanup:function(){Venus.log(" ::: Got a cleanup notification: we are unpublished now :::")},destroyed:function(){}})}})}function reloadRemoteStream(){Object.keys(remoteList).map((key,index)=>{if(index>8){return}share_manager.setLocalPublish(Object.keys(remoteList).length);let noteSteam=remoteList[key];var videoObjc=document.getElementById("room_remote_stream_"+index.toString());var remoteObjct=document.getElementById("room_remote_user_name_"+index.toString());var remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());var RemoteName=remoteNameList[key];remoteObjct.innerText=RemoteName;videoObjc.style.display="block";remoteDiv.style.display="block";videoObjc.muted=false;var videoTracks;if(noteSteam){videoTracks=noteSteam.getVideoTracks()}var remote_audio_img=document.getElementById("room_remote_audio_img_"+index.toString());if(videoTracks){if(videoTracks.length>0){remote_audio_img.style.display="none"}else{remote_audio_img.style.display="block"}}Venus.attachMediaStream($("#"+"room_remote_stream_"+index.toString()).get(0),noteSteam);if(RoomisMutAudio){$(document).trigger("RoomMutAudio",RoomisMutAudio)}})}Object.defineProperties(remoteList,{name:{configurable:true,set:function(newValue){name=newValue}}});function videoRoomInviteUsers(roomNumber,users){if(VideoCall){var message={request:"invite_users",room:roomNumber,users:users};VideoCall.send({message:message});Venus.log("invite_users:\n",message);users.forEach(function(value){if(value.dom_id){roomDomIds[disposeSipCallName(value.username)]=value.dom_id}})}else{alert("需要登录videoCall,后才能使用邀请人")}}function disposeSipCallName(data){if(data&&typeof data=="string"&&data.match(/:(\S*)@/)){return data.match(/:(\S*)@/)[1]}if(data&&typeof data=="string"&&data.match(/(\S*)@/)){return data.match(/(\S*)@/)[1]}return data}var has_change_role=false;function checkRoomTotalPublisher(msg,mediaType){var participantsArray=msg.participants;var publisherCount=0;Object.keys(participantsArray).map(function(key,index){if(participantsArray[key].publisher==true){publisherCount++}});if(publisherCount<maxPublisher&&!has_change_role&&maxPublisher>0){has_change_role=true;var media_type;if(share_manager.getIsWxs()){media_type=globalVariable.call.avType}else if(share_manager.getIsTestRoom()){media_type=parseInt($("#set_test_room_type option:selected").val())}else{media_type=mediaType}publishOwnFeed(true,media_type===2?true:false)}}function getRoomMaxPublisher(msg,roomNumber){let array=msg["list"];if(array!==null&&array!==undefined){if(array.length>0)for(let i=0;i<array.length;i++){let ID=array[i]["room"];if(ID==roomNumber){maxPublisher=array[i]["max_publishers"];console.log("maxPublisher---",maxPublisher);let num_participants=array[i]["num_participants"];console.log("num_participants---",num_participants)}}}}function createRoomLocalStream(stream,isShow){var videoObjc=document.getElementById("room_local_video");var remoteDiv=document.getElementById("room_local_video_view");if(isShow){LocalStreamUrl=stream;if(ClickLocalBig){ClickVideoAction("room_big_stream_video",LocalStreamUrl)}videoObjc.style.display="block";remoteDiv.style.display="block";if(videoObjc){Venus.attachMediaStream($("#room_local_video").get(0),stream)}}else{videoObjc.style.display="none";remoteDiv.style.display="none";RoommutVideo(true,true)}}let remoteDiv0=document.getElementById("room_local_video_view");let divClick=document.getElementById("room_big_video_view");let bigVideoName=document.getElementById("room_big_video_user_name");let LocalStreamUrl=null;let ClickLocalBig=false;if(remoteDiv0!=undefined||remoteDiv0!=null){remoteDiv0.ondblclick=function(){bigVideoName.innerText=name;ClickLocalBig=true;$("#room_remote_view").hide();ClickVideoAction("room_big_stream_video",LocalStreamUrl);if(isScreen){let videoCallStream=document.getElementById("room_big_stream_video");videoCallStream.style.transform="rotateY(0deg)"}else{let videoCallStream=document.getElementById("room_big_stream_video");videoCallStream.style.transform="rotateY(180deg)"}}}$(document).on("BigViewDismiss",function(ev,Status){ClickLocalBig=false});$(document).on("RoomMutAudio",function(ev,code){RoomisMutAudio=code;if(code==0){Object.keys(remoteList).map((key,index)=>{var videoObjc=document.getElementById("room_remote_stream_"+index.toString());videoObjc.muted=false})}else{Object.keys(remoteList).map((key,index)=>{var videoObjc=document.getElementById("room_remote_stream_"+index.toString());videoObjc.muted=true})}});function ClickVideoDisplay(number){let dictkey=Object.keys(remoteList)[number];let nameKey=Object.keys(remoteNameList)[number];bigVideoName.innerText=remoteNameList[nameKey];let noteSteam=remoteList[dictkey];ClickLocalBig=false;ClickVideoAction("room_big_stream_video",noteSteam);let videoCallStream=document.getElementById("room_big_stream_video");videoCallStream.style.transform="rotateY(0deg)"}function ClickVideoAction(ID,Stram){if(divClick){divClick.style.display="block"}Venus.attachMediaStream($("#"+ID).get(0),Stram)}if(divClick!=undefined||divClick!=null){divClick.ondblclick=function(e){$(document).trigger("ShowBigVideo")}}function outLine(isLogin){sipRegister=isLogin;registered=isLogin}function checkRoom(roomID){first_request=0;has_change_role=false;var checkRoom={request:"exists",room:parseInt(roomID)};videoRoomCall.send({message:checkRoom})}function createRoom(roomID,maxPublisher){RoomNumber=roomID;creater_id=null;if(parseInt(maxPublisher)==0||maxPublisher==undefined||maxPublisher==null){maxPublisher=9}var CreatRoom={};var bitrate=parseInt(localStorage.getItem("frameBitrate"));if(isNaN(bitrate)||bitrate<0){bitrate=1024}var videoCodec="vp8,vp9,h264";var audioCodec="opus,g722,pcma,pcmu";if(localStorage.getItem("defaultCodec")==="0"){videoCodec=localStorage.getItem("videoCodec");audioCodec=localStorage.getItem("audioCodec");audioCodec=audioCodec.replace("alaw","pcma");audioCodec=audioCodec.replace("mulaw","pcmu")}if(share_manager.getIsTestRoom()&&parseInt($("#set_test_mode_type option:selected").val())){CreatRoom={request:"create",publishers:parseInt(maxPublisher),room:parseInt(roomID),is_private:false,permanent:false,opus_fec:true,media_type:parseInt($("#set_test_room_type option:selected").val()),test_mode:parseInt($("#set_test_mode_type option:selected").val()),test_room_count:parseInt($("#test_mode_create_room_count").val()),test_users_per_room:parseInt($("#test_mode_room_publisher_num").val()),test_target_gw_prefix:$("#test_mode_gateway_prefix").val(),test_start_room_id:parseInt($("#test_mode_room_start_no").val())};if(!share_manager.getUserDefaultUSer()){CreatRoom["test_users"]=share_manager.getTestUsers()}else{CreatRoom["test_user_count"]=parseInt($("#test_mode_default_user_num").val())}}else{CreatRoom={request:"create",publishers:parseInt(maxPublisher),room:parseInt(roomID),is_private:false,permanent:false,opus_fec:true,audiocodec:audioCodec,videocodec:videoCodec,call_reqinfo:_call_reqinfo}}if(bitrate>0){CreatRoom["bitrate"]=bitrate*1e3;CreatRoom["bitrate_cap"]=true}Venus.log("createRoom:\n"+JSON.stringify(CreatRoom));videoRoomCall.send({message:CreatRoom})}function joinRoom(room,userName){RoomNumber=room;var bitrate=parseInt(localStorage.getItem("frameBitrate"));if(isNaN(bitrate)||bitrate<0){bitrate=1024}var register={request:"join",room:parseInt(room),ptype:"publisher",display:userName,opus_fec:true,factory_id:_factory_id,call_reqinfo:_call_reqinfo};console.log("venus_manager joinroom register:",register);if(bitrate>0){register["bitrate"]=bitrate*1e3;register["bitrate_cap"]=true}myusername=userName;videoRoomCall.send({message:register})}function destroyRoom(roomID){var dict={request:"destroy",room:parseInt(roomID),permanent:false,secret:""};videoRoomCall.send({message:dict})}function RoomList(){var roomList={request:"list"};if(videoRoomCall){videoRoomCall.send({message:roomList})}}function detachRoom(){ownLeave=true;if(Roomvenus){Roomvenus.destroy();Roomvenus=null;videoRoomCall.roomDestroyHandle();videoRoomCall=null}}let PartyListInterval;let first_request=0;function PartyList(RoomID){clearInterval(PartyListInterval);if(first_request==0){first_request=1;var list={request:"listparticipants",room:parseInt(RoomID)};if(videoRoomCall){videoRoomCall.send({message:list})}}PartyListInterval=setInterval(()=>{var list={request:"listparticipants",room:parseInt(RoomID)};if(videoRoomCall){videoRoomCall.send({message:list})}if(share_manager.getIsWxs()&&globalVariable.call.callType!==1){clearInterval(PartyListInterval)}},3e3)}function kickRoomUserWithID(secret,room,userID){var dict={request:"kick",secret:secret,room:parseInt(room),id:parseInt(userID)};videoRoomCall.send({message:dict})}function kickRoomUserWithName(secret,room,userName){var participants=roomParticipants[room+""];if(participants){Object.keys(participants).forEach(function(id){if(participants[id].display&&participants[id].display==userName){var dict={request:"kick",secret:secret,room:parseInt(room),id:participants[id].id};videoRoomCall.send({message:dict});console.log("room kick: "+JSON.stringify(dict))}})}}function getMyIDAndCreateID(){return{my_id:myid,creat_id:creater_id}}function leaveRoom(){var leaveroom={request:"leave"};if(videoRoomCall){videoRoomCall.send({message:leaveroom});videoRoomCall.hangup()}clearInterval(PartyListInterval)}function exitRoom(){has_change_role=false;first_request=0;joinedRoom=false;getAudioInfoTimerLocal(true);clearInterval(PartyListInterval);if(videoRoomCall){$(document).trigger("videoRoomExit");$(document).trigger("RoomHangup");this.CloseLocalVideoExit();if(!share_manager.getIsWxs()&&!share_manager.getIsSDK()){for(i=0;i<8;i++){var videoObjc=document.getElementById("room_remote_stream_"+i.toString());var remoteDiv=document.getElementById("room_remote_video_view_"+i.toString());videoObjc.style.display="none";remoteDiv.style.display="none"}}}if(Object.keys(remoteList).length>0){if(!share_manager.getIsWxs()&&!share_manager.getIsSDK()){Object.keys(remoteList).map((key,index)=>{var videoObjc=document.getElementById("room_remote_stream_"+index.toString());var remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());videoObjc.style.display="none";remoteDiv.style.display="none";delete remoteList[key];delete RoomhandleRemote[key];delete remoteNameList[key]})}}if(divClick){divClick.style.display="none"}remoteList={};RoomhandleRemote={};remoteNameList={};RoomLeav=false;if(videoRoomCall){let dict=getMyIDAndCreateID();if(dict["my_id"]==dict["creat_id"]||dict["creat_id"]==null){myid=null;creater_id=null;destroyRoom(RoomNumber)}invite_media_type=2;detachRoom()}}function publish(){var objc={request:"unpublish"}}function ampleskyExitRoom(){has_change_role=false;first_request=0;getAudioInfoTimerLocal(true);clearInterval(PartyListInterval);this.CloseLocalVideoExit();isInRoom=false;if(videoRoomCall){let dict=getMyIDAndCreateID();if(dict["my_id"]==dict["creat_id"]||dict["creat_id"]==null){myid=null;creater_id=null;destroyRoom(RoomNumber)}invite_media_type=2;detachRoom();roomDomIds.forEach(value=>{document.getElementById(value).src="";document.getElementById(value).srcObject=null});roomDomIds=[]}}function getCurrentjsp(){return currentJsep}function publishOwnFeed(useAudio,media_type){videoRoomCall.createOffer({media:cameraVideo?{video:"screen",captureDesktopAudio:hasMic,audioRecv:true,videoRecv:media_type,audioSend:hasMic?useAudio:false,videoSend:media_type}:{audioRecv:true,videoRecv:media_type,audioSend:hasMic?useAudio:false,videoSend:hasCamer?media_type:false},simulcast:false,success:function(jsep){var publish={request:"configure",audio:useAudio,video:media_type};console.log("publish own feed: "+JSON.stringify(publish));jsep=change_ice_sdp(jsep);let dict=venus_manager.getMyIDAndCreateID();if(dict["my_id"]==dict["creat_id"]||dict["creat_id"]==null){jsep=changeSDP(jsep)}if(videoRoomCall){videoRoomCall.send({message:publish,jsep:jsep})}},error:function(error){Venus.error("WebRTC error:",error)}})}function publishownFeedCustom(useAudio,videoSend){videoRoomCall.createOffer({media:cameraVideo?{video:"screen",captureDesktopAudio:hasMic,audioRecv:true,videoRecv:false,audioSend:hasMic?useAudio:false,videoSend:videoSend}:{audioRecv:true,videoRecv:true,audioSend:hasMic?useAudio:false,videoSend:hasCamer?videoSend:false},simulcast:false,success:function(jsep){var publish={request:"configure",audio:useAudio,video:videoSend};videoRoomCall.send({message:publish,jsep:jsep})},error:function(error){Venus.error("WebRTC error:",error);if(useAudio){publishownFeedCustom(false)}else{}}})}function unpublishOwnFeed(){var unpublish={request:"unpublish"};videoRoomCall.send({message:unpublish})}function ampleskyNewRemoteFeed(id,display,ownName,video,backRemoteFeed,callback){let remoteFeed=null;Roomvenus.attach({plugin:"venus.plugin.videoroom",opaqueId:opaqueId,success:function(pluginHandle){remoteFeed=pluginHandle;remoteFeed.simulcastStarted=false;var subscribe={request:"join",room:parseInt(RoomNumber),ptype:"listener",feed:id};remoteFeed.videoCodec=video;remoteFeed.send({message:subscribe})},error:function(error){},onmessage:function(msg,jsep){console.log(msg);var event=msg["videoroom"];if(msg["error"]!==undefined&&msg["error"]!==null){}else if(event!=undefined&&event!=null){if(event==="attached"){var feeds=[];for(var i=0;i<8;i++){if(feeds[i]===undefined||feeds[i]===null){feeds[i]=remoteFeed;remoteFeed.rfindex=i;break}}remoteFeed.rfid=msg["id"];remoteFeed.rfdisplay=msg["display"];if(remoteFeed.spinner===undefined||remoteFeed.spinner===null){}else{remoteFeed.spinner.spin()}}if(event==="event"){var substream=msg["substream"];var temporal=msg["temporal"];if(substream!==null&&substream!==undefined||temporal!==null&&temporal!==undefined){if(!remoteFeed.simulcastStarted){remoteFeed.simulcastStarted=true}}}else{}}if(jsep!==undefined&&jsep!==null){remoteFeed.createAnswer({jsep:jsep,media:{audioSend:false,videoSend:false},success:function(jsep){var body={request:"start",room:parseInt(RoomNumber)};remoteFeed.send({message:body,jsep:jsep})},error:function(error){}})}},webrtcState:function(on){Venus.log("Venus says this WebRTC PeerConnection (feed #"+remoteFeed.rfindex+") is "+(on?"up":"down")+" now")},onlocalstream:function(stream){},onremotestream:function(stream){if(display==ownName){return}backRemoteFeed(remoteFeed);callback([id,stream])},oncleanup:function(){}})}function newRemoteFeed(id,display,audio,video,callback){let remoteFeed=null;Roomvenus.attach({plugin:"venus.plugin.videoroom",opaqueId:opaqueId,success:function(pluginHandle){remoteFeed=pluginHandle;remoteFeed.simulcastStarted=false;var subscribe={request:"join",room:parseInt(RoomNumber),ptype:"listener",feed:id};remoteFeed.videoCodec=video;remoteFeed.send({message:subscribe})},error:function(error){},onmessage:function(msg,jsep){console.log(msg);var event=msg["videoroom"];if(msg["error"]!==undefined&&msg["error"]!==null){}else if(event!=undefined&&event!=null){if(event==="attached"){for(var i=0;i<8;i++){if(feeds[i]===undefined||feeds[i]===null){feeds[i]=remoteFeed;remoteFeed.rfindex=i;break}}remoteFeed.rfid=msg["id"];remoteFeed.rfdisplay=msg["display"];if(remoteFeed.spinner===undefined||remoteFeed.spinner===null){}else{remoteFeed.spinner.spin()}}if(event==="event"){var substream=msg["substream"];var temporal=msg["temporal"];if(substream!==null&&substream!==undefined||temporal!==null&&temporal!==undefined){if(!remoteFeed.simulcastStarted){remoteFeed.simulcastStarted=true}}}else{}}if(jsep!==undefined&&jsep!==null){jsep=change_ice_sdp(jsep);remoteFeed.createAnswer({jsep:jsep,media:{audioSend:false,videoSend:false},success:function(jsep){var body={request:"start",room:parseInt(RoomNumber)};jsep=change_ice_sdp(jsep);remoteFeed.send({message:body,jsep:jsep})},error:function(error){}})}},webrtcState:function(on){Venus.log("Venus says this WebRTC PeerConnection (feed #"+remoteFeed.rfindex+") is "+(on?"up":"down")+" now")},onlocalstream:function(stream){},onremotestream:function(stream){console.log("stream",stream);if(callback){callback(id,stream,display)}if(display==myusername){return}for(var i=0;i<8;i++){if(!share_manager.getIsWxs()&&!share_manager.getIsSDK()){var videoObjc=document.getElementById("room_remote_stream_"+i);var remoteDiv=document.getElementById("room_remote_video_view_"+i);videoObjc.style.display="none";remoteDiv.style.display="none"}}remoteList[id]=stream;RoomhandleRemote[id]=remoteFeed;remoteNameList[id]=display;if(Object.keys(remoteList).length!==0){let remoteView=document.getElementById("room_remote_view");if(remoteView){if(Object.keys(remoteList).length<=3){remoteView.style.width="740px"}else{remoteView.style.width="1090px"}}Object.keys(remoteList).map((key,index)=>{if(index>8){return}share_manager.setLocalPublish(Object.keys(remoteList).length);let noteSteam=remoteList[key];let videoObjc;let remoteObjct;let remoteDiv;let remote_audio_img;var RemoteName=remoteNameList[key];let videoId;if(share_manager.getIsWxs()){videoId=globalVariable.domIdMapWithUser[RemoteName];globalVariable.domIdMapWithUser[id]=videoId;if(globalVariable.call.isCallee&&globalVariable.roomCreater==RemoteName){videoId=remoteStreamVideo}console.log("标签--room--播放",display,videoId,globalVariable.domIdSets)}else if(share_manager.getIsSDK()){if(RemoteName==display){videoId=roomDomIds[RemoteName];if(videoId){roomDomIds[id]=videoId}else{console.log("room--空闲id--",...roomDomFree);const freeId=roomDomFree.shift();console.log("标签--room--找空闲video--",freeId);videoId=freeId;roomDomIds[RemoteName]=freeId;roomDomIds[id]=freeId}console.log("标签--room--播放",display,videoId,RemoteName)}}else{videoId="room_remote_stream_"+index.toString();videoObjc=document.getElementById("room_remote_stream_"+index.toString());remoteObjct=document.getElementById("room_remote_user_name_"+index.toString());remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());remoteObjct.innerText=RemoteName;videoObjc.style.display="block";remoteDiv.style.display="block";remote_audio_img=document.getElementById("room_remote_audio_img_"+index.toString());videoObjc.muted=false}var videoTracks;if(noteSteam){videoTracks=noteSteam.getVideoTracks()}if(videoTracks&&!share_manager.getIsWxs()&&!share_manager.getIsSDK()){if(videoTracks.length>0){remote_audio_img.style.display="none"}else{remote_audio_img.style.display="block"}}if(videoId&&$("#"+videoId)){Venus.attachMediaStream($("#"+videoId).get(0),noteSteam)}if(RoomisMutAudio){$(document).trigger("RoomMutAudio",RoomisMutAudio)}})}},oncleanup:function(){}})}let intervalAudio;let timeTag=0;let timeTag2=0;let defaultFrameRate=true;let isOpenVideo=true;let isCloseRemoteVideo=false;let isSelected=false;let changeRtt=false;function getAudioInfoTimerLocal(close,googRtt){if(videoRoomCall==undefined||videoRoomCall==null){return}isOpenVideo=videoRoomCall.isVideoMuted();if(!share_manager.getAutoChanageVideo()){return}if(close){clearInterval(callMessageTimer);return}if(changeRtt){googRtt="101"}if(isCloseRemoteVideo){isOpenVideo=true}googRtt=parseInt(googRtt);if(share_manager.getAutoChanageVideo()){if(googRtt>parseInt(share_manager.getCloseVideoNumber())){timeTag++;if(timeTag>=parseInt(share_manager.getReduceVideo())){timeTag=0;if(!isCloseRemoteVideo){isCloseRemoteVideo=true;if(!isOpenVideo){RoommutVideo(true,false)}console.log("googRtt视频关闭");let closeVideo={request:"media_stream_reset",recv_video:false};for(var f in RoomhandleRemote){var remotefeedListElement=RoomhandleRemote[f];remotefeedListElement.send({message:closeVideo})}}}}else if(googRtt>parseInt(share_manager.getReduceFrameRate())){if(!isScreen){if(!isOpenVideo&&defaultFrameRate){timeTag2++;if(timeTag2>parseInt(share_manager.getReduceVideo())){timeTag2=0;defaultFrameRate=false;videoRoomCall.changeLocalVideoFrameRate(640,480,15);console.log("googRtt视频分辨率降低")}}else{timeTag2=0}}}else if(isCloseRemoteVideo&&googRtt<parseInt(share_manager.getOpenVideo())){if(!isOpenVideo){timeTag=0}else{timeTag++;if(timeTag>=parseInt(share_manager.getImproveVideo())){timeTag++;if(timeTag>=share_manager.getImproveVideo()){timeTag=0;defaultFrameRate=false;if(!ownUerMuteVideo){RoommutVideo(false,false)}isCloseRemoteVideo=false;console.log("googRtt视频开启");let openVideo={request:"media_stream_reset",recv_video:true};for(var f in RoomhandleRemote){var remotefeedListElement=RoomhandleRemote[f];remotefeedListElement.send({message:openVideo})}}}}}else if(googRtt<parseInt(share_manager.getImproveFrameRate())){if(!screen){if(!isOpenVideo&&!defaultFrameRate){timeTag2++;if(timeTag2>=parseInt(share_manager.getImproveVideo())){timeTag2=0;switch(parseInt(localStorage.getItem("resolution"))){case 0:videoRoomCall.changeLocalVideoFrameRate(1280,720,share_manager.getVideoFrameRate());break;case 1:videoRoomCall.changeLocalVideoFrameRate(640,480,share_manager.getVideoFrameRate());break;case 2:videoRoomCall.changeLocalVideoFrameRate(3840,2160,share_manager.getVideoFrameRate());break;case 3:videoRoomCall.changeLocalVideoFrameRate(1920,1080,share_manager.getVideoFrameRate());break;case 4:videoRoomCall.changeLocalVideoFrameRate(320,240,share_manager.getVideoFrameRate());break}defaultFrameRate=true;console.log("googRtt视频分辨率恢复")}}else{timeTag2=0}}}}}$(document).on("RoomVideoMute",function(ev){if(Object.keys(remoteList).length>0){Object.keys(remoteList).map((key,index)=>{var videoObjc=document.getElementById("room_remote_stream_"+index.toString());var remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());videoObjc.style.display="none";remoteDiv.style.display="none"})}if(Object.keys(remoteList).length!==0){let remoteView=document.getElementById("room_remote_view");if(Object.keys(remoteList).length<=3){remoteView.style.width="740px"}else{remoteView.style.width="1090px"}Object.keys(remoteList).map((key,index)=>{if(index>7){return}let noteSteam=remoteList[key];var videoTracks;if(noteSteam){videoTracks=noteSteam.getVideoTracks()}var videoObjc=document.getElementById("room_remote_stream_"+index.toString());var remoteObjct=document.getElementById("room_remote_user_name_"+index.toString());var remoteDiv=document.getElementById("room_remote_video_view_"+index.toString());var RemoteName=remoteNameList[key];remoteObjct.innerText=RemoteName;videoObjc.style.display="block";remoteDiv.style.display="block";videoObjc.muted=false;var remote_audio_img=document.getElementById("room_remote_audio_img_"+index.toString());if(videoTracks){if(videoTracks.length>0){remote_audio_img.style.display="none"}else{remote_audio_img.style.display="block"}}Venus.attachMediaStream($("#"+"room_remote_stream_"+index.toString()).get(0),noteSteam)})}});function getRoomRemoteListName(){return remoteNameList}function mut(){var muted=videoRoomCall.isAudioMuted();if(videoRoomCall){if(muted){videoRoomCall.unmuteAudio()}else{videoRoomCall.muteAudio()}}}function roomMuteSpeaker(mute){if(videoRoomCall){if(mute){videoRoomCall.unmuteAudio()}else{videoRoomCall.muteAudio()}}}function RoommutVideo(mut,usermute){ownUerMuteVideo=usermute;if(mut){videoRoomCall.muteVideo()}else{let length=Object.keys(remoteList).length;if(length>0){if(length<maxPublisher){}}else{}ownUerMuteVideo=false;videoRoomCall.unmuteVideo()}$(document).trigger("RoomChangeVideo")}function getRoomhandle(){return videoRoomCall}function watchStreaming(RemoteVideoID,Server,iceServer,protol,name,pwd,callBack){venusUrl=Server;let ice=undefined;if(iceServer&&iceServer.indexOf("turn:")===0){ice=[{urls:iceServer+"?transport="+protol,username:name,credential:pwd,credentialType:"password"}]}else if(iceServer&&iceServer.indexOf("stun:")===0){ice=[{urls:iceServer}]}watchLive(RemoteVideoID,ice,function(message){return callBack(message)})}function watchLive(RemoteVideoID,ice,callbackdata){remoteWatchVideo=RemoteVideoID;share_manager.setStreamingVideoID(RemoteVideoID);if(!Streaming){Venus.init({debug:"all",callback:function(){StreamVenus=new Venus({server:venusUrl,iceServers:ice,success:function(){(venus==undefined||venus==null?StreamVenus:venus).attach({plugin:"venus.plugin.streaming",opaqueId:opaqueId,success:function(pluginHandle){Streaming=pluginHandle;updateStreamsList(function callback(data){callbackdata(data)})},error:function(error){Venus.error("  -- Error attaching plugin... ",error);Streaming=null},onmessage:function(msg,jsep){console.log(msg);var result=msg["result"];var streaming=msg["streaming"];if(streaming!==null&&streaming!==undefined&&streaming=="info"){callbackdata(msg)}if(result!==null&&result!==undefined){callbackdata(result);if(result["status"]!==undefined&&result["status"]!==null){var status=result["status"];if(status==="starting"){$(document).trigger("starting")}else if(status==="started"){$(document).trigger("started")}else if(status==="stopped"){$(document).trigger("stopped");stopStream()}else if(result["status"]==0){$(document).trigger("stopped");stopStream()}}else if(msg["streaming"]==="event"){var substream=result["substream"];var temporal=result["temporal"];if(substream!==null&&substream!==undefined||temporal!==null&&temporal!==undefined){}var spatial=result["spatial_layer"];temporal=result["temporal_layer"]}}else if(msg["error"]!==undefined&&msg["error"]!==null){callbackdata(msg);if(msg["error_code"]==455){$(document).trigger("watchError",[msg["error"]])}else if(msg["error_code"]==457){$(document).trigger("showPassWordPage",[msg["error"]])}stopStream();return}if(jsep!==undefined&&jsep!==null){Streaming.createAnswer({jsep:jsep,media:{audioSend:false,audioRecv:true,videoSend:false,videoRecv:true},success:function(jsep){var body={request:"start"};Streaming.send({message:body,jsep:jsep})},error:function(error){Venus.error("WebRTC error:",error)}})}},onremotestream:function(stream){var addButtons=false;var videoTracks=stream.getVideoTracks();if(videoTracks){if(videoTracks.length>0){$(".streaming-audio-img").hide()}else{$(".streaming-audio-img").show()}}Venus.attachMediaStream($("#"+remoteWatchVideo).get(0),stream)},oncleanup:function(){}})}})}})}else{updateStreamsList(function callback(data){callbackdata(data)})}}function streamingGetInfo(roomID){var body={request:"info",id:parseInt(roomID),admin:"adminpwd"};Streaming.send({message:body})}function getCurrentDate(format){var now=new Date;var year=now.getFullYear();var month=now.getMonth();var date=now.getDate();var day=now.getDay();var hour=now.getHours();var minu=now.getMinutes();var sec=now.getSeconds();month=month+1;if(month<10)month="0"+month;if(date<10)date="0"+date;if(hour<10)hour="0"+hour;if(minu<10)minu="0"+minu;if(sec<10)sec="0"+sec;var time="";if(format==1){time=year+"-"+month+"-"+date}else if(format==2){time=year+"-"+month+"-"+date+" "+hour+":"+minu+":"+sec}return time}function updateStreamsList(callBack){var body={request:"list"};Streaming.send({message:body,success:function(result){if(result===null||result===undefined){return}if(result["list"]!==undefined&&result["list"]!==null){var list=result["list"];Venus.log("Got a list of available streams");Venus.debug(list);callBack(list)}}})}let watchID="";let passPWD="";function getWatchStreamInfo(ID,pass){watchID=ID;passPWD=pass;if(Streaming){var body={request:"watch",offer_audio:true,offer_video:true,pin:pass,id:parseInt(ID)};streamingGetInfo(ID)}else{watchLive("streaming_remote_video","",function callback(back){if(Array.isArray(back)){streamingGetInfo(ID)}})}Streamtype="live"}function getWatchStreamInfoWithName(name,pass){passPWD=pass;if(Streaming){var body={request:"watch",offer_audio:true,offer_video:true,pin:pass,name:name};getWatchStreamInfoWithName(name)}else{watchLive("streaming_remote_video","",function callback(back){if(Array.isArray(back)){getWatchStreamInfoWithName(name)}})}Streamtype="live"}let LiveCodetype="";function requestStreamingInfo(watchID){var body={request:"watch",offer_audio:true,offer_video:true,pin:passPWD,id:parseInt(watchID)};Streaming.send({message:body})}function startWatchLiveWithPassword(watchID,password){var body={request:"watch",offer_audio:true,offer_video:true,pin:password,id:parseInt(watchID)};Streaming.send({message:body})}function startLiveWithName(name,password){var body={request:"watch",offer_audio:true,offer_video:true,pin:password,name:name};Streaming.send({message:body})}function stopStream(type){if(type){Streamtype=type}if(Streamtype=="live"){var body={request:"stop"};Streaming.send({message:body});Streaming.hangup()}else{playRecordStop()}}$(document).on("screenSuccess",function(ev){isScreen=true});function preShareScreen(screenShare,isVideoCall,stateback){isScreen=screenShare;if(!isVideoCall){if(screenShare){VideoCall.VideoCallGetScreen(false,incoming,function(state){if(state==true){stateback(state);isScreen=true}else{stateback(state);isScreen=false;$(document).trigger("CloseRoomScreen")}});let videoCallStream=document.getElementById("room_big_stream_video");if(videoCallStream){videoCallStream.style.transform="rotateY(0deg)"}}else{VideoCall.VideoCallGetScreen(true,incoming,function(state){stateback(state)});let videoCallStream=document.getElementById("room_big_stream_video");if(videoCallStream){videoCallStream.style.transform="rotateY(180deg)"}}}else{if(screenShare){VideoCall.VideoCallGetScreen(false,incoming,function(state){if(state==true){stateback(state);isScreen=true}else{$(document).trigger("CloseVideoCallScreen");stateback(false);isScreen=false}})}else{VideoCall.VideoCallGetScreen(true,incoming,function(state){stateback(state);isScreen=false})}}}let isSDK=false;function initPlayRecordPrepare(remotePlayID,server,iceServer,protol,name,pwd,statusCallBack){venusUrl=server;let ice=undefined;if(iceServer&&iceServer.indexOf("turn:")===0){ice=[{urls:iceServer+"?transport="+protol,username:name,credential:pwd,credentialType:"password"}]}else if(iceServer&&iceServer.indexOf("stun:")===0){ice=[{urls:iceServer}]}isSDK=true;initPlayRecord(remotePlayID,ice,function back(status){return statusCallBack(status)})}function initPlayRecord(remotePlayID,ice,status){remoteWatchVideo=remotePlayID;Venus.init({debug:"all",callback:function(){PlayVenus=new Venus({server:venusUrl,iceServers:ice,success:function(){if(isSDK){currentvenus=PlayVenus}else{currentvenus=venus}currentvenus.attach({plugin:"venus.plugin.recordplay",opaqueId:opaqueId,success:function(pluginHandle){PlayCall=pluginHandle;updateRecsList(function(dataList){});status("Success")},error:function(error){status(error);Venus.error("  -- Error attaching plugin...",error)},webrtcState:function(on){Venus.log("Venus says our WebRTC PeerConnection is "+(on?"up":"down")+" now")},onmessage:function(msg,jsep){console.log(msg);var result=msg["result"];var recordplay=msg["recordplay"];if(recordplay!==null&&recordplay!==undefined&&recordplay=="update"){share_manager.setsecond(msg["duration"])}if(recordplay!==null&&recordplay!==undefined){if(result!==null&&result!==undefined){if(result["status"]!==null&&result["status"]!==undefined&&(result["status"]=="skiped"||result["status"]=="failed"||result["status"]=="pausing_skiped")){share_manager.setSeekTime(parseInt(result["position"]))}}}status(msg);if(result!==null&&result!==undefined){if(result["status"]!==undefined&&result["status"]!==null){var event=result["status"];if(event==="preparing"||event==="refreshing"){share_manager.setsecond(result["duration"]);PlayCall.createAnswer({jsep:jsep,media:{audioSend:false,videoSend:false},success:function(jsep){Venus.debug("Got SDP!");Venus.debug(jsep);var body={request:"start"};PlayCall.send({message:body,jsep:jsep})},error:function(error){Venus.error("WebRTC error:",error)}})}else if(event==="recording"){if(jsep!==null&&jsep!==undefined)PlayCall.handleRemoteJsep({jsep:jsep});var id=result["id"];if(id!==null&&id!==undefined){recordingId=id}}else if(event==="slow_link"){var uplink=result["uplink"];if(uplink!==0){bandwidth=parseInt(bandwidth/1.5);PlayCall.send({message:{request:"configure","video-bitrate-max":bandwidth,"video-keyframe-interval":15e3}})}}else if(event==="playing"){}else if(event==="stopped"){var id=result["id"];if(recordingId!==null&&recordingId!==undefined){if(recordingId!==id){Venus.warn("Not a stop to our recording?");return}}if(selectedRecording!==null&&selectedRecording!==undefined){if(selectedRecording!==id){Venus.warn("Not a stop to our playout?");return}}}else{}}}else if(msg["error"]!==undefined&&msg["error"]!==null){if(msg["error_code"]==455){$(document).trigger("watchError",[msg["error"]])}else if(msg["error_code"]==429||msg["error_code"]==425){$(document).trigger("showPassWordPage",[msg["error"]])}else if(msg["error_code"]==422){setTimeout(()=>{$(document).trigger("recordPlayError",[msg["error"]])},1e3)}else if(msg["error_code"]==417){setTimeout(()=>{$(document).trigger("recordPlayError417",[msg["error"]])},1e3)}}},onlocalstream:function(stream){},onremotestream:function(stream){var videoTracks=stream.getVideoTracks();if(videoTracks){if(videoTracks.length>0){$(".streaming-audio-img").hide()}else{$(".streaming-audio-img").show()}}Venus.attachMediaStream($("#"+remoteWatchVideo).get(0),stream)},oncleanup:function(){}})},error:function(error){status(error)},destroyed:function(){}})}})}function updateRecsList(dataList){var body={request:"list",timeStemp:getCurrentDate(2)};console.log("发送消息时间======="+getCurrentDate(2));PlayCall.send({message:body,success:function(result){if(result===null||result===undefined){return}if(result["list"]!==undefined&&result["list"]!==null){console.log("收到消息时间======="+getCurrentDate(2));var list=result["list"];console.log(result);list.sort(function(a,b){return a["date"]<b["date"]?1:b["date"]<a["date"]?-1:0});dataList(list);for(var mp in list){}}}})}function playRecordRequestURL(IDNumber,callBack){var body={request:"download",id:parseInt(IDNumber)};PlayCall.send({message:body,success:function(result){console.log(result);callBack(result)}})}function startRecording(recordName){PlayCall.send({message:{request:"configure","video-bitrate-max":bandwidth,"video-keyframe-interval":15e3}});PlayCall.createOffer({simulcast:doSimulcast,success:function(jsep){Venus.debug("Got SDP!");Venus.debug(jsep);var body={request:"record",name:recordName};PlayCall.send({message:body,jsep:jsep})},error:function(error){Venus.error("WebRTC error...",error);bootbox.alert("WebRTC error... "+error);PlayCall.hangup()}})}function startPlayout(PlayID,password){share_manager.setRecordPassword(password);var play={request:"play",id:parseInt(PlayID),pin:password};PlayCall.send({message:play});Streamtype="record"}function requestSkipDuration(value){var body={request:"skip",id:parseInt(share_manager.getRecordID()),position:value};PlayCall.send({message:body})}function requestRecordPlay(){var body={request:"play",id:parseInt(share_manager.getRecordID())};PlayCall.send({message:body})}function recordPause(){var body={request:"pause"};PlayCall.send({message:body})}function recordPlaying(){var body={request:"startfrompause"};PlayCall.send({message:body})}function searchReacord(startTime,endTime,keyWord){var body={request:"list",begin_time:startTime,end_time:endTime,key_word:keyWord};PlayCall.send({message:body})}function recordPreview(ID,position){var body={request:"preview",id:ID,position:position};PlayCall.send({message:body})}function playRecordStop(){if(PlayCall){var stop={request:"stop"};PlayCall.send({message:stop});PlayCall.hangup()}}function initHtmlTag(remote_video,remote_audio,local_video){localStream=local_video;remoteStreamAudio=remote_audio;remoteStreamVideo=remote_video}function getCallErroResult(errorBack){return errorBack(callErrorresult)}function CloseLocalVideoExit(){if(videoRoomCall){videoRoomCall.CloseScreenVideo();videoRoomCall.CloseLocalVideo()}else{if(VideoCall){VideoCall.CloseScreenVideo();VideoCall.CloseLocalVideo()}}}function getVideoCall(){return VideoCall.getPeerconnection()}function getCallHandle(){if(issipCall){return sipcall}else{return VideoCall}}function switchCameraSipInfo(channel,callNum){console.log("切换摄像头-------------------------");console.log("factory_id",globalVariable.local.factoryId);console.log("call_type",globalVariable.call.callType);var msg={request:"message",factory_id:globalVariable.local.factoryId,type:"text/plain",content:'{"msgType": "CAMERA_SWITCH_INFO"}'};if(globalVariable.call.callType){var participants=globalVariable.roomParticipants[channel+""];if(participants){Object.keys(participants).forEach(function(id){if(participants[id].display&&callNum.indexOf(participants[id].display)>=0){msg["room"]=channel;msg["feed"]=participants[id].id;console.log("video room send:",msg);videoRoomCall.send({message:msg})}})}}else{sipcall.send({message:msg})}}function switchMuteSipInfo(channel,callNum){console.log("静音切换-------------------------");console.log("factory_id",globalVariable.local.factoryId);console.log("call_type",globalVariable.call.callType);var msg={request:"message",factory_id:globalVariable.local.factoryId,type:"text/plain",content:'{"msgType": "MUTE_SWITCH_INFO"}'};if(globalVariable.call.callType){var participants=globalVariable.roomParticipants[channel+""];if(participants){Object.keys(participants).forEach(function(id){if(participants[id].display&&callNum.indexOf(participants[id].display)>=0){msg["room"]=channel;msg["feed"]=participants[id].id;console.log("video room send:",msg);videoRoomCall.send({message:msg})}})}}else{sipcall.send({message:msg})}}function callTimeOut(callNumber){console.log("call timeout start timing: "+callNumber);var timeoutObj=setTimeout(function(){var callParam=share_manager.getCallParam();if(callParam["status"]==="onCall"){clearTimeout(callParam["timeoutObj"])}else{console.log("call time out: "+callNumber);callParam["callStateMessageBack"]({errCode:2004,errMsg:"呼叫"+callNumber+"超时"});hangup()}},share_manager.getCallParam()["callTimeout"]);share_manager.setCallParam("timeoutObj",timeoutObj)}var getISSipCall=function(){return issipCall};function getChromeVersion(){const appVersion=navigator.appVersion;const index=appVersion.indexOf("Chrome/");const ChromeStr=appVersion.substring(index+7,appVersion.length);const index_point=ChromeStr.indexOf(".");const ChromeVersions=ChromeStr.substring(0,index_point);if(appVersion.includes("Chrome")){return ChromeVersions}else{return false}}function setBell(ring,call){ringing=new Audio(ring);calling=new Audio(call)}function setRoomDomIdSets(ids){roomDomIdSets.clear();if(ids&&ids.length>0){for(const val of ids){roomDomIdSets.add(val)}}}return{call:call,is_bell:is_bell,setPeerBitrate:setPeerBitrate,login:login,logout:logout,answer:answer,hangup:hangup,initAndLogin:initAndLogin,getCounterpartNum:getCounterpartNum,SwitchAudio:SwitchAudio,SDKSwitchAudio:SDKSwitchAudio,switchVideo:switchVideo,initHtmlTag:initHtmlTag,getCommingCall:getCommingCall,decline:decline,getCallErroResult:getCallErroResult,getCurrentjsp:getCurrentjsp,AllOpen:AllOpen,registVideoRoom:registVideoRoom,joinRoom:joinRoom,mut:mut,unpublishOwnFeed:unpublishOwnFeed,publishOwnFeed:publishOwnFeed,createRoom:createRoom,checkRoom:checkRoom,RoomList:RoomList,PartyList:PartyList,destroyRoom:destroyRoom,exitRoom:exitRoom,leaveRoom:leaveRoom,outLine:outLine,ReloadDevice:ReloadDevice,getVideoCall:getVideoCall,publishownFeedCustom:publishownFeedCustom,RoommutVideo:RoommutVideo,ClickVideoDisplay:ClickVideoDisplay,refreshLogOut:refreshLogOut,watchLive:watchLive,watchStreaming:watchStreaming,getWatchStreamInfo:getWatchStreamInfo,getWatchStreamInfoWithName:getWatchStreamInfoWithName,stopStream:stopStream,updateStreamsList:updateStreamsList,preShareScreen:preShareScreen,initPlayRecord:initPlayRecord,playRecordRequestURL:playRecordRequestURL,updateRecsList:updateRecsList,startRecording:startRecording,startPlayout:startPlayout,playRecordStop:playRecordStop,initPlayRecordPrepare:initPlayRecordPrepare,CloseLocalVideoExit:CloseLocalVideoExit,SDKSwitchVideo:SDKSwitchVideo,SDKSwitchRoomCamera:SDKSwitchRoomCamera,p2pTransform:p2pTransform,p2pLiveRecord:p2pLiveRecord,StopTransform:StopTransform,changeLocalVideo:changeLocalVideo,videoCallType:videoCallType,requestStreamingInfo:requestStreamingInfo,requestSkipDuration:requestSkipDuration,requestRecordPlay:requestRecordPlay,recordPause:recordPause,recordPlaying:recordPlaying,startWatchLiveWithPassword:startWatchLiveWithPassword,startLiveWithName:startLiveWithName,requesUserOnLineList:requesUserOnLineList,searchReacord:searchReacord,recordPreview:recordPreview,getVideoInfo:getVideoInfo,getCallHandle:getCallHandle,getRoomhandle:getRoomhandle,videoRoomInviteUsers:videoRoomInviteUsers,ampleskyRegistVideoRoom:ampleskyRegistVideoRoom,ampleskyExitRoom:ampleskyExitRoom,ampleskyNewRemoteFeed:ampleskyNewRemoteFeed,getRoomMaxPublisher:getRoomMaxPublisher,checkRoomTotalPublisher:checkRoomTotalPublisher,kickRoomUserWithID:kickRoomUserWithID,kickRoomUserWithName:kickRoomUserWithName,getRoomRemoteListName:getRoomRemoteListName,getMyIDAndCreateID:getMyIDAndCreateID,getSipStatus:getSipStatus,getVideoCallStatus:getVideoCallStatus,roomMuteSpeaker:roomMuteSpeaker,videoCallSwitch:videoCallSwitch,sipInfo:sipInfo,start_talkback:start_talkback,stop_talkback:stop_talkback,set_call_codec_type:set_call_codec_type,sip_switch_other_mic:sip_switch_other_mic,getStats:getStats2,detachRoom:detachRoom,getISSipCall:getISSipCall,switchCameraSipInfo:switchCameraSipInfo,switchMuteSipInfo:switchMuteSipInfo,call_reqinfo:call_reqinfo,muteRoomSpeaker:muteRoomSpeaker,setBell:setBell,setRoomDomIdSets:setRoomDomIdSets}}();export let amplesky_venus=function(){let registered=false;let loginErrorBack;let onConnecting;let loginTimer;let loginParam;let allRecordChunks={};let allRecorder={};let allRecordCanvas={};let openIM=typeof openImSdk!=="undefined"?new openImSdk.OpenIMSDK:{};let im_msg;let im_msg_sent=new Map;let imCallback=null;function imcpHttpRequest(method,url,contentType,send,callback){let request=new XMLHttpRequest;request.open(method,url);request.setRequestHeader("Content-Type",contentType);request.setRequestHeader("Blade-Auth",localStorage.getItem("token"));request.send(send);request.onreadystatechange=function(){let response;if(request.readyState===4){if(request.status===200){response=JSON.parse(request.responseText)}else{response={code:request.status,success:false,msg:request.responseText}}callback&&callback(response)}}}function mcuHttpRequest(method,url,contentType,send,callback){let request=new XMLHttpRequest;request.open(method,url);request.setRequestHeader("Content-Type",contentType);request.setRequestHeader("Blade-Auth",localStorage.getItem("token"));request.send(send);request.onreadystatechange=function(){let response;if(request.readyState===4){if(request.status===200){response=JSON.parse(request.responseText)}else{response={code:request.status,readyState:request.readyState,responseText:request.responseText,statusText:request.statusText}}callback&&callback(response)}}}function getlayoutId(pmcoSubType){const layoutStyles={auto:0,"1_1_1":1,"2_2_1":2,"2_2_2":3,"3_2_1":4,"3_2_2":5,"4_2_1":6,"4_2_2":7,"5_1_1":8,"6_1_1":9,"7_1_1":10,"8_1_1":11,"9_1_1":12,"10_2_1":13,"10_2_2":14,"13_1_1":15,"16_1_1":16,"20_1_1":17};return layoutStyles[pmcoSubType]}function isJSON(str){if(typeof str=="string"){try{const obj=JSON.parse(str);return!!(typeof obj=="object"&&obj)}catch(e){console.log("error："+str+"!!!"+e);return false}}console.log("It is not a string!")}function isEmpty(str){if(str=="undefined"||!str||!/[^\s]/.test(str)){return true}else{return false}}return{getVersion:function(){return{version:share_manager.getVersion()}},iceServerSetting:function(iceServer,iceAccount,icePassword,type,protocol,switchOn){if(switchOn==="1"){if(parseInt(type)==0){iceServer=iceServer.indexOf("turn:")===0?iceServer.substring(5):iceServer;localStorage.setItem("turnProtocol",protocol);localStorage.setItem("iceTurnUser",iceAccount);localStorage.setItem("iceTurnPwd",icePassword)}else{iceServer=iceServer.indexOf("stun:")===0?iceServer.substring(5):iceServer}localStorage.setItem("turnEnable",type);localStorage.setItem("iceServer",iceServer)}else{localStorage.setItem("turnEnable","2")}localStorage.setItem("setIceSwitch",switchOn)},login:function(account,gateway_ip,mcu_ip,LogState,errBack,callStateMessage,backJsonMsg,jsepMsg){if(!account){console.error("请输入用户号");return}if(!gateway_ip){console.error("请输入WebRTC网关地址");return}if(!mcu_ip){console.error("请输入mcu后台地址");return}localStorage.setItem("SetMeetServer","1");share_manager.setIsSDK(true);let frmebitrate=localStorage.getItem("frameBitrate");if(frmebitrate&&frmebitrate>0){share_manager.setVideoFrameBitrate(frmebitrate)}else{share_manager.setVideoFrameBitrate(1024);localStorage.setItem("frameBitrate",1024+"")}let gateway_url=new URL(gateway_ip);let gatewayParamsStr=gateway_url.search.slice(1);if(gatewayParamsStr){let params=new URLSearchParams(gatewayParamsStr);let interceptPayload=params.get("intercept");if(interceptPayload){share_manager.setIntercept(parseInt(interceptPayload))}else{share_manager.setIntercept(null)}}navigator.mediaDevices.getUserMedia({audio:true,video:true},function(stream){stream.getTracks().forEach(track=>{track.stop()});stream.stop()});this.internalLogin(account,"",gateway_ip,mcu_ip,LogState,errBack,callStateMessage,backJsonMsg,jsepMsg)},loginWithPwd:function(account,password,gateway_ip,mcu_ip,LogState,errBack,callStateMessage,backJsonMsg,jsepMsg){if(!account){console.error("请输入用户号");return}if(!gateway_ip){console.error("请输入WebRTC网关地址");return}if(!mcu_ip){console.error("请输入mcu后台地址");return}localStorage.setItem("SetMeetServer","1");let frmebitrate=localStorage.getItem("frameBitrate");if(frmebitrate&&frmebitrate>0){share_manager.setVideoFrameBitrate(frmebitrate)}else{share_manager.setVideoFrameBitrate(1024);localStorage.setItem("frameBitrate",1024+"")}let gateway_url=new URL(gateway_ip);let gatewayParamsStr=gateway_url.search.slice(1);if(gatewayParamsStr){let params=new URLSearchParams(gatewayParamsStr);let interceptPayload=params.get("intercept");if(interceptPayload){share_manager.setIntercept(parseInt(interceptPayload))}else{share_manager.setIntercept(null)}}this.internalLogin(account,password,gateway_ip,mcu_ip,LogState,errBack,callStateMessage,backJsonMsg,jsepMsg)},internalLogin:function(account,password,gateway_ip,mcu_ip,LogState,errBack,callStateMessage,backJsonMsg,jspMsg){share_manager.initLocalStorage();password=password&&password.length>0?password:"123456";loginParam={account:account,password:password,gateway_ip:gateway_ip.split("?")[0],mcu_ip:mcu_ip,LogState:LogState,errBack:errBack,callStateMessage:callStateMessage,backJsonMsg:backJsonMsg,jspMsg:jspMsg};console.log("调用登录请求："+JSON.stringify(loginParam));venus_manager.logout();share_manager.setIsForceLogout(false);venus_manager.initAndLogin({GateWay:loginParam.gateway_ip,server:loginParam.mcu_ip,name:loginParam.account,exten:loginParam.account,password:password},function(success){registered=success;if(success){console.log("login success");if(loginTimer){console.log("clear login timer");clearInterval(loginTimer)}onConnecting=false}return LogState(success)},function(errorback){loginErrorBack=errBack;console.error("Venus failed, error: ",errorback);if(!share_manager.getIsForceLogout()){if(errorback==="Lost connection to the server (is it down?)"||errorback.indexOf("the server down")>=0||errorback.indexOf("API call failed")>=0){console.error("Lost connection, relogin.");amplesky_venus.loginErrorBackAction()}}return errBack(errorback)},function(callMessage){return callStateMessage(callMessage)},function(msg){if(msg["forcelogout"]){share_manager.setIsForceLogout(true);console.error("Force logout.",msg["error"])}return backJsonMsg(msg)},function(Jsp){return jspMsg(Jsp)},()=>{})},loginOut:function(){clearInterval(loginTimer);venus_manager.hangup();venus_manager.exitRoom();venus_manager.logout();share_manager.setIsForceLogout(false)},set_call_codec_type:function(audio_type,video_type,use_default){venus_manager.set_call_codec_type(audio_type,video_type,use_default)},bell_enabel:function(enabel){venus_manager.is_bell(enabel)},call:function(callee,isScreenVideo,send_video,recv_video,send_audio,recv_audio,remote_video,remote_audio,local_video,callback){if(callee&&callee.trim().length>0){if(registered){venus_manager.initHtmlTag(remote_video,remote_audio,local_video);venus_manager.call(callee,isScreenVideo,send_video,recv_video,send_audio,recv_audio,function(status){return callback(status)});if(!(send_video||send_audio)){console.error("视频与音频请至少选择一种方式");callback({errCode:2001,errMsg:"视频与音频请至少选择一种方式"})}}else{console.error("请先登录");callback({errCode:2002,errMsg:"呼叫前请先登录"})}}else{console.error("被叫号码不可为空");callback({errCode:2003,errMsg:"被叫号码不可为空"})}},hangup:function(){venus_manager.hangup()},call_camera:function(venus_server,meeting_server,call_number,remote_stream_id,call_back){call_camera.call_camera(venus_server,meeting_server,call_number,remote_stream_id,function(msg,jsep){call_back(msg,jsep)})},hangup_camera:function(call_number){call_camera.hangup_camera(call_number)},clean_camera:function(){call_camera.clean_call_camera()},getCommingCall:function(callbackMessage){venus_manager.getCommingCall(function(incomingName,incomingType){return callbackMessage(incomingName,incomingType)})},commingJsep:function(backJsep){backJsep(venus_manager.getCurrentjsp())},accept:function(remote_video,remote_audio,local_video,isScreenVideo){venus_manager.initHtmlTag(remote_video,remote_audio,local_video);venus_manager.answer(isScreenVideo,function(jsp){})},setPeerToPeerBitrate:function(bitrate){venus_manager.setPeerBitrate(bitrate)},decline:function(){venus_manager.decline()},mute:function(enable){venus_manager.SwitchAudio(enable)},videoCameraSwitch:function(enable){venus_manager.SDKSwitchVideo(enable)},start_talkback:function(isRoom){venus_manager.start_talkback(isRoom)},stop_talkback:function(isRoom){venus_manager.stop_talkback(isRoom)},sip_switch_other_mic:function(mute,isRoom){venus_manager.sip_switch_other_mic(mute,isRoom)},getOnlineUser:function(){venus_manager.requesUserOnLineList()},shareScreen:function(isShare,isVideoCall,callback){venus_manager.preShareScreen(isShare,isVideoCall,function(status){callback(status)})},initWatchStreaming:function(VideoID,Server,iceServer,protol,name,pwd,backMessage,stateMessage){venus_manager.watchStreaming(VideoID,Server,iceServer,protol,name,pwd,function back(message){if(Array.isArray(message)){backMessage(message)}else{stateMessage(message)}})},updateWatchList:function(backList){venus_manager.updateStreamsList(function back(list){backList(list)})},getWatchInfo:function(ID){venus_manager.getWatchStreamInfo(ID,"")},getWatchInfoWithName:function(name){venus_manager.getWatchStreamInfoWithName(name,"")},startWatchWithPassword:function(WatchID,password){venus_manager.startWatchLiveWithPassword(WatchID,password)},startLiveWithName:function(name,password){venus_manager.startLiveWithName(name,password)},stopStream:function(type){venus_manager.stopStream(type)},initPlayRecord:function(remoteVideoID,Server,iceServer,protol,name,pwd,status){venus_manager.initPlayRecordPrepare(remoteVideoID,Server,iceServer,protol,name,pwd,function(Status){status(Status)})},updataPlayRecordList:function(dataList){venus_manager.updateRecsList(function(list){dataList(list)})},StartRecord:function(recordName){venus_manager.startRecording(recordName)},startPlayRecord:function(ID,Password){venus_manager.startPlayout(ID,Password)},stopPlayRecord:function(){venus_manager.playRecordStop()},playRecordNecessaryValue:function(type,duration,recordID,videoContainID){share_manager.setRecordID(recordID);share_manager.setsecond(duration);share_manager.setVideoContainID(videoContainID);share_manager.setAPIType(type)},playRecordRequestDownloadURL:function(recordID,backResult){venus_manager.playRecordRequestURL(recordID,function(back){backResult(back)})},registVideoRoom:function(localVideoID,server,roomNumber,userName,avType,messageCallback){let maxPublisher=parseInt(localStorage.getItem("roomPublisher"));maxPublisher=isNaN(maxPublisher)?9:maxPublisher;if(avType!="audio"||avType!="video"){avType="video"}if(isEmpty(userName))userName=loginParam.account;if(isEmpty(roomNumber)){console.error("room number is empty.");return}venus_manager.ampleskyExitRoom();venus_manager.ampleskyRegistVideoRoom(localVideoID,server,false,roomNumber,maxPublisher,userName,avType,null,null,null,messageCallback)},setRoomDomIdSets:function(ids){venus_manager.setRoomDomIdSets(ids)},checkVideoRoomisExit:function(roomNumber){venus_manager.checkRoom(parseInt(roomNumber))},creatVideoRoom:function(roomID,maxPublisher){venus_manager.createRoom(parseInt(roomID),maxPublisher)},joinVideoRoom:function(room,userName){venus_manager.joinRoom(parseInt(room),userName)},destoryVideoRoom:function(roomID){venus_manager.destroyRoom(parseInt(roomID))},getRoomList:function(){venus_manager.RoomList()},getParticipants:function(RoomID){venus_manager.PartyList(parseInt(RoomID))},publishOwnFeed:function(audio,video){venus_manager.publishOwnFeed(audio,video)},publishownFeedCustom:function(useAudio,videoSend){venus_manager.publishownFeedCustom(useAudio,videoSend)},unpublishOwnFeed:function(){venus_manager.unpublishOwnFeed()},ampleskyNewRemoteFeed:function(id,display,ownName,video,remoteFeed,callback){venus_manager.ampleskyNewRemoteFeed(id,display,ownName,video,function(Feed){remoteFeed(Feed)},function(back){callback(back)})},checkRoomMaxPublisher:function(msg,roomNumber){venus_manager.getRoomMaxPublisher(msg,parseInt(roomNumber))},roomInviteUser:function(roomID,users){venus_manager.videoRoomInviteUsers(parseInt(roomID),users)},kickRoomUserWithName:function(secret,roomID,callNum){venus_manager.kickRoomUserWithName(secret,roomID,callNum)},exitRoom:function(){venus_manager.ampleskyExitRoom()},roomCameraSwitch:function(enable){venus_manager.SDKSwitchRoomCamera(enable)},roomMutedSpeaker:function(){venus_manager.mut()},roomMutedVideo:function(muted,usermute){venus_manager.RoommutVideo()},getRoomHandle:function(){return venus_manager.getRoomhandle()},setUserImages:function(data){share_manager.setUserImages(data)},remoteScreenCapture:function(elementId,filename){let videoElement=document.getElementById(elementId);if(videoElement){let canvas=document.createElement("canvas");canvas.width=videoElement.videoWidth;canvas.height=videoElement.videoHeight;canvas.getContext("2d").drawImage(videoElement,0,0);let a=document.createElement("a");a.href=canvas.toDataURL("image/png");a.download=filename||"下载图片";a.click()}else{return{success:false,errMsg:"传入的视频标签有误"}}},startLocalRecord:function(elementId){let video=document.getElementById(elementId);let canvas=document.createElement("canvas");canvas.width=640;canvas.height=360;allRecordChunks[elementId]=[];allRecordCanvas[elementId]=canvas;let ctx=canvas.getContext("2d");ctx.fillStyle="white";ctx.fillRect(0,0,640,360);this.playCanvas(video,ctx);if(video){let stream=canvas.captureStream(30);allRecorder[elementId]=new MediaRecorder(stream);allRecorder[elementId].ondataavailable=e=>{console.log("trigger ondataavailable");allRecordChunks[elementId].push(e.data)};allRecorder[elementId].start(10)}},playCanvas:function(video,ctx){ctx.drawImage(video,0,0,640,360);requestAnimationFrame(()=>{this.playCanvas(video,ctx)})},stopLocalRecord:function(elementId){allRecorder[elementId].stop();let link=document.createElement("a");link.style.display="none";let fullBlob=new Blob(allRecordChunks[elementId]);link.href=window.URL.createObjectURL(fullBlob);link.download="video_"+(new Date).getTime()+".mp4";document.body.appendChild(link);link.click();link.remove()},reLoginAction:function(){if(loginTimer){clearInterval(loginTimer)}onConnecting=true;loginTimer=setInterval(function(){venus_manager.logout();console.log("重新注册--"+JSON.stringify(loginParam));amplesky_venus.internalLogin(loginParam.account,loginParam.password,loginParam.gateway_ip,loginParam.mcu_ip,loginParam.LogState,loginParam.errBack,loginParam.callStateMessage,loginParam.backJsonMsg,loginParam.jspMsg)},1e4)},loginErrorBackAction:function(){venus_manager.CloseLocalVideoExit();if(!onConnecting&&!venus_manager.getVideoCallStatus()){venus_manager.logout();venus_manager.outLine(false);amplesky_venus.reLoginAction()}},setVideoFrameBitrate:function(bitrate){if(!bitrate||isNaN(bitrate)||bitrate<0){share_manager.setVideoFrameBitrate(1024);localStorage.setItem("frameBitrate",1024+"")}else{share_manager.setVideoFrameBitrate(bitrate);localStorage.setItem("frameBitrate",bitrate)}},getUserMediaStats:function(){return{audioTextReceive:share_manager.getCallParam()["audioTextReceive"],audioTextSend:share_manager.getCallParam()["audioTextSend"],videoTextReceive:share_manager.getCallParam()["videoTextReceive"],videoTextSend:share_manager.getCallParam()["videoTextSend"]}},getCameraDevices:async function(){var cameraDevices=[];await navigator.mediaDevices.getUserMedia({audio:true,video:true},function(stream){stream.getTracks().forEach(track=>{track.stop()});stream.stop()});await navigator.mediaDevices.enumerateDevices().then(devices=>{for(let i=0;i!==devices.length;++i){const deviceInfo=devices[i];if(deviceInfo.kind==="videoinput"){cameraDevices.push(deviceInfo)}}});return cameraDevices},getMicDevices:async function(){var micDevices=[];await navigator.mediaDevices.getUserMedia({audio:true,video:true},function(stream){stream.getTracks().forEach(track=>{track.stop()});stream.stop()});await navigator.mediaDevices.enumerateDevices().then(devices=>{for(let i=0;i!==devices.length;++i){const deviceInfo=devices[i];if(deviceInfo.kind==="audioinput"){micDevices.push(deviceInfo)}}});return micDevices},setCamera:function(deviceId){if(deviceId!=null){localStorage.setItem("selectedCamera",deviceId)}},setMic:function(deviceId){if(deviceId!=null){localStorage.setItem("selectedMic",deviceId)}},setBell:function(ring,call){venus_manager.setBell(ring,call)},loginIMCP:function(serverIp,username,password,callback){let formData="tenantId=000000";formData+="&username="+username;formData+="&password="+password;formData+="&grant_type=password";formData+="&scope=all";let request=new XMLHttpRequest;request.open("POST",`${serverIp}/blade-auth/oauth/token`);request.setRequestHeader("Content-Type","application/x-www-form-urlencoded");request.setRequestHeader("Tenant-Id","000000");request.setRequestHeader("Authorization","Basic c3dvcmQ6c3dvcmRfc2VjcmV0");request.send(formData);request.onreadystatechange=function(){let response;if(request.readyState===4&&request.status===200){localStorage.setItem("token",JSON.parse(request.responseText)["access_token"]);localStorage.setItem("serverIp",serverIp);response={code:200,success:true,msg:"登录成功"}}else{response={code:404,success:false,msg:"后台接口地址访问失败"}}callback&&callback(response)}},channelList:function(connStatus,chName,chCode,platName,mcuId,groupID,callback){let formdata="current=1&size=1000";if(!isEmpty(connStatus)){formdata+="&connStatus="+connStatus}if(!isEmpty(chName)){formdata+="&chName="+chName}if(!isEmpty(chCode)){formdata+="&chCode="+chCode}if(!isEmpty(platName)){formdata+="&platName="+platName}if(!isEmpty(mcuId)){formdata+="&mcuId="+mcuId}if(!isEmpty(groupID)){formdata+="&groupID="+groupID}imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/channel/page?`+formdata,"application/x-www-form-urlencoded",null,callback)},queryChannel:function(platCode,chCode,mcuId,startTime,endTime,callback){let formData="current=1&size=1000";formData+="&platCode="+platCode;formData+="&chCode="+chCode;formData+="&mcuId="+mcuId;formData+="&startTime="+startTime;formData+="&endTime="+endTime;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/video/notice?`+formData,"application/x-www-form-urlencoded",null,callback)},queryChannelList:function(chCode,mcuId,startTime,endTime,callback){let formData="current=1&size=1000";formData+="&chCode="+chCode;formData+="&mcuId="+mcuId;formData+="&startTime="+startTime;formData+="&endTime="+endTime;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/video/playbackpage?`+formData,"application/x-www-form-urlencoded",null,callback)},channelStatus:function(id,callback){let formData="id="+id;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/channel/status?`+formData,"application/x-www-form-urlencoded",null,callback)},controlRecording:function(id,action,callback){let formData="id="+id;formData+="&action="+action;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/channel/start-record-control?`+formData,"application/x-www-form-urlencoded",null,callback)},control:function(action,status,participantNumber,pmcoMcuId,callback){let formData={action:action,participantNumber:participantNumber,pmcoMcuId:pmcoMcuId,status:status};imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/ptz`,"application/json",JSON.stringify(formData),callback)},standardList:function(callback){let formData="type=0&current=1&size=10000";imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/subplat/page?`+formData,"application/x-www-form-urlencoded",null,callback)},standardRestart:function(id,callback){let formData="id="+id;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/subplat/restart?`+formData,"application/x-www-form-urlencoded",null,callback)},noticeStandardInfo:function(id,callback){let formData="id="+id;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/subplat/notice-device?`+formData,"application/x-www-form-urlencoded",null,callback)},getStandardInfo:function(id,callback){let formData="id="+id;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/subplat/get-device?`+formData,"application/x-www-form-urlencoded",null,callback)},onlineConferenceList:function(callback){let formData="current=1&size=1000";imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/online/list?`+formData,"application/x-www-form-urlencoded",null,callback)},lockConference:function(id,isLock,callback){let formData="id="+id;formData+="&isLock="+isLock;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/lock`,"application/x-www-form-urlencoded",formData,callback)},endConference:function(id,callback){let formData="id="+id;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/end`,"application/x-www-form-urlencoded",formData,callback)},getConferenceInfo:function(id,callback){let formData="current=1&size=1000";formData+="&id="+id;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/online/detail?`+formData,"application/x-www-form-urlencoded",null,callback)},hangupTerminal:function(id,participantNumber,callback){let formData="id="+id;formData+="&participantNumber="+participantNumber;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/handUp`,"application/x-www-form-urlencoded",formData,callback)},controlMic:function(audioMute,id,participantNumber,callback){let formData="audioMute="+audioMute;formData+="&id="+id;formData+="&participantNumber="+participantNumber;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/mute`,"application/x-www-form-urlencoded",formData,callback)},isPoll:function(isPoll,id,participantNumber,callback){let formData="isPoll="+isPoll;formData+="&id="+id;formData+="&participantNumber="+participantNumber;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/isPoll`,"application/x-www-form-urlencoded",formData,callback)},isShield:function(isShield,id,participantNumber,callback){let formData="isShield="+isShield;formData+="&id="+id;formData+="&participantNumber="+participantNumber;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/isShield`,"application/x-www-form-urlencoded",formData,callback)},setSpeaker:function(id,speaker,callback){let formData="id="+id;formData+="&speaker="+speaker;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/speaker`,"application/x-www-form-urlencoded",formData,callback)},setChairman:function(id,chairperson,callback){let formData="id="+id;formData+="&chairperson="+chairperson;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/chairperson`,"application/x-www-form-urlencoded",formData,callback)},talkBack:function(mcuId,participantNumber,pmcoNumber,talkback,callback){let formData={mcuId:mcuId,participantNumber:participantNumber,pmcoNumber:pmcoNumber,talkback:talkback};imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/sys/talkback/control`,"application/json",JSON.stringify(formData),callback)},remove:function(flag,id,participantNumber,callback){let formData="flag="+flag;formData+="&id="+id;formData+="&participantNumber="+participantNumber;imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/remove`,"application/x-www-form-urlencoded",formData,callback)},sendDTMF:function(conferenceNumber,mcuId,participantNumber,type,callback){let formData={conferenceNumber:conferenceNumber,mcuId:mcuId,participantNumber:participantNumber,type:type};imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/sys/participant/dtmf`,"application/json",JSON.stringify(formData),callback)},invite:function(flag,driver,id,ipAddress,mediaVideo,partName,partNumbers,pmcoMcuId,pmcoNumber,type,videoCap,rtsp,callback){let formData={flag:flag,driver:driver,id:id,ipAddress:ipAddress,mediaVideo:mediaVideo,partName:partName,partNumbers:partNumbers,pmcoMcuId:pmcoMcuId,pmcoNumber:pmcoNumber,type:type,videoCap:videoCap};if(!isEmpty(rtsp)){formData.rtsp=rtsp}imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/part/invite`,"application/json",JSON.stringify(formData),callback)},setCaption:function(id,supported,location,fontType,fontColor,leftOffset,rightOffset,topOffset,bottomOffset,callback){let formData={id:id,supported:supported,location:location,fontType:fontType,fontColor:fontColor,leftOffset:leftOffset,rightOffset:rightOffset,topOffset:topOffset,bottomOffset:bottomOffset};imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/caption/modify`,"application/json",JSON.stringify(formData),callback)},setDisplayMode:function(id,autoLayout,autoScanTime,pmcoSubType,pollAudio,user,callback){let formData={id:id,autoLayout:autoLayout,autoScanTime:autoScanTime,pmcoSubType:pmcoSubType,layoutStyle:getlayoutId(pmcoSubType),pollAudio:pollAudio,user:user};imcpHttpRequest("POST",`${localStorage.getItem("serverIp")}/imcp-mcu/conf/layout/modify`,"application/json",JSON.stringify(formData),callback)},getMobilePosition:function(deviceId,mcuId,current,size,startTime,endTime,callback){let formData="current="+current;formData+="&size="+size;if(!isEmpty(mcuId))formData+="&mcuId="+mcuId;if(!isEmpty(deviceId))formData+="&deviceId="+deviceId;if(!isEmpty(startTime))formData+="&startTime="+startTime;if(!isEmpty(endTime))formData+="&endTime="+endTime;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-mcu/mcu/channel/mobilePositionPage?`+formData,"application/x-www-form-urlencoded",null,callback)},getRecordList:function(name,recordNo,sourceType,sourceName,beginTime,endTime,status,mediaType,current,size,callback){let formData="current="+current;formData+="&size="+size;if(name.length>0)formData+="&name="+name;if(recordNo.length>0)formData+="&record_no="+recordNo;if(sourceType.length>0)formData+="&source_type="+sourceType;if(sourceName.length>0)formData+="&source_name="+sourceName;if(status.length>0)formData+="&status="+status;if(mediaType.length>0)formData+="&media_type="+mediaType;if(beginTime.length>0)formData+="&begin_time="+beginTime;if(endTime.length>0)formData+="&end_time="+endTime;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-record/record/list?`+formData,"application/x-www-form-urlencoded",null,callback)},getRecordInfo:function(id,callback){let formData="id="+id;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-record/record/detail?`+formData,"application/x-www-form-urlencoded",null,callback)},getRecordMp4List:function(recordId,name,beginTime,endTime,callback){let formData="record_id="+recordId;if(name.length>0)formData+="&name="+name;if(beginTime.length>0)formData+="&begin_time="+beginTime;if(endTime.length>0)formData+="&end_time="+endTime;imcpHttpRequest("GET",`${localStorage.getItem("serverIp")}/imcp-record/recordMp4/list?`+formData,"application/x-www-form-urlencoded",null,callback)},getBaseParam:function(callback){mcuHttpRequest("GET",window.location.origin+"/mcu/utsys?service=mcs.mcsinfo&action=GET","application/x-www-form-urlencoded",null,callback)},setBaseParam:function(account,pwd,centerUrl,licensePlate,pageTitle,callback){let params=JSON.stringify({account:account,pwd:pwd,centerUrl:centerUrl,licensePlate:licensePlate,pageTitle:pageTitle});mcuHttpRequest("GET",window.location.origin+"/mcu/utsys?service=mcs.mcsinfo&action=MODIFY&params="+encodeURIComponent(params),"application/json",null,callback)},getLayoutJson:function(callback){mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=meeting.getlayoutconfiginfo","application/x-www-form-urlencoded",null,callback)},getLayoutInfo:function(conferenceNumber,callback){mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=Meeting.MeetingPanePlacementInfo&conferenceNumber="+conferenceNumber,"application/json",null,callback)},setLayoutInfo:function(conferenceNumber,panel_participants,layoutStyle,callback){const params=JSON.stringify({autoScanTime:"5",pollaudio:"yes",conferenceNumber:conferenceNumber,autoLayout:layoutStyle===undefined?"yes":"no",panel_participants:panel_participants,layoutStyle:layoutStyle||""});mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=Meeting.OnlineMeetingControl&action=displaymode&params="+encodeURIComponent(params),"application/x-www-form-urlencoded",null,callback)},getMeetingInfo:function(conferenceNumber,callback){mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=Meeting.OnlineMeetingInfo&conferenceNumber="+conferenceNumber,"application/json",null,callback)},getSuPlatConf:function(callback){const params=JSON.stringify({start:0,offset:100,type:0});mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=plat.subPlatConf&action=GET&params="+encodeURIComponent(params),"application/x-www-form-urlencoded",null,callback)},addSuPlatConf:function(suPlat,callback){mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=plat.subPlatConf&action=ADD&params="+encodeURIComponent(JSON.stringify(suPlat)),"application/json",null,callback)},deleteSuPlatConf:function(name,code,ip,callback){const params=JSON.stringify({name:name,code:code,ip:ip,type:"0"});mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=plat.subPlatConf&action=DELETE&params="+encodeURIComponent(params),"application/json",null,callback)},getChannel:function(callback){mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=mcs.mcschannelget&start=0&offset=100","application/json",null,callback)},getMcuInfo:function(callback){mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=system.systemvcamerainfo","application/json",null,callback)},controlCamera:function(action,status,partyNumber,step,callback){const params=JSON.stringify({action:action,status:status,partyNumber:partyNumber,step:step});mcuHttpRequest("GET",window.location.origin+"/mcu/utsys/?service=ptz.action&params="+encodeURIComponent(params),"application/json",null,callback)},imEvents:function(callback,notice){let cbe=openImSdk.CbEvents;imCallback=callback;openIM.on(cbe.ONCONNECTFAILED,data=>{console.log("ON CONNECT FAILED");notice("ON CONNECT FAILED","连接失败")});openIM.on(cbe.ONCONNECTSUCCESS,data=>{console.log("ON CONNECT SUCCESS");notice("ON CONNECT SUCCESS","连接成功")});openIM.on(cbe.ONCONNECTING,data=>{console.log("ON CONNECTING");notice("ON CONNECTING","连接中")});openIM.on(cbe.ONKICKEDOFFLINE,data=>{console.log("ON KICKED OFFLINE");notice("ON KICKED OFFLINE","被踢下线")});openIM.on(cbe.ONUSERTOKENEXPIRED,data=>{console.log("ON USER TOKEN EXPIRED");notice("ON USER TOKEN EXPIRED","token过期")});openIM.on(cbe.ONJOINEDGROUPADDED,data=>{console.log("ON JOINED GROUP ADDED");notice("ON JOINED GROUP ADDED","加入了新群组")});openIM.on(cbe.ONJOINEDGROUPDELETED,data=>{console.log("ON JOINED GROUP DELETED");notice("ON JOINED GROUP DELETED","退出了某个群组")});openIM.on(cbe.ONGROUPMEMBERADDED,data=>{console.log("ON GROUP MEMBER ADDED");notice("ON GROUP MEMBER ADDED","加入的某个群有新成员加入")});openIM.on(cbe.ONGROUPMEMBERDELETED,data=>{console.log("ON GROUP MEMBER DELETED");notice("ON GROUP MEMBER DELETED","加入的某个群有成员退出")});openIM.on(cbe.ONGROUPAPPLICATIONADDED,data=>{console.log("ON GROUP APPLICATION ADDED");notice("ON GROUP APPLICATION ADDED","收到或发出的群申请有新增")});openIM.on(cbe.ONGROUPAPPLICATIONDELETED,data=>{console.log("ON GROUP APPLICATION DELETED");notice("ON GROUP APPLICATION DELETED","收到或发出的群申请有减少")});openIM.on(cbe.ONGROUPINFOCHANGED,data=>{console.log("ON GROUP INFO CHANGED");notice("ON GROUP INFO CHANGED","已加入的某个群信息改变")});openIM.on(cbe.ONGROUPMEMBERINFOCHANGED,data=>{console.log("ON GROUP MEMBER INFO CHANGED");notice("ON GROUP MEMBER INFO CHANGED","已加入的某个群中有群成员信息改变")});openIM.on(cbe.ONGROUPAPPLICATIONACCEPTED,data=>{console.log("ON GROUP APPLICATION ACCEPTED");notice("ON GROUP APPLICATION ACCEPTED","收到或发出的群申请被同意")});openIM.on(cbe.ONGROUPAPPLICATIONREJECTED,data=>{console.log("ON GROUP APPLICATION REJECTED");notice("ON GROUP APPLICATION REJECTED","收到或翻出的群申请被拒绝")});openIM.on(cbe.ONBLACKADDED,data=>{console.log("ON BLACK ADDED");notice("ON BLACK ADDED","有用户被添加到黑名单")});openIM.on(cbe.ONBLACKDELETED,data=>{console.log("ON BLACK DELETED");notice("ON BLACK DELETED","从黑名单中移除了某个用户")});openIM.on(cbe.ONFRIENDAPPLICATIONACCEPTED,data=>{console.log("ON FRIEND APPLICATION ACCEPTED");notice("ON FRIEND APPLICATION ACCEPTED","收到或发出的好友请求被接受")});openIM.on(cbe.ONFRIENDAPPLICATIONREJECTED,data=>{console.log("ON FRIEND APPLICATION REJECTED");notice("ON FRIEND APPLICATION REJECTED","收到或发出的好友请求被拒绝")});openIM.on(cbe.ONFRIENDAPPLICATIONADDED,data=>{console.log("ON FRIEND APPLICATION ADDED");notice("ON FRIEND APPLICATION ADDED","收到或发出的好友请求列表增加");this.imGetSendFriendApplicationList();this.imGetRecvFriendApplicationList()});openIM.on(cbe.ONFRIENDAPPLICATIONDELETED,data=>{console.log("ON FRIEND APPLICATION DELETED");notice("ON FRIEND APPLICATION DELETED","收到或发出的好友请求列表减少");this.imGetSendFriendApplicationList();this.imGetRecvFriendApplicationList()});openIM.on(cbe.ONFRIENDINFOCHANGED,data=>{console.log("ON FRIEND INFO CHANGED");notice("ON FRIEND INFO CHANGED","好友信息更新")});openIM.on(cbe.ONFRIENDADDED,data=>{console.log("ON FRIEND ADDED");notice("ON FRIEND ADDED","好友列表增加")});openIM.on(cbe.ONFRIENDDELETED,data=>{console.log("ON FRIEND DELETED");notice("ON FRIEND DELETED","好友列表减少")});openIM.on(cbe.ONSELFINFOUPDATED,data=>{console.log("ON SELF INFO UPDATED");notice("ON SELF INFO UPDATED","当前登录用户个人信息改变")});openIM.on(cbe.ONRECVNEWMESSAGE,data=>{console.log("ON RECV NEW MESSAGE");notice("ON RECV NEW MESSAGE","收到新消息")});openIM.on(cbe.ONRECVNEWMESSAGES,data=>{console.log("ON RECV NEW MESSAGES");notice("ON RECV NEW MESSAGES","收到新消息（开启批量推送时）")});openIM.on(cbe.ONRECVMESSAGEREVOKED,data=>{console.log("ON RECV MESSAGE REVOKED");notice("ON RECV MESSAGE REVOKED","有消息被撤回")});openIM.on(cbe.ONRECVC2CREADRECEIPT,data=>{console.log("ON RECV C2C READ RECEIPT");notice("ON RECV C2C READ RECEIPT","收到单聊已读回执，即有人读了发出的消息")});openIM.on(cbe.ONRECVGROUPREADRECEIPT,data=>{console.log("ON RECV GROUP READ RECEIPT");notice("ON RECV GROUP READ RECEIPT","收到群聊已读回执，即有人读了发出的消息")});openIM.on(cbe.ONNEWCONVERSATION,data=>{console.log("ON NEW CONVERSATION");notice("ON NEW CONVERSATION","新增新会话")});openIM.on(cbe.ONCONVERSATIONCHANGED,data=>{console.log("ON CONVERSATION CHANGED");notice("ON CONVERSATION CHANGED","已有的会话发生改变")});openIM.on(cbe.ONTOTALUNREADMESSAGECOUNTCHANGED,data=>{console.log("ON TOTAL UNREAD MESSAGE COUNT CHANGED");notice("ON TOTAL UNREAD MESSAGE COUNT CHANGED","消息总未读数改变")})},imInit:function(userID,token,apiAddress,wsAddress,url){share_manager.setImParam("userID",userID);share_manager.setImParam("token",token);share_manager.setImParam("url",url)},imLogin:function(){let im=share_manager.getImParam();console.log(im);let config={userID:im.userID,token:im.token,url:im.url,platformID:im.platform};openIM.login(config).then(res=>{console.log("login success...",res);imCallback({action:"imLogin",success:true,msg:res})}).catch(err=>{console.error("login failed...",err);imCallback({action:"imLogin",success:false,msg:err})})},imLogout:function(){openIM.logout().then(res=>{console.log("logout success...",res);imCallback({action:"imLogout",success:true,msg:res})}).catch(err=>{console.log("logout failed...");imCallback({action:"imLogout",success:false,msg:err})})},imLoginStatus:function(){openIM.getLoginStatus().then(res=>{imCallback({action:"imLoginStatus",success:true,data:res.data?res.data:""})}).catch(err=>{imCallback({action:"imLoginStatus",success:false,msg:err})})},imLoginUser:function(){openIM.getLoginUser().then(res=>{imCallback({action:"imLoginUser",success:true,data:res})}).catch(err=>{imCallback({action:"imLoginUser",success:false,msg:err})})},imInviteUserToGroup:function(groupID,reason,userIDList){let options={groupID:groupID,reason:reason,userIDList:userIDList.split(",")};openIM.inviteUserToGroup(options).then(({data})=>{data=isJSON(data)?JSON.parse(data):data;imCallback({action:"imInviteUserToGroup",success:true,data:data})}).catch(err=>{imCallback({action:"imInviteUserToGroup",success:false,msg:err})})},imKickGroupMember:function(groupID,reason,userIDList){let options={groupID:groupID,reason:reason,userIDList:userIDList.split(",")};openIM.kickGroupMember(options).then(({data})=>{data=isJSON(data)?JSON.parse(data):data;imCallback({action:"imKickGroupMember",success:true,data:data})}).catch(err=>{imCallback({action:"imKickGroupMember",success:false,msg:err})})},imGetGroupMembersInfo:function(groupID,userIDList){let options={groupID:groupID,userIDList:userIDList.split(",")};openIM.getGroupMembersInfo(options).then(({data})=>{imCallback({action:"imGetGroupMembersInfo",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetGroupMembersInfo",success:false,msg:err})})},imGetGroupMemberList:function(groupID,filter,offset,count){let options={groupID:groupID,filter:parseInt(filter),offset:parseInt(offset),count:parseInt(count)};openIM.getGroupMemberList(options).then(({data})=>{imCallback({action:"imGetGroupMemberList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetGroupMemberList",success:false,msg:err})})},imGetJoinedGroupList:function(){openIM.getJoinedGroupList().then(({data})=>{imCallback({action:"imGetJoinedGroupList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetJoinedGroupList",success:false,msg:err})})},imCreateGroup:function(groupMember,groupName,introduction,notification,faceURL,ex){const groupBaseInfo={groupType:0,groupName:groupName};if(typeof introduction=="string"&&introduction.length>0)groupBaseInfo.introduction=introduction;if(typeof notification=="string"&&notification.length>0)groupBaseInfo.notification=notification;if(typeof faceURL=="string"&&faceURL.length>0)groupBaseInfo.faceURL=faceURL;if(typeof ex=="string"&&ex.length>0)groupBaseInfo.ex=ex;let memberList=[];let members=groupMember.split(",");let i=0;while(i<members.length){let mem=members[i].split(":");memberList.push({userID:mem[0],roleLevel:parseInt(mem[1])});i++}const options={groupBaseInfo:groupBaseInfo,memberList:memberList};openIM.createGroup(options).then(({data})=>{data=isJSON(data)?JSON.parse(data):data;imCallback({action:"imCreateGroup",success:true,data:data})}).catch(err=>{imCallback({action:"imCreateGroup",success:false,msg:err})})},imSetGroupInfo:function(groupID,groupName,introduction,notification,faceURL,ex){const groupInfo={groupName:groupName,introduction:introduction,notification:notification,faceURL:faceURL,ex:ex};const options={groupID:groupID,groupInfo:groupInfo};openIM.setGroupInfo(options).then(({data})=>{imCallback({action:"imSetGroupInfo",success:true,data:data})}).catch(err=>{imCallback({action:"imSetGroupInfo",success:false,msg:err})})},imGetGroupsInfo:function(groupIDList){const ids=groupIDList.split(",");openIM.getGroupsInfo(ids).then(({data})=>{imCallback({action:"imGetGroupsInfo",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetGroupsInfo",success:false,msg:err})})},imJoinGroup:function(groupID,reqMsg){const options={groupID:groupID,reqMsg:reqMsg};openIM.joinGroup(options).then(({data})=>{imCallback({action:"imJoinGroup",success:true,data:data})}).catch(err=>{imCallback({action:"imJoinGroup",success:false,msg:err})})},imQuitGroup:function(groupID){openIM.quitGroup(groupID).then(({data})=>{imCallback({action:"imQuitGroup",success:true,data:data})}).catch(err=>{imCallback({action:"imQuitGroup",success:false,msg:err})})},imTransferGroupOwner:function(groupID,newOwnerUserID){const options={groupID:groupID,newOwnerUserID:newOwnerUserID};openIM.transferGroupOwner(options).then(({data})=>{imCallback({action:"imTransferGroupOwner",success:true,data:data})}).catch(err=>{imCallback({action:"imTransferGroupOwner",success:false,msg:err})})},imGetRecvGroupApplicationList:function(){openIM.getRecvGroupApplicationList().then(({data})=>{imCallback({action:"imGetRecvGroupApplicationList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetRecvGroupApplicationList",success:false,msg:err})})},imGetSendGroupApplicationList:function(){openIM.getSendGroupApplicationList().then(({data})=>{imCallback({action:"imGetSendGroupApplicationList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetSendGroupApplicationList",success:false,msg:err})})},imAcceptGroupApplication:function(groupID,fromUserID,handleMsg){const options={groupID:groupID,fromUserID:fromUserID,handleMsg:handleMsg};openIM.acceptGroupApplication(options).then(({data})=>{imCallback({action:"imAcceptGroupApplication",success:true,data:data})}).catch(err=>{imCallback({action:"imAcceptGroupApplication",success:false,msg:err})})},imRefuseGroupApplication:function(groupID,fromUserID,handleMsg){const options={groupID:groupID,fromUserID:fromUserID,handleMsg:handleMsg};openIM.refuseGroupApplication(options).then(({data})=>{imCallback({action:"imRefuseGroupApplication",success:true,data:data})}).catch(err=>{imCallback({action:"imRefuseGroupApplication",success:false,msg:err})})},imDismissGroup:function(groupID){openIM.dismissGroup(groupID).then(({data})=>{imCallback({action:"imDismissGroup",success:true,data:data})}).catch(err=>{imCallback({action:"imDismissGroup",success:false,msg:err})})},imChangeGroupMute:function(groupID,isMute){const options={groupID:groupID,isMute:isMute};openIM.changeGroupMute(options).then(({data})=>{imCallback({action:"imChangeGroupMute",success:true,data:data})}).catch(err=>{imCallback({action:"imChangeGroupMute",success:false,msg:err})})},imChangeGroupMemberMute:function(groupID,userID,mutedSeconds){const options={groupID:groupID,userID:userID,mutedSeconds:mutedSeconds};openIM.changeGroupMemberMute(options).then(({data})=>{imCallback({action:"imChangeGroupMemberMute",success:true,data:data})}).catch(err=>{imCallback({action:"imChangeGroupMemberMute",success:false,msg:err})})},imSetGroupMemberNickname:function(groupID,userID,nickname){const options={groupID:groupID,userID:userID,GroupMemberNickname:nickname};openIM.setGroupMemberNickname(options).then(({data})=>{imCallback({action:"imSetGroupMemberNickname",success:true,data:data})}).catch(err=>{imCallback({action:"imSetGroupMemberNickname",success:false,msg:err})})},imSearchGroups:function(keywordList,isSearchGroupID,isSearchGroupName){const options={keywordList:keywordList,isSearchGroupID:isSearchGroupID,isSearchGroupName:isSearchGroupName};openIM.searchGroups(options).then(({data})=>{imCallback({action:"imSearchGroups",success:true,data:data})}).catch(err=>{imCallback({action:"imSearchGroups",success:false,msg:err})})},imSetGroupMemberRoleLevel:function(groupID,userID,roleLevel){const options={groupID:groupID,userID:userID,roleLevel:roleLevel};openIM.setGroupMemberRoleLevel(options).then(({data})=>{imCallback({action:"imSetGroupMemberRoleLevel",success:true,data:data})}).catch(err=>{imCallback({action:"imSetGroupMemberRoleLevel",success:false,msg:err})})},imSetGroupVerification:function(groupID,verification){const options={groupID:groupID,verification:verification};openIM.setGroupVerification(options).then(({data})=>{imCallback({action:"imSetGroupVerification",success:true,data:data})}).catch(err=>{imCallback({action:"imSetGroupVerification",success:false,msg:err})})},imAddFriend:function(toUserID,reqMsg){const options={toUserID:toUserID,reqMsg:reqMsg};openIM.addFriend(options).then(({data})=>{imCallback({action:"imAddFriend",success:true,data:data})}).catch(err=>{imCallback({action:"imAddFriend",success:false,msg:err})})},imGetRecvFriendApplicationList:function(){openIM.getRecvFriendApplicationList().then(({data})=>{imCallback({action:"imGetRecvFriendApplicationList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetRecvFriendApplicationList",success:false,msg:err})})},imGetSendFriendApplicationList:function(){openIM.getSendFriendApplicationList().then(({data})=>{imCallback({action:"imGetSendFriendApplicationList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetSendFriendApplicationList",success:false,msg:err})})},imAcceptFriendApplication:function(toUserID,handleMsg){const options={toUserID:toUserID,handleMsg:handleMsg};openIM.acceptFriendApplication(options).then(({data})=>{imCallback({action:"imAcceptFriendApplication",success:true,data:data})}).catch(err=>{imCallback({action:"imAcceptFriendApplication",success:false,msg:err})})},imRefuseFriendApplication:function(toUserID,handleMsg){const options={toUserID:toUserID,handleMsg:handleMsg};openIM.refuseFriendApplication(options).then(({data})=>{imCallback({action:"imRefuseFriendApplication",success:true,data:data})}).catch(err=>{imCallback({action:"imRefuseFriendApplication",success:false,msg:err})})},imGetFriendList:function(){openIM.getFriendList().then(({data})=>{imCallback({action:"imGetFriendList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetFriendList",success:false,msg:err})})},imGetDesignatedFriendsInfo:function(friendIDs){const ids=friendIDs.split(",");openIM.getDesignatedFriendsInfo(ids).then(({data})=>{imCallback({action:"imGetDesignatedFriendsInfo",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetDesignatedFriendsInfo",success:false,msg:err})})},imSetFriendRemark:function(toUserID,remark){const options={toUserID:toUserID,remark:remark};openIM.setFriendRemark(options).then(({data})=>{imCallback({action:"imSetFriendRemark",success:true,data:data})}).catch(err=>{imCallback({action:"imSetFriendRemark",success:false,msg:err})})},imCheckFriend:function(userIDs){const ids=userIDs.split(",");openIM.checkFriend(ids).then(({data})=>{imCallback({action:"imCheckFriend",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imCheckFriend",success:false,msg:err})})},imDeleteFriend:function(userID){openIM.deleteFriend(userID).then(({data})=>{imCallback({action:"imDeleteFriend",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteFriend",success:false,msg:err})})},imGetBlacklist:function(){openIM.getBlackList().then(({data})=>{imCallback({action:"imGetBlacklist",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetBlacklist",success:false,msg:err})})},imAddBlack:function(userID){openIM.addBlack(userID).then(({data})=>{imCallback({action:"imAddBlack",success:true,data:data})}).catch(err=>{imCallback({action:"imAddBlack",success:false,msg:err})})},imRemoveBlack:function(userID){openIM.removeBlack(userID).then(({data})=>{imCallback({action:"imRemoveBlack",success:true,data:data})}).catch(err=>{imCallback({action:"imRemoveBlack",success:false,msg:err})})},imSearchFriends:function(keywordList,isSearchUserID,isSearchNickname,isSearchRemark){const options={keywordList:keywordList,isSearchUserID:isSearchUserID,isSearchNickname:isSearchNickname,isSearchRemark:isSearchRemark};openIM.searchFriends(options).then(({data})=>{imCallback({action:"imSearchFriends",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imSearchFriends",success:false,msg:err})})},imGetUsersInfo:function(userIDList){openIM.getUsersInfo(userIDList).then(({data})=>{imCallback({action:"imGetUsersInfo",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetUsersInfo",success:false,msg:err})})},imGetSelfUserInfo:function(){openIM.getSelfUserInfo().then(({data})=>{imCallback({action:"imGetSelfUserInfo",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetSelfUserInfo",success:false,msg:err})})},imSetSelfInfo:function(userID,nickname,faceURL,gender,phoneNumber,birth,email,ex){const selfInfo={userID:userID,nickname:nickname,faceURL:faceURL,gender:gender,phoneNumber:phoneNumber,birth:birth,email:email,ex:ex};openIM.setSelfInfo(selfInfo).then(({data})=>{imCallback({action:"imSetSelfInfo",success:true,data:data})}).catch(err=>{imCallback({action:"imSetSelfInfo",success:false,msg:err})})},imGetHistoryMessageList:function(groupID,startClientMsgID,count,userID){const options={groupID:groupID,startClientMsgID:startClientMsgID,count:parseInt(count),userID:userID};openIM.getHistoryMessageList(options).then(({data})=>{data=isJSON(data)?JSON.parse(data):data;imCallback({action:"imGetHistoryMessageList",success:true,data:data})}).catch(err=>{imCallback({action:"imGetHistoryMessageList",success:false,msg:err})})},imRevokeMessage:function(clientMsgID){const msg=im_msg_sent.get(clientMsgID);if(isEmpty(msg)){imCallback({action:"imRevokeMessage",success:false,msg:"没有找到对应消息id"})}else{openIM.revokeMessage(msg).then(({data})=>{imCallback({action:"imRevokeMessage",success:true,data:data});openIM.deleteMessageFromLocalStorage(msg).then(({data})=>{imCallback({action:"deleteMessageFromLocalStorage",success:true,data:data})}).catch(err=>{imCallback({action:"deleteMessageFromLocalStorage",success:false,msg:err})});openIM.deleteMessageFromLocalAndSvr(msg).then(({data})=>{imCallback({action:"deleteMessageFromLocalAndSvr",success:true,data:data})}).catch(err=>{imCallback({action:"deleteMessageFromLocalAndSvr",success:false,msg:err})})}).catch(err=>{imCallback({action:"imRevokeMessage",success:false,msg:err})})}},imDeleteMessageFromLocalStorage:function(message){openIM.deleteMessageFromLocalStorage(message).then(({data})=>{imCallback({action:"imDeleteMessageFromLocalStorage",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteMessageFromLocalStorage",success:false,msg:err})})},imDeleteMessageFromLocalAndSvr:function(message){openIM.deleteMessageFromLocalAndSvr(message).then(({data})=>{imCallback({action:"imDeleteMessageFromLocalAndSvr",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteMessageFromLocalAndSvr",success:false,msg:err})})},imInsertSingleMessageToLocalStorage:function(message,recvID,sendID){const options={message:message,recvID:recvID,sendID:sendID};openIM.insertSingleMessageToLocalStorage(options).then(({data})=>{imCallback({action:"imInsertSingleMessageToLocalStorage",success:true,data:data})}).catch(err=>{imCallback({action:"imInsertSingleMessageToLocalStorage",success:false,msg:err})})},imInsertGroupMessageToLocalStorage:function(message,groupID,sendID){const options={message:message,groupID:groupID,sendID:sendID};openIM.insertSingleMessageToLocalStorage(options).then(({data})=>{imCallback({action:"imInsertGroupMessageToLocalStorage",success:true,data:data})}).catch(err=>{imCallback({action:"imInsertGroupMessageToLocalStorage",success:false,msg:err})})},imMarkC2CMessageAsRead:function(userID,msgIDList){const options={userID:userID,msgIDList:msgIDList};openIM.markC2CMessageAsRead(options).then(({data})=>{imCallback({action:"imMarkC2CMessageAsRead",success:true,data:data})}).catch(err=>{imCallback({action:"imMarkC2CMessageAsRead",success:false,msg:err})})},imMarkGroupMessageAsRead:function(groupID,msgIDList){const options={groupID:groupID,msgIDList:msgIDList};openIM.markGroupMessageAsRead(options).then(({data})=>{imCallback({action:"imMarkGroupMessageAsRead",success:true,data:data})}).catch(err=>{imCallback({action:"imMarkGroupMessageAsRead",success:false,msg:err})})},imTypingStatusUpdate:function(recvID,msgTip){const options={recvID:recvID,msgTip:msgTip};openIM.typingStatusUpdate(options).then(({data})=>{imCallback({action:"imTypingStatusUpdate",success:true,data:data})}).catch(err=>{imCallback({action:"imTypingStatusUpdate",success:false,msg:err})})},imCreateTextMessage:function(textStr){openIM.createTextMessage(textStr).then(({data})=>{im_msg=data;data=isJSON(data)?JSON.parse(data):data;imCallback({action:"imCreateTextMessage",success:true,data:data})}).catch(err=>{imCallback({action:"imCreateTextMessage",success:false,msg:err})})},imCreateTextAtMessage:function(text,atUserIDList,atUserInfoList,message){let infoList=[];let i=0;while(i<atUserInfoList.length){infoList.push({atUserID:atUserInfoList[i].userID,groupNickname:atUserInfoList[i].groupNickname});i++}const options={text:text,atUserIDList:atUserIDList,atUserInfoList:infoList,message:message};openIM.createTextAtMessage(options).then(({data})=>{imCallback({action:"imCreateTextAtMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateTextAtMessage",success:false,msg:err})})},imCreateAdvancedTextMessage:function(text,messageEntityList){let messageEntitys=[];let i=0;while(i<messageEntityList.length){messageEntitys.push({type:messageEntityList[i].type,offset:messageEntityList[i].offset,length:messageEntityList[i].length,url:messageEntityList[i].url,info:messageEntityList[i].info});i++}const options={messageEntityList:messageEntitys,text:text};openIM.createAdvancedTextMessage(options).then(({data})=>{imCallback({action:"imCreateAdvancedTextMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateAdvancedTextMessage",success:false,msg:err})})},imCreateImageMessage:function(uuid,type,size,width,height,url){const baseInfo={uuid:uuid,type:type,size:size,width:width,height:height,url:url};const options={sourcePicture:baseInfo,bigPicture:baseInfo,snapshotPicture:baseInfo};openIM.createImageMessage(options).then(({data})=>{imCallback({action:"imCreateImageMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateImageMessage",success:false,msg:err})})},imCreateSoundMessage:function(uuid,soundPath,sourceUrl,dataSize,duration){const options={uuid:uuid,soundPath:soundPath,sourceUrl:sourceUrl,dataSize:dataSize,duration:duration};openIM.createSoundMessage(options).then(({data})=>{imCallback({action:"imCreateSoundMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateSoundMessage",success:false,msg:err})})},imCreateVideoMessage:function(videoPath,videoType,duration,snapshotPath,videoUUID,videoUrl,videoSize,snapshotUUID,snapshotSize,snapshotUrl,snapshotWidth,snapshotHeight){const options={videoPath:videoPath,duration:duration,videoType:videoType,snapshotPath:snapshotPath,videoUUID:videoUUID,videoUrl:videoUrl,videoSize:videoSize,snapshotUUID:snapshotUUID,snapshotSize:snapshotSize,snapshotUrl:snapshotUrl,snapshotWidth:snapshotWidth,snapshotHeight:snapshotHeight};openIM.createVideoMessage(options).then(({data})=>{imCallback({action:"imCreateVideoMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateVideoMessage",success:false,msg:err})})},imCreateFileMessage:function(filePath,fileName,uuid,sourceUrl,fileSize){const options={filePath:filePath,fileName:fileName,uuid:uuid,sourceUrl:sourceUrl,fileSize:fileSize};openIM.createFileMessage(options).then(({data})=>{imCallback({action:"imCreateFileMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateFileMessage",success:false,msg:err})})},imCreateMergerMessage:function(messageList,title,summaryList){const options={messageList:messageList,title:title,summaryList:summaryList};openIM.createMergerMessage(options).then(({data})=>{imCallback({action:"imCreateMergerMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateMergerMessage",success:false,msg:err})})},imCreateForwardMessage:function(message){openIM.createForwardMessage(message).then(({data})=>{imCallback({action:"imCreateForwardMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateForwardMessage",success:false,msg:err})})},imCreateLocationMessage:function(description,latitude,longitude){const options={description:description,longitude:longitude,latitude:latitude};openIM.createLocationMessage(options).then(({data})=>{imCallback({action:"imCreateLocationMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateLocationMessage",success:false,msg:err})})},imCreateCustomMessage:function(data,extension,description){const options={data:data,extension:extension,description:description};openIM.createCustomMessage(options).then(({data})=>{imCallback({action:"imCreateCustomMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateCustomMessage",success:false,msg:err})})},imCreateQuoteMessage:function(text,message){const options={text:text,message:message};openIM.createQuoteMessage(options).then(({data})=>{imCallback({action:"imCreateQuoteMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateQuoteMessage",success:false,msg:err})})},imCreateCardMessage:function(cardDesc){openIM.createCardMessage(cardDesc).then(({data})=>{imCallback({action:"imCreateCardMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateCardMessage",success:false,msg:err})})},imCreateFaceMessage:function(index,data){const options={index:index,data:data};openIM.createFaceMessage(options).then(({data})=>{imCallback({action:"imCreateFaceMessage",success:true,data:data});im_msg=data}).catch(err=>{imCallback({action:"imCreateFaceMessage",success:false,msg:err})})},imSendMessage:function(message,recvID,groupID,title,desc,ex){const offlinePushInfo={title:title,desc:desc,ex:ex,iOSPushSound:"",iOSBadgeCount:true};const options={recvID:recvID,groupID:groupID,offlinePushInfo:offlinePushInfo,message:message};openIM.sendMessage(options).then(({data,errCode})=>{const msg=JSON.parse(data);if(!isEmpty(msg["clientMsgID"])){im_msg_sent.set(msg["clientMsgID"],data)}imCallback({action:"imSendMessage",success:true,data:msg})}).catch(err=>{imCallback({action:"imSendMessage",success:false,msg:err})})},imSendMessageNotOss:function(groupID,recvID,title,desc,ex){const offlinePushInfo={title:title,desc:desc,ex:ex,iOSPushSound:"",iOSBadgeCount:true};const options={recvID:recvID,groupID:groupID,offlinePushInfo:offlinePushInfo,message:im_msg};openIM.sendMessageNotOss(options).then(({data,errCode})=>{const msg=JSON.parse(data);if(!isEmpty(msg["clientMsgID"])){im_msg_sent.set(msg["clientMsgID"],data)}imCallback({action:"imSendMessageNotOss",success:true,data:msg});if(!isEmpty(groupID)){const opt={message:data,groupID:groupID,sendID:msg.sendID};openIM.insertGroupMessageToLocalStorage(opt).then(({data})=>{imCallback({action:"insertGroupMessageToLocalStorage",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"insertGroupMessageToLocalStorage",success:false,msg:err})})}if(!isEmpty(recvID)){const opt={message:data,recvID:recvID,sendID:msg.sendID};openIM.insertSingleMessageToLocalStorage(opt).then(({data})=>{imCallback({action:"insertSingleMessageToLocalStorage",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"insertSingleMessageToLocalStorage",success:false,msg:err})})}}).catch(err=>{imCallback({action:"imSendMessageNotOss",success:false,msg:err})})},imClearC2CHistoryMessage:function(userID){openIM.clearC2CHistoryMessage(userID).then(({data})=>{imCallback({action:"imClearC2CHistoryMessage",success:true,data:data})}).catch(err=>{imCallback({action:"imClearC2CHistoryMessage",success:false,msg:err})})},imClearC2CHistoryMessageFromLocalAndSvr:function(userID){openIM.clearC2CHistoryMessageFromLocalAndSvr(userID).then(({data})=>{imCallback({action:"imClearC2CHistoryMessageFromLocalAndSvr",success:true,data:data})}).catch(err=>{imCallback({action:"imClearC2CHistoryMessageFromLocalAndSvr",success:false,msg:err})})},imClearGroupHistoryMessage:function(groupID){openIM.clearGroupHistoryMessage(groupID).then(({data})=>{imCallback({action:"imClearGroupHistoryMessage",success:true,data:data})}).catch(err=>{imCallback({action:"imClearGroupHistoryMessage",success:false,msg:err})})},imClearGroupHistoryMessageFromLocalAndSvr:function(groupID){openIM.clearGroupHistoryMessageFromLocalAndSvr(groupID).then(({data})=>{imCallback({action:"imClearGroupHistoryMessageFromLocalAndSvr",success:true,data:data})}).catch(err=>{imCallback({action:"imClearGroupHistoryMessageFromLocalAndSvr",success:false,msg:err})})},imSearchLocalMessages:function(conversationID,keywordList,keywordListMatchType,senderUserIDList,messageTypeList,searchTimePosition,searchTimePeriod,pageIndex,count){const options={conversationID:conversationID,keywordList:keywordList,keywordListMatchType:keywordListMatchType,senderUserIDList:senderUserIDList,messageTypeList:messageTypeList,searchTimePosition:searchTimePosition,searchTimePeriod:searchTimePeriod,pageIndex:pageIndex,count:count};openIM.searchLocalMessages(options).then(({data})=>{imCallback({action:"imSearchLocalMessages",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imSearchLocalMessages",success:false,msg:err})})},imDeleteAllMsgFromLocal:function(){openIM.deleteAllMsgFromLocal().then(({data})=>{imCallback({action:"imDeleteAllMsgFromLocal",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteAllMsgFromLocal",success:false,msg:err})})},imDeleteAllMsgFromLocalAndSvr:function(){openIM.deleteAllMsgFromLocalAndSvr().then(({data})=>{imCallback({action:"imDeleteAllMsgFromLocalAndSvr",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteAllMsgFromLocalAndSvr",success:false,msg:err})})},imGetHistoryMessageListReverse:function(groupID,startClientMsgID,count,userID){const options={groupID:groupID,startClientMsgID:startClientMsgID,count:parseInt(count),userID:userID};openIM.getHistoryMessageListReverse(options).then(({data})=>{imCallback({action:"imGetHistoryMessageListReverse",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetHistoryMessageListReverse",success:false,msg:err})})},imGetAllConversationList:function(){openIM.getAllConversationList().then(({data})=>{imCallback({action:"imGetAllConversationList",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetAllConversationList",success:false,msg:err})})},imGetConversationListSplit:function(offset,count){const options={offset:offset,count:count};openIM.getConversationListSplit(options).then(({data})=>{imCallback({action:"imGetConversationListSplit",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetConversationListSplit",success:false,msg:err})})},imGetOneConversation:function(sourceID,sessionType){const options={sourceID:sourceID,sessionType:sessionType};openIM.getOneConversation(options).then(({data})=>{imCallback({action:"imGetOneConversation",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetOneConversation",success:false,msg:err})})},imGetMultipleConversation:function(conversationIDList){openIM.getMultipleConversation(conversationIDList).then(({data})=>{imCallback({action:"imGetMultipleConversation",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetMultipleConversation",success:false,msg:err})})},imSetConversationDraft:function(conversationID,draftText){const options={conversationID:conversationID,draftText:draftText};openIM.setConversationDraft(options).then(({data})=>{imCallback({action:"imSetConversationDraft",success:true,data:data})}).catch(err=>{imCallback({action:"imSetConversationDraft",success:false,msg:err})})},imPinConversation:function(conversationID,isPinned){const options={conversationID:conversationID,isPinned:isPinned};openIM.pinConversation(options).then(({data})=>{imCallback({action:"imPinConversation",success:true,data:data})}).catch(err=>{imCallback({action:"imPinConversation",success:false,msg:err})})},imMarkGroupMessageHasRead:function(groupID){openIM.markGroupMessageHasRead(groupID).then(({data})=>{imCallback({action:"imMarkGroupMessageHasRead",success:true,data:data})}).catch(err=>{imCallback({action:"imMarkGroupMessageHasRead",success:false,msg:err})})},imMarkNotifyMessageHasRead:function(conversationID){openIM.markNotifyMessageHasRead(conversationID).then(({data})=>{imCallback({action:"imMarkNotifyMessageHasRead",success:true,data:data})}).catch(err=>{imCallback({action:"imMarkNotifyMessageHasRead",success:false,msg:err})})},imGetTotalUnreadMsgCount:function(){openIM.getTotalUnreadMsgCount().then(({data})=>{imCallback({action:"imGetTotalUnreadMsgCount",success:true,data:data})}).catch(err=>{imCallback({action:"imGetTotalUnreadMsgCount",success:false,msg:err})})},imSetConversationRecvMessageOpt:function(conversationIDList,opt){const options={conversationIDList:conversationIDList,opt:opt};openIM.setConversationRecvMessageOpt(options).then(({data})=>{imCallback({action:"imSetConversationRecvMessageOpt",success:true,data:data})}).catch(err=>{imCallback({action:"imSetConversationRecvMessageOpt",success:false,msg:err})})},imGetConversationRecvMessageOpt:function(conversationIDList){openIM.getConversationRecvMessageOpt(conversationIDList).then(({data})=>{imCallback({action:"imGetConversationRecvMessageOpt",success:true,data:JSON.parse(data)})}).catch(err=>{imCallback({action:"imGetConversationRecvMessageOpt",success:false,msg:err})})},imSetOneConversationPrivateChat:function(conversationID,isPrivate){const options={conversationID:conversationID,isPrivate:isPrivate};openIM.setOneConversationPrivateChat(options).then(({data})=>{imCallback({action:"imSetOneConversationPrivateChat",success:true,data:data})}).catch(err=>{imCallback({action:"imSetOneConversationPrivateChat",success:false,msg:err})})},imDeleteConversation:function(conversationID){openIM.deleteConversation(conversationID).then(({data})=>{imCallback({action:"imDeleteConversation",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteConversation",success:false,msg:err})})},imDeleteConversationFromLocalAndSvr:function(conversationID){openIM.deleteConversationFromLocalAndSvr(conversationID).then(({data})=>{imCallback({action:"imDeleteConversationFromLocalAndSvr",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteConversationFromLocalAndSvr",success:false,msg:err})})},imDeleteAllConversationFromLocal:function(){openIM.deleteAllConversationFromLocal().then(({data})=>{imCallback({action:"imDeleteAllConversationFromLocal",success:true,data:data})}).catch(err=>{imCallback({action:"imDeleteAllConversationFromLocal",success:false,msg:err})})},imResetConversationGroupAtType:function(conversationID){openIM.resetConversationGroupAtType(conversationID).then(({data})=>{imCallback({action:"imResetConversationGroupAtType",success:true,data:data})}).catch(err=>{imCallback({action:"imResetConversationGroupAtType",success:false,msg:err})})},imSetGlobalRecvMessageOpt:function(opt){const options={opt:opt};openIM.setGlobalRecvMessageOpt(options).then(({data})=>{imCallback({action:"imSetGlobalRecvMessageOpt",success:true,data:data})}).catch(err=>{imCallback({action:"imSetGlobalRecvMessageOpt",success:false,msg:err})})}}}();var share_manager=function(){var version="1.2.75";var recordID="";var recordPassword="";var recordDuration="";var second="";var playStatus="";var isLiveRecord=true;var showRecordBtn=false;var isClairEye=false;var isXiaoJing=true;let isNeutral=false;var mcuIP="127.0.0.1:5060";var gateWayIP=window.location.protocol+"//"+window.location.host+"/gw";var playType="";var playName="";var recordStatus=true;var recordStop=false;var remoteVideoID="";var videoContainID="";var ApiType=false;var isPlayRecord=false;var videoWith=0;var isShowLoading=false;var seekTime=1;var roomMaxPublisher=0;var localPublisher=0;var joinedNumner=0;var joinID=null;var inviteRoomID=null;var clickShowLocalPreview=false;var isH265Info=false;var isComming=false;var audioType=0;var isRecordShowPass=false;var autoChanageVideo=true;var closeVideo=100;var reduceFrameRate=50;var reduceVideo=3;var openVideo=80;var improveFrameRate=50;var improveVideo=10;var videoFrameRate=30;var defaultCodeType=1;var sipRegistStatus=false;var videoCallRegistStatus=false;var inviteType=0;var showPageIndex=null;var videoFrameBitrate=null;var roomPublisher=null;var streamingVideoID="streaming_remote_video";var testUsers=[];var isTestRoom=false;var useDefalutUser=true;var userImageMap=new Map;var interceptPayload=null;var supportAudio=true;var supportVideo=true;var isWxs=false;var isSDK=false;var isForceLogout=false;var call={otherSide:"",status:"ready",isCallee:false,ayType:"video",timeoutObj:null,callTimeout:25e3,rejectTheCall:false,statisticsTimer:null,audioTextReceive:"",audioTextSend:"",videoTextReceive:"",videoTextSend:"",callStateMessageBack:function(){}};var im={token:"",userID:"",platform:5,apiAddress:"",wsAddress:"",url:"",callback:{}};function getStreamingVideoID(){return streamingVideoID}function setStreamingVideoID(value){streamingVideoID=value}function getShowPageIndex(){return showPageIndex}function setShowPageIndex(value){showPageIndex=value}function getInviteType(){return inviteType}function setInviteType(value){inviteType=value}function getInviteRoomNumber(){return inviteRoomID}function setInviteRoomNumber(value){inviteRoomID=value}function getJoinID(){return joinID}function setJoinID(value){joinID=value}function getVideoCallRegistStatus(){return videoCallRegistStatus}function setVideoCallRegistStatus(value){videoCallRegistStatus=value}function getSipRegistStatus(){return sipRegistStatus}function setSipRegistStatus(value){sipRegistStatus=value}function getDefaultCodeType(){return defaultCodeType}function setDefaultCodeType(value){defaultCodeType=value}function getAutoChanageVideo(){return autoChanageVideo}function setAutoChanageVideo(value){autoChanageVideo=value}function getVideoFrameRate(){return videoFrameRate}function setVideoFrameRate(value){videoFrameRate=value}function setVideoFrameBitrate(value){if(!value||isNaN(value)||value<0){value=1024}else if(value>20480){value=20480}videoFrameBitrate=value}function getVideoFrameBitrate(value){videoFrameBitrate=value}function setRoomPublisher(value){roomPublisher=value}function getRoomPublisher(){return roomPublisher}function getCloseVideoNumber(){return closeVideo}function getReduceFrameRate(){return reduceFrameRate}function getReduceVideo(){return reduceVideo}function getOpenVideo(){return openVideo}function getImproveFrameRate(){return improveFrameRate}function getImproveVideo(){return improveVideo}function setCloseVideoNumber(value){closeVideo=value}function setReduceFrameRate(value){reduceFrameRate=value}function setReduceVideo(value){reduceVideo=value}function setOpenVideo(value){openVideo=value}function setImproveFrameRate(value){improveFrameRate=value}function setImproveVideo(value){improveVideo=value}function getRecordPassword(){return recordPassword}function getRecordShow(){return isRecordShowPass}function getAudioType(){return audioType}function getIsComming(){return isComming}function getCodeInfo(){return isH265Info}function getClickShowLocalPreview(){return clickShowLocalPreview}function getDuration(){return recordDuration}function getIsClairEye(){return isClairEye}function getRecordID(){return recordID}function getSecond(){return second}function getPlayStatus(){return playStatus}function getLiveRecord(){return isLiveRecord}function getVersion(){return version}function getMcuIP(){return mcuIP}function getGateWayIP(){return gateWayIP}function getPlayType(){return playType}function getPlayName(){return playName}function getRecordStatus(){return recordStatus}function getRecordStop(){return recordStop}function getRemoteVideoID(){return remoteVideoID}function getVideoContainID(){return videoContainID}function getAPIType(){return ApiType}function getShowrecord(){return showRecordBtn}function getPlayRecord(){return isPlayRecord}function getVideoWidth(){return videoWith}function getShowLoading(){return isShowLoading}function getSeekTime(){return seekTime}function getRoomMaxPublisher(){return roomMaxPublisher}function getLocalPublisher(){return localPublisher}function getJoinedNumner(){return joinedNumner}function getIsXiaoJing(){return isXiaoJing}function getNeutral(){return isNeutral}function setRecordID(value){recordID=value}function setDuration(value){recordDuration=value}function setsecond(value){second=value}function setPlayStatus(value){playStatus=value}function setPlayType(value){playType=value}function setPlayName(value){playName=value}function setRecordStatus(value){recordStatus=value}function setRecordStop(value){recordStop=value}function setVideoContainID(value){videoContainID=value}function setRemoteVideoID(value){remoteVideoID=value}function setAPIType(value){ApiType=value}function setPlayRecord(value){isPlayRecord=value}function setVideoWidth(value){videoWith=value}function setShowLoading(value){isShowLoading=value}function setSeekTime(value){seekTime=value}function setLocalPublish(value){localPublisher=value}function setMaxPublish(value){roomMaxPublisher=value}function setJoinedNumber(value){joinedNumner=value}function setClickShowLocalPreview(value){clickShowLocalPreview=value}function setCodeInfo(value){isH265Info=value}function setIsComming(value){isComming=value}function setAudioType(value){audioType=value}function setRecordShow(value){isRecordShowPass=value}function setRecordPassword(value){recordPassword=value}function getTestUsers(){return testUsers}function getIsTestRoom(){return isTestRoom}function setIsTestRoom(value){isTestRoom=value}function getUserDefaultUSer(){return useDefalutUser}function setUserDefaultUSer(value){useDefalutUser=value}function getSupportAudio(){return supportAudio}function setSupportAudio(bool){supportAudio=bool}function getSupportVideo(){return supportVideo}function setSupportVideo(bool){supportVideo=bool}function getVideoCodec(){var videoCodec=localStorage.getItem("CodeType");if(videoCodec!=undefined&&videoCodec!=null){return videoCodec}else{return"vp8,vp9,h264"}}function getAudioCodec(){let audioCodec=localStorage.getItem("audioCodec");if(audioCodec!=undefined&&audioCodec!=null){return audioCodec}else{return"opus,g722,alaw,mulaw"}}function getIntercept(){return interceptPayload}function setIntercept(value){interceptPayload=value}function getSwitchLocalPreview(ison){let type=parseInt(localStorage.getItem("localPreview"));if(type!=1&&type!=0){localStorage.setItem("localPreview",0);return 0}else{localStorage.setItem("localPreview",type);return type}}function setUserImages(data){if(Array.isArray(data)){for(var i=0,len=data.length;i<len;i++){var a=data[i];if(a.user&&a.img){userImageMap.set(a.user,a.img)}}}else if(data.user&&data.img){userImageMap.set(data.user,data.img)}}function getImageByUser(key){return userImageMap.get(key)}function getUserImageMap(){return userImageMap}function getCallParam(){return call}function setCallParam(key,value){call[key]=value}function cleanCallParam(){clearTimeout(call["timeoutObj"]);clearInterval(call["statisticsTimer"]);call.otherSide="";call.status="ready";call.isCallee=false;call.ayType="";call.timeoutObj=null;call.rejectTheCall=false;call.statisticsTimer=null;call.audioTextReceive="";call.audioTextSend="";call.videoTextReceive="";call.videoTextSend=""}function getImParam(){return im}function setImParam(key,value){im[key]=value}function getIsWxs(){return isWxs}function setIsWxs(bool){isWxs=bool}function getIsSDK(){return isSDK}function setIsSDK(bool){isSDK=bool}function getIsForceLogout(){return isForceLogout}function setIsForceLogout(bool){isForceLogout=bool}function initLocalStorage(){setLocalStorageItem("SetMeetServer","1");setLocalStorageItem("resolution","0");setLocalStorageItem("frameRate","20");setLocalStorageItem("frameBitrate","1024");setLocalStorageItem("transportType","1");setLocalStorageItem("defaultCodec","1");setLocalStorageItem("videoCodec","");setLocalStorageItem("audioCodec","");setLocalStorageItem("audioPriority","0");setLocalStorageItem("closeVideo","100");setLocalStorageItem("reduceFrameRate","50");setLocalStorageItem("reduceVideo","3");setLocalStorageItem("openVideo","80");setLocalStorageItem("improveFrameRate","50");setLocalStorageItem("improveVideo","10");setLocalStorageItem("roomPublisher","9");setLocalStorageItem("setIceSwitch","0");setLocalStorageItem("turnEnable","2");setLocalStorageItem("iceServer","");setLocalStorageItem("iceTurnUser","");setLocalStorageItem("iceTurnPwd","");setLocalStorageItem("autoAccept","0");setLocalStorageItem("autoRecord","0");setLocalStorageItem("fullScreen","0");setLocalStorageItem("keyboard","0");setLocalStorageItem("localPreview","1");setLocalStorageItem("language","0");setLocalStorageItem("userImageMap","");setLocalStorageItem("zoomView","0");setLocalStorageItem("test-room-count","10");setLocalStorageItem("test_start_room_id","9010000000");setLocalStorageItem("test-participant-number","2");setLocalStorageItem("test-gw-prefix","9999");setLocalStorageItem("test-default-number","1000")}function setLocalStorageItem(item,value){if(isEmpty(localStorage.getItem(item))){localStorage.setItem(item,value)}}function isEmpty(str){if(str=="undefined"||!str||!/[^\s]/.test(str)){return true}else{return false}}return{getDuration:getDuration,getRecordID:getRecordID,getSecond:getSecond,setRecordID:setRecordID,setDuration:setDuration,setsecond:setsecond,setPlayStatus:setPlayStatus,getPlayStatus:getPlayStatus,getLiveRecord:getLiveRecord,getVersion:getVersion,getPlayType:getPlayType,setPlayType:setPlayType,getPlayName:getPlayName,setPlayName:setPlayName,getRecordStatus:getRecordStatus,setRecordStatus:setRecordStatus,getRecordStop:getRecordStop,setRecordStop:setRecordStop,setRemoteVideoID:setRemoteVideoID,getVideoContainID:getVideoContainID,setVideoContainID:setVideoContainID,getRemoteVideoID:getRemoteVideoID,setAPIType:setAPIType,getAPIType:getAPIType,getShowrecord:getShowrecord,getIsClairEye:getIsClairEye,getPlayRecord:getPlayRecord,setPlayRecord:setPlayRecord,setVideoWidth:setVideoWidth,getVideoWidth:getVideoWidth,getShowLoading:getShowLoading,setShowLoading:setShowLoading,getSeekTime:getSeekTime,setSeekTime:setSeekTime,setLocalPublish:setLocalPublish,setMaxPublish:setMaxPublish,getRoomMaxPublisher:getRoomMaxPublisher,getLocalPublisher:getLocalPublisher,getJoinedNumner:getJoinedNumner,setJoinedNumber:setJoinedNumber,getSwitchLocalPreview:getSwitchLocalPreview,getIsXiaoJing:getIsXiaoJing,setClickShowLocalPreview:setClickShowLocalPreview,getClickShowLocalPreview:getClickShowLocalPreview,getCodeInfo:getCodeInfo,setCodeInfo:setCodeInfo,setIsComming:setIsComming,getIsComming:getIsComming,getAudioType:getAudioType,setAudioType:setAudioType,setRecordShow:setRecordShow,getRecordShow:getRecordShow,getRecordPassword:getRecordPassword,setRecordPassword:setRecordPassword,getCloseVideoNumber:getCloseVideoNumber,getReduceFrameRate:getReduceFrameRate,getReduceVideo:getReduceVideo,getOpenVideo:getOpenVideo,getImproveFrameRate:getImproveFrameRate,getImproveVideo:getImproveVideo,setCloseVideoNumber:setCloseVideoNumber,setReduceFrameRate:setReduceFrameRate,setReduceVideo:setReduceVideo,setOpenVideo:setOpenVideo,setImproveFrameRate:setImproveFrameRate,setImproveVideo:setImproveVideo,setVideoFrameRate:setVideoFrameRate,getVideoFrameRate:getVideoFrameRate,setVideoFrameBitrate:setVideoFrameBitrate,getVideoFrameBitrate:getVideoFrameBitrate,getAutoChanageVideo:getAutoChanageVideo,setAutoChanageVideo:setAutoChanageVideo,getDefaultCodeType:getDefaultCodeType,setDefaultCodeType:setDefaultCodeType,setSipRegistStatus:setSipRegistStatus,getSipRegistStatus:getSipRegistStatus,getVideoCallRegistStatus:getVideoCallRegistStatus,setVideoCallRegistStatus:setVideoCallRegistStatus,setJoinID:setJoinID,getJoinID:getJoinID,getInviteRoomNumber:getInviteRoomNumber,setInviteRoomNumber:setInviteRoomNumber,setInviteType:setInviteType,getInviteType:getInviteType,setShowPageIndex:setShowPageIndex,getShowPageIndex:getShowPageIndex,setRoomPublisher:setRoomPublisher,getRoomPublisher:getRoomPublisher,getStreamingVideoID:getStreamingVideoID,setStreamingVideoID:setStreamingVideoID,getGateWayIP:getGateWayIP,getMcuIP:getMcuIP,getNeutral:getNeutral,getTestUsers:getTestUsers,getIsTestRoom:getIsTestRoom,setIsTestRoom:setIsTestRoom,getUserDefaultUSer:getUserDefaultUSer,setUserDefaultUSer:setUserDefaultUSer,getVideoCodec:getVideoCodec,getAudioCodec:getAudioCodec,setUserImages:setUserImages,getImageByUser:getImageByUser,getUserImageMap:getUserImageMap,getIntercept:getIntercept,setIntercept:setIntercept,getCallParam:getCallParam,setCallParam:setCallParam,cleanCallParam:cleanCallParam,getImParam:getImParam,setImParam:setImParam,getSupportAudio:getSupportAudio,setSupportAudio:setSupportAudio,getSupportVideo:getSupportVideo,setSupportVideo:setSupportVideo,getIsWxs:getIsWxs,setIsWxs:setIsWxs,getIsSDK:getIsSDK,setIsSDK:setIsSDK,getIsForceLogout:getIsForceLogout,setIsForceLogout:setIsForceLogout,initLocalStorage:initLocalStorage}}();let playButtom=document.querySelector(".play");let controlBar=document.querySelector(".controlBar");var recordVideo=document.getElementById(share_manager.getStreamingVideoID());var timeDisplay;var diffnumber;var seekTime=1;var playID;var isWatchScreen=false;var hasPause=false;var tmp=1;if(recordVideo==undefined||recordVideo==null){recordVideo=document.getElementById("remote_stream_video")}if(playButtom){playButtom.onclick=function(e){pauseVideo();e.stopPropagation()}}function pauseVideo(){switch(tmp){case 0:tmp=1;hasPause=true;venus_manager.recordPause();recordVideo.pause();if(playButtom){playButtom.src="./photo/suspend_icon.png"}$("#watch_audio_img").attr("src","./photo/audio_pause.png");break;case 1:if(seekTime==parseInt(share_manager.getSecond())){replayAction()}tmp=0;hasPause=false;venus_manager.recordPlaying();recordVideo.play();if(playButtom){playButtom.src="./photo/start_icon.png"}$("#watch_audio_img").attr("src","./photo/audio.gif");break}}$(".streaming-replay-img").click(function(){replayAction()});function replayAction(){$("#watch_audio_img").attr("src","./photo/audio.gif");seekTime=0;share_manager.setSeekTime(seekTime);$(".current-Time").html(formatSeconds(parseInt(seekTime)));$(".process").val(parseInt(seekTime)/share_manager.getSecond());venus_manager.startPlayout(parseInt(share_manager.getRecordID(),share_manager.getRecordPassword()));recordVideo.play();$(".streaming-replay-img").hide();share_manager.setRecordStop(false);hasPause=false;tmp=1;if(playButtom){playButtom.src="./photo/suspend_icon.png"}}var docElm=document.documentElement;function watchVideoClick(){isWatchScreen=!isWatchScreen;if(isWatchScreen){if(!share_manager.getAPIType()){$("#streaming_video_screen").attr("src","./photo/exit_screen.png");$("#streaming_video_view").removeClass("h-75");$("#streaming_video_view").addClass("h-100")}else{$("#streaming_video_screen").attr("src","./photo/exit_screen.png");$("#"+share_manager.getVideoContainID()).css("height","100%");$("#"+share_manager.getVideoContainID()).css("marginTop","0")}if(docElm.webkitRequestFullScreen){docElm.webkitRequestFullScreen()}else if(docElm.requestFullScreen){docElm.requestFullScreen()}else if(docElm.msRequestFullscreen){docElm.msRequestFullscreen()}else if(docElm.mozRequestFullScreen){docElm.mozRequestFullScreen()}}else{if(!share_manager.getAPIType()){$("#streaming_video_screen").attr("src","./photo/video_screen.png");$("#streaming_video_view").removeClass("h-100");$("#streaming_video_view").addClass("h-75")}else{$("#streaming_video_screen").attr("src","./photo/video_screen.png");$("#"+share_manager.getVideoContainID()).css("height","80%");$("#"+share_manager.getVideoContainID()).css("marginTop","75px")}if(document.exitFullscreen){document.exitFullscreen()}else if(document.msExitFullscreen){document.msExitFullscreen()}else if(document.mozCancelFullScreen){document.mozCancelFullScreen()}else if(document.webkitCancelFullScreen){document.webkitCancelFullScreen()}}}function bindRecordListener(){if(recordVideo){console.log("record video add listener...");recordVideo.addEventListener("play",function(){$(".total-Time").html(formatSeconds(share_manager.getSecond()));if(!share_manager.getRecordStop()){$(".streaming-replay-img").hide()}if(share_manager.getRecordStatus()){share_manager.setRecordStatus(false);tmp=0;if(playButtom){playButtom.src="./photo/start_icon.png"}}if(!hasPause){tmp=0;if(playButtom){playButtom.src="./photo/start_icon.png"}}});recordVideo.addEventListener("ended",function(){console.log("播放结束")});recordVideo.addEventListener("timeupdate",function(){console.log("timeupdate--",recordVideo.currentTime);if(share_manager.getPlayStatus()=="stop"){seekTime=0;share_manager.setSeekTime(seekTime);share_manager.setPlayStatus("start")}timeDisplay=Math.floor(recordVideo.currentTime);if(diffnumber!=timeDisplay){if(share_manager.getSeekTime()!=null&&share_manager.getSeekTime()!=undefined){seekTime=parseInt(share_manager.getSeekTime())}seekTime=timeDisplay;share_manager.setSeekTime(seekTime);if(seekTime>=parseInt(share_manager.getSecond())){seekTime=parseInt(share_manager.getSecond());share_manager.setSeekTime(seekTime);hasPause=false;tmp=1;if(playButtom){playButtom.src="./photo/suspend_icon.png"}}$(".current-Time").html(formatSeconds(parseInt(seekTime)));$(".process").val(parseInt(seekTime)/share_manager.getSecond());$(".total-Time").html(formatSeconds(share_manager.getSecond()))}diffnumber=timeDisplay},false);recordVideo.addEventListener("pause",function(){console.log("播放暂停")})}}let start=false;let skipTimer;function densityInput(event){recordVideo.pause();let currenttime=parseInt(event*parseInt(share_manager.getSecond()));$(".current-Time").html(formatSeconds(currenttime));if(!start){start=true;clearTimeout(skipTimer);skipTimer=setTimeout(()=>{start=false},500);let changeDate=parseInt(share_manager.getSecond()*event);venus_manager.recordPreview(share_manager.getRecordID(),changeDate)}console.log(currenttime);$(".streaming-preview-img").show()}let timeOut;function skipDuration(value){start=false;recordVideo.play();$(".streaming-preview-img").hide();let second=share_manager.getSecond();let postion=parseInt(second*value);seekTime=postion;if(tmp==0){recordVideo.play()}share_manager.setSeekTime(postion);venus_manager.requestSkipDuration(postion);venus_manager.recordPlaying()}function formatSeconds(value){let result=parseInt(value);let h=Math.floor(result/3600)<10?"0"+Math.floor(result/3600):Math.floor(result/3600);let m=Math.floor(result/60%60)<10?"0"+Math.floor(result/60%60):Math.floor(result/60%60);let s=Math.floor(result%60)<10?"0"+Math.floor(result%60):Math.floor(result%60);result=`${h}:${m}:${s}`;return result}var call_camera=function(){var monitor_handles=[];var video_dict=[];var monitor_venus=null;var venus_server=null;var meeting_server=null;var opaqueId="videoroomtest-"+Math.random().toString;var video_codec_type=null;var audio_codec_type=null;return{call_camera:function(gateway_server,mcu_ip,call_number,remote_video,call_back){if(call_number.length>0&&remote_video.length>0&&gateway_server.length>0&&mcu_ip.length>0){if(call_number.indexOf("+")===0){call_number=call_number.slice(1)}meeting_server=mcu_ip;if(monitor_venus==null){venus_server=gateway_server;Venus.init({debug:"all",callback:function(){monitor_venus=new Venus({server:venus_server,success:function(){if(monitor_handles[call_number]){call_back("监控号码："+call_number+"已经在"+video_dict[call_number]+"播放")}else{monitor_attrach(call_number,remote_video,call_back)}},error:function(error){},destroyed:function(){}})}})}else{if(monitor_handles[call_number]){call_back("Error: 监控号码"+call_number+"已经在"+video_dict[call_number]+"播放")}else{monitor_attrach(call_number,remote_video,call_back)}}}else{alert("参数有误")}},call_video_audio_type:function(video_codec,audio_codec){video_codec_type=video_codec;audio_codec_type=audio_codec},hangup_camera:function(call_number){var hangup={request:"hangup"};if(call_number.indexOf("+")===0){call_number=call_number.slice(1)}if(monitor_handles[call_number]){monitor_handles[call_number].send({message:hangup});monitor_handles[call_number].hangup()}delete monitor_handles[call_number];delete video_dict[call_number]},clean_call_camera:function(){video_codec_type=null;audio_codec_type=null;monitor_venus=null;monitor_handles=[];video_dict=[]}};function monitor_attrach(call_number,remote_video,call_back){monitor_venus.attach({plugin:"venus.plugin.videomonitor",opaqueId:opaqueId,success:function(pluginHandle){monitor_handles[call_number]=pluginHandle;video_dict[call_number]=remote_video;var body={request:"monitor_call",monitor_id:call_number,uri:"sip:"+call_number+"@"+meeting_server,audio_codec:audio_codec_type==null?"G722":audio_codec_type,video_codec:video_codec_type==null?"H264":video_codec_type};pluginHandle.send({message:body})},error:function(error){console.log(error)},onmessage:function(msg,jsep){var result=msg["result"];if(result!=undefined&&result!=null){var event=result["event"];call_back(msg,jsep);if(event!=null&&event!=undefined){if(event=="accepted"){if(jsep!==undefined&&jsep!==null){var call_handle=monitor_handles[result["username"]];if(call_handle){monitor_handles[result["username"]].createAnswer({jsep:jsep,media:{audioRecv:false,videoRecv:true,audioSend:false,videoSend:false},simulcast:false,success:function(jsep){var body={request:"monitor_start"};monitor_handles[result["username"]].send({message:body,jsep:jsep})},error:function(error){Venus.error("WebRTC error:",error)}})}}}}}},onremotestream:function(stream,pluginHandle){var midKey=null;Object.keys(monitor_handles).map((key,index)=>{if(monitor_handles[key]==pluginHandle){midKey=key}});console.log("====");console.log(midKey);console.log("====");if(midKey){Venus.attachMediaStream($("#"+video_dict[midKey]).get(0),stream)}},oncleanup:function(e){}})}}();