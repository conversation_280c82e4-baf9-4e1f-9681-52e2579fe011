import { createApp } from 'vue'
import ElementPlus from 'element-plus'
import 'element-plus/dist/index.css'
import router from './router'
import App from './App.vue'
import * as ElementPlusIconsVue from '@element-plus/icons-vue'
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'
import './globalFunction.ts'
//import BaiduMap from 'vue-baidu-map-3x';
import numberGrow from '@/directives/numberGrow' // 导入数字增长动画自定义指令
import 'animate.css'
import zhCn from 'element-plus/es/locale/lang/zh-cn' //1 引入中文
import '@/assets/font/font.css'
import '@/assets/css/style.css'
import '@/assets/global.scss'
import "@/assets/css/toms.css"
import "@/utils/flexible"

/**BPMN*/
import 'bpmn-js/dist/assets/diagram-js.css'
import 'bpmn-js/dist/assets/bpmn-font/css/bpmn-embedded.css'
import 'bpmn-js-properties-panel/dist/assets/bpmn-js-properties-panel.css'

import SVGIcon from '@/pages/monitor/common/SVGIcon.vue'
import 'virtual:svg-icons-register'

const app = createApp(App)
const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

for (const [key, component] of Object.entries(ElementPlusIconsVue)) {
  app.component(key, component)
}
app.use(numberGrow) // 使用数字增长动画自定义指令
app.use(ElementPlus, { locale: zhCn })
app.use(router)
app.use(router)
app.use(pinia)
app.component('SVGIcon', SVGIcon);
// app.use(BaiduMap, {
//   // ak 是在百度地图开发者平台申请的密钥 详见 http://lbsyun.baidu.com/apiconsole/key */
//   ak: '2LbCc00KLeHJZeYj9zchjmMDES9b6GYp',
//   // v:'2.0',  // 默认使用3.0
//   //type: 'WebGL' // ||API 默认API  (使用此模式 BMap=BMapGL)
// });

app.mount('#app')

app.config.globalProperties.$pageNumber = 1
app.config.globalProperties.$pageSize = 10
