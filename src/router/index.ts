import { createRouter, createWebHistory } from 'vue-router'
import { isNewVersion } from '@/utils/version.ts'

import { sys } from './sys'


const router = createRouter({
    history: createWebHistory('/tocc/'),
    routes: [
        {
            path: '/',
            name: 'monitoring',
            meta: { title: '综合监测' },
            component: () => import('@/pages/monitor/LayOut/index.vue'),
            // 如果启动v2，取消该行注释
            // redirect: '/home',
        },
        // v2版新布局路由，分而治之，
        {
            path: '/aviation-railway',
            component: () => import('@/views/layout/main/index.vue'),
            children: [
                {
                    // name: 'aviation',
                    path: 'aviation',
                    component: () => import('@/views/aviation-railway/aviation/index.vue')
                },
                {
                    path: '/aviation-railway/railway',
                    component: () => import('@/views/aviation-railway/railway/index.vue')
                },
            ]
        },
        {
            path: '/road-transport',
            component: () => import('@/views/layout/main/index.vue'),
            children: [
                {
                    path: '/road-transport/dangerous',
                    component: () => import('@/views/road-transport/dangerous-goods/index.vue')
                },
                {
                    path: '/road-transport/goods',
                    component: () => import('@/views/road-transport/goods/index.vue')
                },
                {
                    path: '/road-transport/passenger',
                    component: () => import('@/views/road-transport/passenger/index.vue')
                }
            ]
        },
        {
            path: '/city-traffic',
            component: () => import('@/views/layout/main/index.vue'),
            children: [
                {
                    path: '/city-traffic/overview',
                    component: () => import('@/views/city-traffic/overview/index.vue')
                },
                {
                    path: '/city-traffic/bus',
                    component: () => import('@/views/city-traffic/bus/index.vue')
                },
                {
                    path: '/city-traffic/parade-car',
                    component: () => import('@/views/city-traffic/parade-car/index.vue')
                },
                {
                    path: '/city-traffic/online-taix',
                    component: () => import('@/views/city-traffic/online-taix/index.vue')
                },
                {
                    path: '/city-traffic/suspected-black-car',
                    component: () => import('@/views/city-traffic/suspected-black-car/index.vue')
                }
            ]
        },
        {
            path: '/road-network',
            component: () => import('@/views/layout/main/index.vue'),
            children: [
                {
                    name: 'road-overview',
                    path: '/road-network/overview',
                    component: () => import('@/views/road-network/overview/index.vue')
                },
                {
                    name: 'road-maintain',
                    path: '/road-network/maintain',
                    component: () => import('@/views/road-network/maintain/index.vue')
                }
            ]
        },
        ...sys
    ]
})

router.beforeEach(async (to, from, next) => {
    // 在路由切换时候进行判断版本号是否更新 提示用户刷新页面
    isNewVersion()
    next()
})
export default router
