/*综合监测*/

export const sys = [{
    path: '/sys',
    component: () => import('@/views/layout/sys/index.vue'),
    children: [
        {
            path: 'unit',
            component: () => import('@/views/sys/Unit.vue')
        },
        {
            path: 'user',
            component: () => import('@/views/sys/User.vue')
        },
        {
            path: 'role',
            component: () => import('@/views/sys/Role.vue')
        },
        {
            path: 'menu',
            component: () => import('@/views/sys/Menu.vue')
        },
        {
            path: 'log',
            component: () => import('@/views/sys/Log.vue')
        },
        {
            path: 'dictionary',
            component: () => import('@/views/sys/Dictionary.vue')
        },
        {
            path: 'setting',
            component: () => import('@/views/sys/Setting.vue')
        },
        {
            path: 'monitor',
            component: () => import('@/views/sys/Monitor.vue')
        },
        {
            path: 'pageFlex',
            component: () => import('@/views/sys/PageFlex.vue')
        },
        {
            path: 'pageComponent',
            component: () => import('@/views/sys/PageComponent.vue')
        },
        {
            path: 'settingDm',
            component: () => import('@/views/sys/SettingDm.vue')
        },
        {
            path: 'fileInfo',
            children: [
                { path: '', component: () => import('@/views/sys/FileInfo/FileInfo.vue') },
                {
                    path: 'edit/:id?',
                    component: () => import('@/views/sys/FileInfo/EditFileInfo.vue')
                },
                {
                    path: 'view/:id?',
                    component: () => import('@/views/sys/FileInfo/EditFileInfo.vue')
                }
            ]
        }
    ]
}]
