//sass常用混合器(应该合理使用)
// 弹性布局混合器
@mixin flexMixer($justify: center, $align: center, $flex: row) {
	// 申明为弹性盒子
	display: flex;
	// 排列方式
	flex-direction: $flex;
	// 主（副）轴的对齐方式
	justify-content: $justify;
	// 主（副）轴的对齐方式
	align-items: $align;
	// 边框盒
	box-sizing: border-box;
}

// 定位混合器
@mixin positionMixer($position: static, $left: auto, $top: auto, $right: auto, $bottom: auto) {
	position: $position;
	left: $left;
	top: $top;
	right: $right;
	bottom: $bottom;
}

// 浮动混合器
@mixin floatMixer($float: left) {
	float: $float;
}

// 设置背景混合器
@mixin bgMixer($url, $position: center center, $size: 100% 100%, $repeat: no-repeat) {
	background: $url $repeat $position;
	background-size: $size;
}

// 宽高混合器
@mixin widthMixer($width: 100px, $height: 100px) {
	width: $width;
	height: $height;
	box-sizing: border-box;
}

// 文字混合器
@mixin fontMixer($fontSize: 16px, $color: #515a6e, $weight: 500, $font-familyt: Source Han Sans SC) {
	font-family: $font-familyt;
	font-size: $fontSize;
	font-weight: $weight;
	color: $color;
}

// 多行字体省略混合器
@mixin pointMixer($line: 2) {
	display: -webkit-box; //必须结合的属性 ，将对象作为弹性伸缩盒子模型显示 。
	word-break: break-all;
	-webkit-line-clamp: $line;
	line-clamp: $line;
	overflow: hidden;
	text-overflow: ellipsis; //可以用来多行文本的情况下，用省略号“…”隐藏超出范围的文本 。
	white-space: normal;
	/* autoprefixer: off */
	-webkit-box-orient: vertical; //必须结合的属性 ，设置或检索伸缩盒对象的子元素的排列方式 。
	/* autoprefixer: on */
}

// 卡片混合器
@mixin cardMixer($width: 100px, $height: 100px, $background: white, $radius: 20px) {
	width: $width;
	height: $height;
	background: $background;
	border-radius: $radius;
}

// 宽高相同带圆角图片
@mixin radiusImg($size: 44px, $radius: 15px) {
	width: $size;
	height: $size;
	border-radius: $radius;
}

// 宽高相同圆
@mixin radius($size: 44px, $radius: 50%) {
	width: $size;
	height: $size;
	border-radius: $radius;
}

// 默认水平线
@mixin lineMixin($width: 100%, $height: 1px, $color: #eeeeee) {
	width: $width;
	height: $height;
	background: $color;
}

// 隐藏滚动条
@mixin hiddenScroll() {
	/* Firefox */
	scrollbar-width: none;
	/* Chrome Safari */
	&::-webkit-scrollbar {
		display: none;
	}
}

// 圆混入
@mixin radiusMixin($size: 10px) {
	border-width: 1px;
	border-radius: $size;
}

// 网格布局 https://blog.csdn.net/weixin_43334673/article/details/108879115
@mixin GridMixin($gap: 0px, $rows: auto, $columns: auto, $areas: auto) {
	display: grid;
	// 网格间距
	gap: $gap;
	// 网格行布局
	grid-template-rows: $rows;
	// 网格列布局
	grid-template-columns: $columns;
	// 网格区域布局
	grid-template-areas: $areas;
}

// 网格样式
@mixin GridStyleMixin($justify-items: center, $align-items: center, $justify-content: center, $align-content: center, $grid-auto-flow: row) {
	// 网格布局流
	grid-auto-flow: $grid-auto-flow;
	//  start | end | center | stretch
	// 网格每个容器的水平、垂直对齐
	place-items: $align-items $justify-items;
	// 网格整体内容的水平、垂直对齐
	place-content: $align-content $justify-content;
}

// 网格项样式
@mixin GridSelfMixin($row: auto, $column: auto, $justify-self: auto, $align-self: auto) {
	// 网格跨行
	grid-row: $row;
	// 网格跨列
	grid-column: $column;
	// 网格项垂直水平布局方式
	place-self: $align-self $justify-self;
}
