/**弹窗UI设计CSS*/
.tip-background-body {
  overflow: hidden;
  padding: 1rem 0rem 0rem 0rem;
}

.tip-background-content {
  background-image: url('@/assets/images/common/tip-background-body.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 100%;
}

.tip-warn-background-content {
  background-image: url('@/assets/images/common/tip-warn-background-body.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  width: 100%;
  height: 100%;
  padding: 2.7rem 0rem 0rem 0rem;
}

.tip-border {
  padding-top: 2.5rem;
  position: relative;
  display: flex;
  flex-direction: column;
  width: 20rem;
  height: 24rem;
}

.tip-warn-border {
  padding-top: 0rem;
}

.tip-border .tip-title {
  display: inline-block;
  top: 0;
  width: 100%;
  height: 2.875rem;
  font-size: 1.125rem;
  line-height: 1.25rem;
  position: absolute;
  padding-top: 0.875rem;
  padding-left: 1.5rem;
  color: #ffffff;
  text-align: left;
}

.tip-border .tip-warn-title {
  padding-left: 2.5rem;
}

.tip-background-title {
  background-image: url('@/assets/images/common/tip-background-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.tip-warn-background-title {
  background-image: url('@/assets/images/common/tip-warn-background-title.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  margin-left: 0.4rem;
}

.tip-remove {
  background-image: url('@/assets/images/common/tip-remove.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 0.9rem;
  right: 0.9rem;
  z-index: 11;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
}

.tip-warn-remove {
  background-image: url('@/assets/images/common/tip-warn-remove.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 1rem;
  right: 1.7rem;
  z-index: 11;
  width: 1rem;
  height: 1rem;
  cursor: pointer;
}

.tip-padding {
  margin: 0.5rem 0.5rem;
  line-height: 1.6rem;
  color: #c9e3ff;
}

.tip-warn-background-content .tip-padding{
  font-size: 1rem;
  color: #FFFFFF !important;
}

.tip-item {
  font-size: 1rem;
  color: #dedfe4;
}

.tip-link {
  border-bottom: 1px solid #0385ff;
  cursor: pointer;
  color: #0385ff;
}

.tip-link:hover {
  color: #f56c6c; /* 鼠标悬停时的颜色 */
  border-bottom: 1px solid #f56c6c;
}

.tip-item-title {
  /* border-left: #3e82ff solid 0.25rem; */
  position: relative;
  padding-left: 0.625rem;
  font-weight: bold;
  margin-top: 0.2rem;
  color: #ffffff;
}

.tip-item-title::before {
  content: '';
  /* 伪元素需要的属性 */
  position: absolute;
  /* 绝对定位，相对于父div */
  left: 0;
  /* 竖线的水平位置 */
  top: 25%;
  /* 竖线的垂直位置 */
  height: 50%;
  /* 竖线的高度 */
  width: 0.25rem;
  /* 竖线的宽度 */
  background: #3e82ff;
  /* 竖线的颜色 */
}

.tip-warn-icon {
  background-image: url('@/assets/images/common/tip-warn-icon.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 0.9rem;
  left: 1.2rem;
  z-index: 11;
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
}

.tip-warn-item-title {
  /* border-left: #3e82ff solid 0.25rem; */
  position: relative;
  padding-left: 0.625rem;
  font-size: 1rem;
  font-weight: bold;
  margin-top: 0.2rem;
  color: #FFFFFF;
}

.tip-warn-item-title::before {
  content: ''; /* 伪元素需要的属性 */
  position: absolute; /* 绝对定位，相对于父div */
  left: 0; /* 竖线的水平位置 */
  top: 25%; /* 竖线的垂直位置 */
  height: 50%; /* 竖线的高度 */
  width: 0.25rem; /* 竖线的宽度 */
  background: #ff7953; /* 竖线的颜色 */
}

.tip-warn-icon {
  background-image: url('@/assets/images/common/tip-warn-icon.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  position: absolute;
  top: 0.9rem;
  left: 1.2rem;
  z-index: 11;
  width: 1.25rem;
  height: 1.25rem;
  cursor: pointer;
}

.tip-warn-item-title {
  /* border-left: #3e82ff solid 0.25rem; */
  position: relative;
  padding-left: 0.625rem;
  font-weight: bold;
  margin-top: 0.2rem;
  color: #dedfe4;
}

.tip-warn-item-title::before {
  content: ''; /* 伪元素需要的属性 */
  position: absolute; /* 绝对定位，相对于父div */
  left: 0; /* 竖线的水平位置 */
  top: 25%; /* 竖线的垂直位置 */
  height: 50%; /* 竖线的高度 */
  width: 0.25rem; /* 竖线的宽度 */
  background: #ff7953; /* 竖线的颜色 */
}

.tip-down-arrow-img {
  height: 2.5rem;
  width: 2.5rem;
  background-image: url('@/assets/images/common/tip-down-arrow.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  transition: transform 0.3s ease;
}
