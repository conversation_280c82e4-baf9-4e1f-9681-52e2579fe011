/**平台级通用样式*/
.crud-main {
  margin: 14px 14px;
}

.ellipsis-text {
  white-space: nowrap;
  /* 禁止文本换行 */
  overflow: hidden;
  /* 隐藏超出容器的内容 */
  text-overflow: ellipsis;
  /* 在超出时显示省略号 */
}
.crud-main-query {
  background: white;
  border-radius: 4px;
  padding: 20px 14px 2px 14px;
  margin-bottom: 16px;
}

.crud-query-form-inline .el-input {
  --el-input-width: 242px;
}

.crud-query-form-inline .el-select {
  --el-select-width: 242px;
}

.crud-main-content {
  background: white;
  border-radius: 4px;
  padding: 6px 10px 6px 10px;
}

.crud-main-content-toolbar {
  text-align: left;
  background: white;
  padding: 8px 0;
  margin-bottom: 6px;
}

.crud-main-content-pagination {
  margin-top: 10px;
  margin-bottom: 4px;
}

.query-form-inline .el-input {
  --el-input-width: 242px;
}

.query-form-inline .el-select {
  --el-select-width: 242px;
}

.crud-form-inline .el-input {
  --el-input-width: 272px;
}

.crud-form-inline .el-select {
  --el-select-width: 272px;
}

.crud-form-inline .el-radio-group {
  width: 272px;
}

.main-form-dialog {
  color: white;
}

.tocc-table-header-cell {
  font-weight: 100;
  color: #303133;
  --el-table-header-bg-color: #f2f6fc;
}

.tocc-table-header-row {
  height: 50px;
}

.tocc-table-row {
  height: 46px;
}

.chart-container-200 {
  height: 200px;
  width: 100%;
}

.chart-container-250 {
  height: 250px;
  width: 100%;
}

.chart-container-300 {
  height: 300px;
  width: 100%;
}

.chart-container-350 {
  height: 350px;
  width: 100%;
}

.chart-container-400 {
  height: 400px;
  width: 100%;
}
