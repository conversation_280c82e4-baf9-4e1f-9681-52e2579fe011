.flex-box {
    display: flex;
    flex-wrap: wrap;
    justify-content: space-between;
}

.search-bar {
    padding-top: 10px;
}

.search-bar-input {
    width: 11.8rem;
    margin-right: 10px
}

.search-bar-button-reset {
    background-color: #165DFF;
    color: #FFFFFF;
}

.search-bar-button-area {
    background-color: #FF7D00;
    color: #FFFFFF;
}

.search-result {
    margin-top: 16px;
}

.el-tree-box {
    max-width: 600px;
    color: #1D2129;
    font-size: var(--font-size-level3);
    padding: 0
}

.circle-online {
	display: inline-block;
	width: 10px;
	height: 10px;
	border-radius: 50%;
	margin-right: 5px;
    background: #25C343;
}

.circle-offline {
	display: inline-block;
	width: 10px;
	height: 10px;
	border-radius: 50%;
	margin-right: 5px;
    background: #e5e6ec
}

/** 折叠面板 */
.el-collapse-box {
    padding: 0 0;
    .item-info {
        display: flex;
        align-items: center;
        font-size: var(--font-size-level3);
        .item-icon {
            margin-right: 10px
        }
    }
}

/**预警列表*/
.warning-table {
    height: 42px;
    line-height: 42px;
    width: 100%;

    .plate,
    .name,
    .rank,
    .time {
        display: inline-block;
        width: 25%;
        text-align: center;
        font-size: 14px;
        color: #1D2129
    }
}

.item-title-right {
    font-family: var(--font-family-normal);
    font-weight: 500 !important;
    cursor: pointer !important;
    font-size: 16px !important;
    color: #165dff !important;
    padding: 4px 12px !important;
    background: rgba(22, 93, 255, 0.1) !important;
    border-radius: 4px !important;
    transition: all 0.2s ease !important;
    display: inline-flex !important;
    align-items: center !important;
    white-space: nowrap !important;
    border: none !important;
}

/* 取消更多按钮的hover效果 */
.more:hover {
    background: rgba(22, 93, 255, 0.1) !important;
    box-shadow: none !important;
}

/** echart图表相关联**/
.echart-container {
  width: 100%;
  height: 100%;
}