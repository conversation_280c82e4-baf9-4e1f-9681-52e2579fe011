//公共
:root {
  font-family: HarmonyOS_Sans; //设置全局默认 字体
  --font-family-normal: HarmonyOS_Sans, HarmonyOS_Sans;
  --font-family-number-normal: Arial, Helvetica, sans-serif; //数字字体
  text-rendering: geometricPrecision;
  //  文字size
  --project-title-font: 26px; //项目标题
  --menu-item-font-1: 18px; //一级菜单
  --menu-item-font-2: 16px; //二级菜单
  --text-size: 14px; //正文
  --auxiliary-font: 12px; //辅助文字

  --font-size-level1: 20px;
  --font-size-level2: 18px;
  --font-size-level3: 16px;
  --font-size-level4: 14px;

  //文字颜色
  --project-title-cololr: #ffffff; //项目标题文字颜色
  //  圆角
  --radius-card: 4px;
  --radius-normal: 2px;


}

.light {
  // 主bar背景颜色
  --bg-main-bar: linear-gradient(270deg, #3196ff 0%, #165dff 100%);
  //   主色
  --color-brand-1: #oe42d2; //主色 - 点击
  --color-brand-2: #oe42d2; //主色 - 常规
  --color-brand-3: #oe42d2; //主色 -悬浮
  --color-brand-4: #oe42d2; //主色 - 特殊场景
  //状态颜色
  --el-color-success-click: #009a29;
  //  线条色
  --color-line-normal: #e5e6eb;
  --color-line-deep: #c9cdd4;
  //  填充 背景颜色
  --bg-fill-1: #f7f8fa; //浅、禁用
  --bg-fill-2: #f2f3f5; // 一般/常规/白色悬浮
  --bg-fill-3: #e5e6e8; //深/会滴悬浮
  --bg-fill-4: #c9cdd4; //重/特殊场景
  --bg-fill-5: #4e5969; //强调/图标/特殊场景
  //  文字颜色
  --color-text-emphasize1: #1d2129; //强调
  --color-text-emphasize2: #4e5969; //次强调
  --color-text-minor: #86909c; //次要信息
  --color-text-gray: #c9cdd4; //置灰信息
  --left-menu-item-color: #165dff; //左侧二级菜单文字
  --left-menu-item-active-color: #ffffff; //左侧二级菜单文字
  --tabs-color: #86909c; //tabs文字颜色
  --tabs-active-hover-color: #86909c; //tabs hover&active 文字颜色
  --more-btn-color: #165dff; //更多按钮文字颜色
  //边框颜色
  --tabs-item-bottom-color: #165dff; //tab栏底部边框颜色
  --table-item-border-color: #E5E6EC; //表格边框颜色
  //  成功
  --color-success-1: #009a29; //点击
  --color-success-2: #00b42a; //常规
  --color-success-3: #23c343; //悬浮
  --color-success-4: #4cd263; //特殊场景
  --color-success-5: #7be188; //禁用
  --color-success-6: #aff0b5; //特殊场景
  --color-success-7: #e8ffea; //浅色背景
  //警告
  --color-warning-1: #d25f00; //点击
  --color-warning-2: #ff7d00; //常规
  --color-warning-3: #ff9a2e; //悬浮
  --color-warning-4: #ffb65d; //特殊场景
  --color-warning-5: #ffcf8b; //禁用
  --color-warning-6: #ffe5ba; //特殊场景
  --color-warning-7: #fff7e8; //浅色背景
  //链接
  --color-link-1: #0e42d2; //点击
  --color-link-2: #165dff; //常规
  --color-link-3: #4080ff; //悬浮
  --color-link-4: #6aa1ff; //特殊场景
  --color-link-5: #94bfff; //禁用
  --color-link-6: #bedaff; //特殊场景
  --color-link-7: #e8f3ff; //浅色背景

}

.dark {

}
