// Sizes
.w-full {
  width: 100% !important;
}
.h-full {
  height: 100%;
}
.h-fit {
  height: fit-content;
}
.h-fit\! {
  height: fit-content !important;
}
.w-1 {
  width: var(--spacing);
}
.w-12 {
  width: calc(var(--spacing) * 12);
}
.w-40 {
  width: calc(var(--spacing) * 40);
}
.w-40\! {
  width: calc(var(--spacing) * 40) !important;
}

// Positions & Z Indexes
.absolute {
  position: absolute;
}
.right-0 {
  right: 0;
}
.z-1 {
  z-index: 1;
}

// Margin & Paddings
.m-5 {
  margin: calc(var(--spacing) * 5);
}
.ml-25 {
  margin-left: calc(var(--spacing) * 25);
}
.p-1 {
  padding: var(--spacing);
}
.p-7 {
  padding: calc(var(--spacing) * 7);
}
.pb-0 {
  padding-bottom: 0;
}
.px-3 {
  padding-left: calc(var(--spacing) * 3);
  padding-right: calc(var(--spacing) * 3);
}
.py-3\.5 {
  padding: calc(var(--spacing) * 3.5) 0;
}

// Borders
.border-0 {
  border: 0;
}

// Rounded
.rounded-sm {
  border-radius: 0.125rem;
}
.rounded {
  border-radius: 0.25rem;
}
.rounded-md {
  border-radius: 0.375rem;
}
.rounded-lg {
  border-radius: 0.5rem;
}
.rounded-xl {
  border-radius: 0.75rem;
}
.rounded-2xl {
  border-radius: 1rem;
}

// Flexible layouts
.flex {
  display: flex;
}
.flex-1 {
  flex: 1;
}
.flex-init {
  flex: initial;
}
.flex-col {
  flex-direction: column;
}
.flex-grow {
  flex-grow: 1;
}

// Grid layouts
.grid {
  display: grid;
}
.grid-cols-2 {
  grid-template-columns: repeat(2, 1fr);
}

// Alignments
.align-center {
  align-items: center;
}
.justify-start\! {
  justify-content: start !important;
}
.justify-center {
  justify-content: center;
}
.justify-center\! {
  justify-content: center !important;
}
.justify-end {
  justify-content: end;
}
.justify-between {
  justify-content: space-between;
}
.justify-between\! {
  justify-content: space-between !important;
}

// Gaps
.gap-1 {
  gap: var(--spacing);
}
.gap-2 {
  gap: calc(var(--spacing) * 2);
}
.gap-4 {
  gap: calc(var(--spacing) * 4);
}

// Overflow
.overflow-hidden {
  overflow: hidden;
}

// Text & Fonts
.color-white {
  color: white;
}
.leading-10 {
  line-height: calc(var(--spacing) * 10);
}
