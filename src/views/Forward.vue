<template>
    <div class="container">
        <div class="parent">
            <div class="img-badge" style="height: 100px">
            </div>
            <div class="login-frame" v-loading="loading" element-loading-custom-class="my-custom-loading"
                element-loading-text="正在跳转中..." >
                <div class="fw-titleBar">
                    <el-row class="fw-titleBar-content">
                        <el-col :span="24" style="padding: 56px 0 36px 0; text-align: center">
                            <div class="title">
                                <span>山西省综合交通<br />运输运行监测平台</span>
                            </div>
                        </el-col>
                    </el-row>
                </div>

            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { reactive, nextTick, onMounted, ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSessionStore, type Menu } from '@/store/session'
import { ElLoading } from 'element-plus'
import loginApi from '@/service/login'
import StringUtils from '@/utils/common/StringUtils'

const session = useSessionStore()
const router = useRouter()
const loading = ref(true)

onMounted(async () => {
    loading.value = true
    const params = new URLSearchParams(window.location.search);
    console.log('-------------forward.onMounted-------------------', params, params.get('url'))
    const token = session.token
    if (!StringUtils.isEmpty(token)) {
        router.push({ name: params.get('url') })
    } else {
        session.$patch({
            token: ''
        })
        let temp = {
            ...dataForm,
            username1: dataForm.username
        }
        delete temp.username
        const res = await loginApi.login(temp)
        session.$patch({
            token: res.data.data.token,
            menuTree: res.data.data.menuTree,
            sessionUser: res.data.data.sessionUser
        })
        router.push({ name: params.get('url') })
    }
})


const dataForm = reactive({
    username: 'admin  ',
    password: 'admin123',
    target: 'form'
})
</script>

<style lang="less" scoped>
.container {
    background-image: url('@/assets/images/login/背景.gif');
    background-size: cover;
    /* 设置白色背景 */
    min-height: 100vh;
    /* 确保背景覆盖整个视口高度 */
    /* opacity: 0.7; */
    /* background: rgba(0, 24, 42, 1); */
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
    background-repeat: no-repeat;
    background-position: center;
}

.parent {
    position: relative;
    margin-bottom: 150px;
}

.img-badge {
    position: relative;
    top: 50px;
    z-index: 999;
    display: flex;
    justify-content: center;
}

.fw-titleBar {
    width: 100%;
    display: inline-block;
    position: relative;
    background-image: url('@/assets/images/login/login-frame-bg.png');
    background-size: 100% 100%;
    //background-size: cover;
    //background-repeat: no-repeat;
    background-position: center;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.login-frame {
    position: relative;
    box-sizing: border-box;
    width: 450px;
    height: 200px;
}

.title {
    word-wrap: break-word;
    font-style: normal;
    line-height: 40px;
    letter-spacing: 2px;
    text-align: center;
    font-weight: 500;
    font-size: 28px;
    padding-bottom: 40px;
    color: #d0e3ff;
    position: relative;

    &::before {
        position: absolute;
        content: '';
        width: 63px;
        height: 5px;
        //background-image: url('@/assets/images/login/左-标题@1x.png');
        background-position: center;
        left: 0;
        top: 30px;
        background-size: 100% 100%;
    }

    &::after {
        position: absolute;
        content: '';
        width: 63px;
        height: 5px;
        //background-image: url('@/assets/images/login/右-标题@1x.png');
        background-position: center;
        right: 0;
        top: 30px;
        background-size: 100% 100%;
    }
}

:deep(.el-input__wrapper) {
    background-color: rgba(0, 0, 0, 0);
    background-image: url('@/assets/images/login/输入框@1x.png');
    background-size: 100% 100%;
    background-repeat: no-repeat;
    background-position: center;
    box-shadow: none;
    width: 330px;
    height: 52px;
    padding-left: 50px;
    position: relative;
    font-weight: 400;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    letter-spacing: 1px;
    text-align: left;
    font-style: normal;

    &::before {
        position: absolute;
        content: '';
        width: 30px;
        height: 30px;
        left: 10px;
        background-position: center;
        background-repeat: no-repeat;
    }

    /* border: 1px solid #32c3c5; */
}

:deep(.el-input__wrapper:nth-child(1)::before) {
    background-image: url('@/assets/images/login/账户名@1x.png');
}

:deep(.password-input .el-input__wrapper::before) {
    background-image: url('@/assets/images/login/密码@1x.png');
}

:deep(.el-input__inner) {
    background-color: rgba(0, 0, 0, 0) !important;
    color: #fff;
    font-weight: 400;
    font-size: 14px;
}

:deep(.el-input input:focus) {
    outline: none;
    border-color: none;
    /* 你可以根据需要修改边框颜色 */
    box-shadow: none;
}

:deep(input:-webkit-autofill) {
    -webkit-text-fill-color: #fff;
    transition: background-color 5000s ease-out 0.5s;
}

:deep(input::-webkit-input-placeholder) {
    color: #fff;
}

:deep(.el-input) {
    input {
        box-shadow: none;
    }
}

:deep(.el-button--large) {
    background-image: url('@/assets/images/login/登录@1x.png');
    background-repeat: no-repeat;
    background-size: 100% 100%;
    border: none;
    background-color: transparent;
    width: 330px;
    height: 43px;
    font-weight: 500;
    font-size: 14px;
    color: #ffffff;
    line-height: 20px;
    letter-spacing: 1px;
    text-align: center;

    &:active,
    &:hover {
        opacity: 0.8;
    }

    font-style: normal;
}

.loading {
    color: #ffffff;
    font-size: 12px;
    font-weight: bold;
    display: block;
}

.createLoading {
    .el-loading-spinner {
        top: 50%;
        left: 50%;
        margin-left: -55px;
        background: rgba(0, 0, 0, 0.7);
        padding: 20px;
        border-radius: 4px;
        width: auto;
        text-align: center;
        position: absolute;

        i {
            color: #eee;
        }

        .el-loading-text {
            color: #eee;
        }
    }
}

.my-loading-class {
    background-color: rgba(0, 0, 0, 0.5);
    /* 背景颜色 */
    display: flex;
    justify-content: center;
    align-items: center;
}


</style>

<style>
.my-custom-loading {
    top: 20%;
      left: 0%;
      /* margin-left: -55px; */
  background-color: rgba(0, 0, 0, 0) !important;
}

.my-custom-loading .el-loading-spinner i {
  /* color: #ff0000 !important;
  font-size: 50px !important; */
}

.my-custom-loading .el-loading-text {
  color: #d0e3ff !important;

}
</style>
