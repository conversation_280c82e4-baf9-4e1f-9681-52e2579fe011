<template>
  <div class="container" style="align-items: center">
    <el-row>
      <el-col :span="7"></el-col>
      <el-col :span="10">
        <div
          style="
            margin-top: 120px;
            padding: 30px 0;
            font-size: 40px;
            text-align: center;
            color: white;
          "
        >
          交通运行监测调度中心
        </div>
        <el-row style="background: white; border-radius: 6px; padding: 20px 10px">
          <el-col :span="12" style="padding-left: 20px">
            <el-image
              style="width: 76%; height: 90%"
              src="public/static/base/login-pic.png"
              fit="cover"
            />
          </el-col>
          <el-col :span="12" style="padding: 36px 20px 0 0; text-align: center">
            <el-form label-width="auto" ref="ruleFormRef" :model="dataForm" status-icon>
              <el-form-item prop="username">
                <el-input
                  v-model="dataForm.username"
                  :prefix-icon="User"
                  size="large"
                  placeholder="请输入登录账号"
                />
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  v-model="dataForm.password"
                  :prefix-icon="Lock"
                  size="large"
                  placeholder="请输入登录密码"
                />
              </el-form-item>
              <el-form-item>
                <el-col :span="24" class="text-center">
                  <el-button
                    type="primary"
                    @click="submitForm(ruleFormRef)"
                    size="large"
                    style="width: 100%"
                    >登录系统</el-button
                  >
                </el-col>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>

        <!-- <div style="text-align: center;background: white;border-radius: 6px">
            <el-container>
              <el-main>
                <el-form label-width="auto" ref="ruleFormRef" :model="dataForm" status-icon>
                  <el-form-item prop="username">
                    <el-input v-model="dataForm.username" :prefix-icon="User" size="large" placeholder="请输入登录账号"/>
                  </el-form-item>
                  <el-form-item prop="password">
                    <el-input v-model="dataForm.password" :prefix-icon="Lock" size="large" placeholder="请输入登录密码"/>
                  </el-form-item>
                  <el-form-item>
                      <el-col :span="24" class="text-center">
                        <el-button type="primary" @click="submitForm(ruleFormRef)" size="large">&nbsp;&nbsp;&nbsp;&nbsp;登录系统&nbsp;&nbsp;&nbsp;&nbsp;</el-button>
                      </el-col>
                  </el-form-item>
                </el-form>
              </el-main>
            </el-container>
          </div> -->
      </el-col>
      <el-col :span="7"></el-col>
    </el-row>
  </div>
</template>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage, ElLoading } from 'element-plus'
import { Lock, User } from '@element-plus/icons-vue'
import { useUserSessionStore } from '@/store/user'
import axios from 'axios'

import type { FormInstance, FormRules } from 'element-plus'

const userSession = useUserSessionStore()

const router = useRouter()

// do not use same name with ref
const dataForm = reactive({
  username: 'admin',
  password: 'admin123'
})

interface RuleForm {
  username: string
  password: string
}

const ruleFormRef = ref<FormInstance>()

// TODO 这个变量未引用
// eslint-disable-next-line
const rules = reactive<FormRules<RuleForm>>({
  username: [
    {
      required: true,
      message: '用户名称不能为空',
      trigger: 'blur'
    }
  ],
  password: [
    {
      required: true,
      message: '用户密码不能为空',
      trigger: 'blur'
    }
  ]
})

const submitForm = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate((valid) => {
    if (valid) {
      if (dataForm.username == null || dataForm.username == '') {
        ElMessage({
          message: '请输入登录账号',
          type: 'warning'
        })
        return
      }
      if (dataForm.password == null || dataForm.password == '') {
        ElMessage({
          message: '请输入登录密码',
          type: 'warning'
        })
        return
      }

      const loadingInstance = ElLoading.service({ fullscreen: true })
      axios
        .post(
          `/apiV1/user/login`,
          {
            name: dataForm.username,
            pwd: dataForm.password
          },
          {
            headers: { 'Content-Type': 'application/json' }
          }
        )
        .then(function (response) {
          console.log(response.data)
          loadingInstance.close()
          router.push('/main/user')
        })
        .catch((error) => {
          loadingInstance.close()
          ElMessage({
            message: error.message,
            type: 'error'
          })
        })
    }
  })
}

// eslint-disable-next-line
const onSubmit = () => {
  console.log('submit!')
  console.log('name', dataForm.username)
  // axios.get("http://127.0.0.1:4523/m1/4855312-4510795-default/api/login").then(function(response){
  //   console.log('------>', response.data);
  // });

  axios
    .post('http://127.0.0.1:4523/m1/4855312-4510795-default/api/login', {
      username: 'Fred',
      password: 'Flintstone'
    })
    .then(function (response) {
      console.log(response.data)
    })
    .catch(function (error) {
      console.log(error)
    })
  userSession.username = dataForm.username
  userSession.rolename = '管理员'
  //router.push('/main');
}
</script>

<style scoped>
.container {
  background-color: #0c588f; /* 设置白色背景 */
  min-height: 100vh; /* 确保背景覆盖整个视口高度 */
  /* opacity: 0.7; */
  /* background: rgba(0, 24, 42, 1); */
}
</style>
