<template>
  <div class="common-layout">
    <el-container>
      <el-header class="header">
        <el-menu
          :default-active="menuIndex"
          background-color="transparent"
          mode="horizontal"
          text-color="#ffffff"
          active-text-color="#ffffff"
          :ellipsis="false"
        >
          <div class="net-name">
            <img src="@/assets/images/layout/icon.png" alt="" />
            <span>忻州智慧交通综合管理平台</span>
          </div>

          <el-menu-item
            class="menu-item"
            v-for="(item, index) in menuItems"
            :key="item.id"
            :index="index"
            @click="menuItemClick(item, index)"
          >
            {{ item.name }}
          </el-menu-item>
        </el-menu>
        <div class="user-header">
          <div class="noice">
            <!-- <el-badge :value="12" class="badge-item">
              <SVGIcon name="bell" :color="'#ffffff'" />
            </el-badge>
            <el-icon color="#fff">
              <SVGIcon name="classification" :color="'#ffffff'" />
            </el-icon> -->
            <div class="header-img">
              <img src="@/assets/images/layout/default-header.png" alt="12" />
            </div>
            <el-dropdown trigger="click" @visible-change="chang_dropdown_handel">
              <span class="el-dropdown-link">
                {{ '系统管理员' }}
                <el-icon v-if="arrowIcon" class="icon-right"><caret-top /></el-icon>
                <el-icon v-else class="icon-right"><caret-bottom /></el-icon>
              </span>
              <template #dropdown>
                <el-dropdown-menu>
                  <el-dropdown-item ><span class="setting" @click="gotoSetting">系统设置</span></el-dropdown-item>
                  <el-dropdown-item ><span class="setting">系统退出</span></el-dropdown-item>
                </el-dropdown-menu>
              </template>
            </el-dropdown>
          </div>
        </div>
      </el-header>
      <el-container>
        <el-aside width="4rem" style="padding: 0">
          <div class="content">
            <div class="left-menu">
              <div
                class="left-menu-item"
                v-for="(item, index) in menuItems[menuIndex].children"
                :key="item.id"
                :class="menuItemIndex === Number(index) ? 'left-menu-item-active' : ''"
                @click="gotoView(item.path, index)"

              >
                <!-- <img :src="menuItemIndex === Number(index) ? item.activeIcon : item.icon"/> -->
                <SVGIcon
                  :name="item.icon"
                  :color="menuItemIndex === Number(index) ? '#FFFFFF' : '#165DFF'"
                />
                <span>{{ item.name }}</span>
              </div>
            </div>
          </div>
        </el-aside>
        <el-main style="background-color: azure; padding: 0; position: relative">
          <router-view />
          <div class="map-locate">
            <BmapBaseContainer ref="bmapBaseContainer" :map-type="MAP_TYPE.BASE" />
          </div>
        </el-main>
      </el-container>
    </el-container>
  </div>
</template>

<script lang="ts" setup>
import { useRouter } from 'vue-router'
import { useRoute } from 'vue-router'
import { onMounted, ref, shallowRef, watch } from 'vue'
import { getMenuIcon } from '@/utils/common/PublicUtils'
// import SVGIcon from '@/pages/monitor/common/SVGIcon.vue'
import BmapBaseContainer from '@/pages/map/BmapBaseContainer.vue'
import { MAP_TYPE } from '@/constant/map'

const router = useRouter()
const route = useRoute()

const menuIndex = ref(5)
const menuItemIndex = ref(0)
const arrowIcon = ref(false)
const menuItemClick = (menu: MenuItem, index: number) => {
  menuIndex.value = index
  gotoView(menu.children![0].path!, 0)
}

const gotoView = (path: string, index: number) => {
  router.push(path)
  menuItemIndex.value = index
}

interface MenuItem {
  id: string
  name: string
  icon?: string
  activeIcon?: string
  /**选中图标 */
  path?: string
  selected?: boolean
  children?: MenuItem[]
}

const menuItems: MenuItem[] = [
  {
    id: '1',
    name: '综合监测'
  },
  {
    id: '2',
    name: '公路路网',
    children: [
      {
        id: '2-1',
        name: '路网概况',
        icon: '',
        path: '/road-network/overview'
      },
      {
        id: '2-2',
        name: '养护',
        icon: '',
        path: '/road-network/maintain'
      },
      {
        id: '2-3',
        name: '事件',
        icon: '',
        path: ''
      },
      {
        id: '2-4',
        name: '建设',
        icon: '',
        path: ''
      },
      {
        id: '2-5',
        name: '设施',
        icon: '',
        path: ''
      }
    ]
  },
  {
    id: '3',
    name: '道路运输',
    children: [
      {
        id: '3-1',
        name: '客运',
        icon: 'passenger',
        path: '/road-transport/passenger'
      },
      {
        id: '3-2',
        name: '危货运',
        icon: 'dangerous',
        path: '/road-transport/dangerous'
      },
      {
        id: '3-3',
        name: '货运',
        icon: 'goods',
        path: '/road-transport/goods'
      }
    ]
  },
  {
    id: '4',
    name: '城市客运',
    children: [
      {
        id: '4-1',
        name: '总体概述',
        icon: 'a-zongtigaikuang1x',
        path: '/city-traffic/overview'
      },
      {
        id: '4-2',
        name: '公交车',
        icon: 'a-gongjiaoche1x',
        path: '/city-traffic/bus'
      },
      {
        id: '4-3',
        name: '巡游车',
        icon: 'a-xunyouche1x',
        path: '/city-traffic/parade-car'
      },
      {
        id: '4-4',
        name: '网约车',
        icon: 'a-wangyueche1x',
        path: '/city-traffic/online-taix'
      },
      {
        id: '4-5',
        name: '疑似黑车',
        icon: 'a-yisiheiche1x',
        path: '/city-traffic/suspected-black-car'
      }
    ]
  },
  {
    id: '5',
    name: '在建交通'
  },
  {
    id: '6',
    name: '民航铁路',
    children: [
      {
        id: '6-1',
        name: '民航',
        icon: 'aviation',
        path: '/aviation-railway/aviation'
      },
      {
        id: '6-2',
        name: '铁路',
        icon: 'railway',
        path: '/aviation-railway/railway'
      }
    ]
  }
]

onMounted(() => {
  console.log('-------------layout.main.index-----------------', menuItems)
  initMap()
})

const bmapBaseContainer = ref<InstanceType<typeof BmapBaseContainer>>()
const map = shallowRef<any>(null)
const initMap = async () => {
  map.value = await bmapBaseContainer.value?.initMap()
}

watch(
  () => route.path,
  (newPath, oldPath) => {
    bmapBaseContainer.value?.resetMap()
  }
)
/*
 * 当菜单出现消失时 触发的方法 控制箭头方向
 * */
const chang_dropdown_handel = (val: boolean) => {
  arrowIcon.value = val
}

function gotoSetting() {
  router.replace({ path: '/sys/user' })
}
</script>

<style>
.header {
  height: 3.8rem;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background: var(--bg-main-bar);

  .user-header {
    margin-right: 20px;

    .noice {
      display: flex;
      align-items: center;

      .badge-item {
        margin: 5px 15px 0 0;
      }
    }

    .header-img {
      width: 32px;
      height: 32px;
      border: 1px solid #fff;
      padding: 2px;
      border-radius: 50%;
      margin: 0 8px;

      img {
        width: 100%;
        height: 100%;
        border-radius: 50%;
      }
    }

    .el-dropdown-link {
      display: flex;
      align-items: center;
      color: #fffeef;
      font-size: var(--menu-item-font-2);
      cursor: pointer;

      .icon-right {
        margin-left: 5px;
      }
    }


  }
}

.el-menu {
  border-bottom: none !important;
  height: 100%;
}

.el-menu-item:hover {
  background: rgba(255, 255, 255, 0.1) !important;
}

.net-name {
  height: 100%;
  display: flex;
  align-items: center;
  font-weight: 500;
  color: var(--project-title-cololr);
  font-size: var(--project-title-font);
  letter-spacing: 1px;
  margin-right: 150px;
  img {
    width: 36px;
    height: 36px;
    margin: 0 14px 0 14px;
  }
}

.content {
  width: 100%;
  height: calc(100vh - 3.8rem);
  display: flex;

  .left-menu {
    width: 100%;
    background: #ffffff;
    height: 100%;

    .left-menu-item {
      width: 100%;
      height: 63px;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      font-size: var(--menu-item-font-2);
      color: var(--left-menu-item-color);
      cursor: pointer;
    }

    .left-menu-item-active {
      background: #165dff;
      color: var(--left-menu-item-active-color);
    }
    .left-menu-item:hover {
      background: rgba(24,98,255,.05);
    }
    .left-menu-item-active:hover {
      background: #165dff;
      color: var(--left-menu-item-active-color);
    }
  }
}

.menu-item {
  font-size: var(--menu-item-font-1);
  padding-top: 5px;
}

.el-menu-item.is-active {
  color: #6681fa;
  background-color: rgba(29, 108, 255, 0.14); /* 设置选中项的背景颜色 */
}

.map-locate {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.setting{
  font-size: var( --font-size-level3);
  padding: 0 5px;
}
</style>
