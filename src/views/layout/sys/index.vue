<template>
  <el-container class="main-layout">
    <el-header style="background: #0c588f; padding: 0">
      <el-row style="height: 100%">
        <el-col :span="20">
          <div
            style="
              height: 100%;
              align-items: center;
              display: flex;
              font-size: 28px;
              color: white;
              padding-left: 4px;
              cursor: pointer;
            "
            @click="goHome"
          >
            <el-image style="width: 68px; height: 60px" :src="logoImg" />
            {{ session.sessionUser?.sysSettingMap?.title }}
          </div>
        </el-col>
        <el-col :span="4">
          <!-- <div style="height: 100%;align-items: center;display: flex;font-size: 16px">{{ userSession.username }} | {{ userSession.rolename }}</div> -->
          <el-row :gutter="0" style="min-height: 100%; color: white" justify="end">
            <el-col :span="8" class="header-toolbar-link nav-action-item">
              <span style="display: flex" @click="goHome">
                {{ session.sessionUser?.user?.unit?.uname }}
              </span>
            </el-col>
            <el-col :span="8" class="header-toolbar-link nav-action-item">
              <el-dropdown trigger="hover" style="padding-top: 0">
                <span class="flex align-center gap-2" style="color: white; font-size: 14px">
                  {{ session.sessionUser?.user?.userName }}
                  <el-icon>
                    <ArrowDown />
                  </el-icon>
                </span>
                <template #dropdown>
                  <el-dropdown-menu>
                    <el-dropdown-item icon="Setting" @click="openSetting()"
                      >个人设置</el-dropdown-item
                    >
                    <el-dropdown-item icon="SwitchButton" divided @click="loginOut"
                      >退出系统</el-dropdown-item
                    >
                  </el-dropdown-menu>
                </template>
              </el-dropdown>
            </el-col>
            <el-col :span="3" class="header-toolbar-link nav-action-item">
              <el-text style="color: white; cursor: pointer">
                <el-icon>
                  <Help />
                </el-icon>
              </el-text>
            </el-col>
          </el-row>
        </el-col>
      </el-row>
    </el-header>
    <el-container>
      <el-aside style="border-right: 1px solid #ebeef5; width: auto">
        <el-scrollbar style="height: calc(100vh - 70px)">
          <el-menu
            class="el-menu-vertical-demo"
            :default-active="route.path"
            :default-openeds="[menuTree[0]?.url]"
            :router="true"
            :collapse="isCollapse"
            @select="handleSelect"
            @open="handleOpen"
            @close="handleClose"
            style="height: 100%; max-height: none"
          >
            <MenuRenderer :menu="transformedMenus" />
          </el-menu>
        </el-scrollbar>
      </el-aside>
      <el-container class="right-view">
        <el-main>
          <BreadcrumbRenderer :crumbs="crumbs" />
          <router-view />
        </el-main>
        <el-footer class="p-7">
          <ElText>{{ session.sessionUser?.sysSettingMap?.CoryRight }}</ElText>
        </el-footer>
      </el-container>
    </el-container>
  </el-container>
  <ElBacktop target=".el-main" />

  <!-- 弹出框 -->
  <el-dialog
    v-model="dialogFormVisible"
    title="修改密码"
    width="600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      label-width="120px"
      style="max-width: 520px"
      ref="ruleFormRef"
      :model="dataForm"
      :rules="rules"
    >
      <el-form-item label="旧的密码" prop="oldPassword">
        <el-input
          v-model="dataForm.oldPassword"
          :prefix-icon="Lock"
          size="large"
          placeholder="请输入旧的密码"
          type="password"
        />
      </el-form-item>
      <el-form-item label="新的密码" prop="newPassword">
        <el-input
          v-model="dataForm.newPassword"
          :prefix-icon="Lock"
          size="large"
          placeholder="请输入新的密码"
          type="password"
        />
      </el-form-item>
      <el-form-item label="确认密码" prop="confirmPassword">
        <el-input
          v-model="dataForm.confirmPassword"
          :prefix-icon="Lock"
          size="large"
          placeholder="请输入确认密码"
          type="password"
        />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="updateSetting(ruleFormRef)"> 确定 </el-button>
        <el-button @click="cancel()">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { RouterView, useRoute, useRouter } from 'vue-router'
import { computed, ref, reactive, nextTick, onMounted } from 'vue'
import logoImg from '@/assets/images/common/logo1.png'
import loginApi from '@/service/login'
import userApi from '@/service/sys/user'
import { useSessionStore, type Menu } from '@/store/session'
import MenuRenderer from '@/components/MenuRenderer.vue'
import { ElBacktop, ElMessage } from 'element-plus'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { Lock } from '@element-plus/icons-vue'
import BreadcrumbRenderer from '@/components/BreadcrumbRenderer.vue'
import useAppStore from '@/store/app'
const appStore = useAppStore()
const crumbs = computed(() => appStore.crumbs)
const isCollapse = computed(() => appStore.isCollapse)

const session = useSessionStore()

const router = useRouter()
const menuTree = ref(session.menuTree)

const ruleFormRef = ref<FormInstance>()
const dialogFormVisible = ref(false)
const dataForm = reactive({
  oldPassword: '',
  newPassword: '',
  confirmPassword: ''
})

onMounted(() => {
  console.log('---------------MainLayout-----------', session)
  initCrumbs()
})

const validateConfPass = (rule: any, value: any, callback: any) => {
  if (value === '') {
    callback(new Error('确认密码不能为空'))
  } else {
    if (dataForm.confirmPassword !== '') {
      if (dataForm.confirmPassword === dataForm.newPassword) {
        return true
      } else {
        callback(new Error('确认密码与新密码不一致'))
      }
    }
    callback()
  }
}
const rules = reactive<FormRules<any>>({
  oldPassword: [
    {
      required: true,
      message: '旧的密码不能为空',
      trigger: 'change'
    }
  ],
  newPassword: [
    {
      required: true,
      message: '新的密码不能为空',
      trigger: 'change'
    }
  ],
  confirmPassword: [
    {
      required: true,
      validator: validateConfPass,
      trigger: 'change'
    }
  ]
})

const getMenu4Renderer = (menus: Menu[]): MenuNode[] =>
  menus?.map((menu) => ({
    id: menu.value,
    icon: menu.icon,
    path: menu.url ? menu.url : menu.value,
    label: menu.label,
    menuType: menu.menuType,
    children: getMenu4Renderer(menu.children)
  }))
const transformedMenus = computed(() => getMenu4Renderer(menuTree.value))
const route = useRoute()
console.log('session', session)

const handleSelect = (key: string, keyPath: string[], event: any) => {
  // console.log('handleSelect--------------------------')
  // console.dir(event)
  let menus: Array<object> = []
  findMenuByKey(event.route, menus, menuTree.value)
  appStore.setCrumbs(menus)
}

const initCrumbs = () => {
  const route = useRoute()
  const path = route.path // 当前路由的路径
  let menus: Array<any> = []
  findMenuByKey(path, menus, menuTree.value)
  if (menus.length > 0) {
    appStore.setCrumbs(menus)
  }
}
//根据路由路径,查找菜单路径
const findMenuByKey = (key: string, menus: Array<any>, children: Array<any>) => {
  for (let menu of children) {
    if (key === menu.url) {
      menus.unshift({ label: menu.label, path: menu.url })
      return true
    } else if (menu.children) {
      if (findMenuByKey(key, menus, menu.children)) {
        menus.unshift({ label: menu.label, path: menu.url })
        return true
      }
    }
  }
  return false
}

const handleOpen = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}
const handleClose = (key: string, keyPath: string[]) => {
  console.log(key, keyPath)
}

async function loginOut() {
  await loginApi.loginOut({})
  session.$reset()
  router.replace({ path: '/' })
}

const openSetting = async () => {
  dialogFormVisible.value = true
  nextTick(() => {
    resetForm(ruleFormRef.value)
  })
}
const cancel = async () => {
  dialogFormVisible.value = false
  nextTick(() => {
    resetForm(ruleFormRef.value)
  })
}
const updateSetting = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    console.dir(valid)
    if (!valid) {
      return
    }
    console.dir(dataForm)
    userApi.updatePassword(dataForm).then(() => {
      dialogFormVisible.value = false
      ElMessage.success('保存成功！')
    })
  })
}
const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}
const goHome = () =>
  router.push({
    //name: session.sessionUser.sysSettingMap.successUrl ?? 'highwayMonitor'
    name: 'aviation'
  })
</script>

<style lang="scss" scoped>
.main-layout {
  height: 100%;

  .el-header {
    position: relative;
    background-color: var(--el-color-primary-light-7);
    color: var(--el-text-color-primary);
  }

  .el-aside {
    color: var(--el-text-color-primary);
    /* background: var(--el-color-primary-light-8); */
  }

  .el-menu {
    border-right: none;
  }

  .right-view {
    display: block;
    height: calc(100vh - 60px); // Header's height
    overflow: auto;

    .el-main {
      min-height: calc(100% - 60px);
    }
  }

  .el-main {
    padding: 0;
    background: #f2f6fc;
  }

  .el-footer {
    display: flex;
    align-items: center;
    justify-content: center;
  }
}

.header-toolbar-link {
  font-size: var(--el-font-size-base);
  align-items: center;
  display: flex;
  justify-content: center;
  color: white;
  cursor: pointer;
}

.el-dropdown-link {
  cursor: pointer;
  color: var(--el-color-primary);
  display: flex;
  align-items: center;
}

.nav-action-item {
  /* display: inline-block;
  min-width: 40px; */
  /* height: $navbar-height;
  line-height: $navbar-height; */
  /* color: var(--el-text-color); */
  text-align: center;
  cursor: pointer;

  &:hover {
    background: rgb(0 0 0 / 15%);
  }
}

.el-menu-vertical-demo:not(.el-menu--collapse) {
  width: 200px;
  min-height: 400px;
  max-height: calc(100vh - 60px);
  overflow: auto;
}
</style>
