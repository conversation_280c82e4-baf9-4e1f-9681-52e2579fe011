<template>
	<div v-if="modelValue" class="track-pop-box">
		<div class="title">
			<div class="left">
				<div class="btn" :class="active_index === 1 ? 'btn_active' : ''" @click="() => (active_index = 1)">
					<img :src="active_index === 1 ? jk_active : jk" alt="" />
					视频监控
				</div>
				<div class="btn" :class="active_index === 2 ? 'btn_active' : ''" @click="() => {active_index = 2;createTrack();}">
					<img :src="active_index === 2 ? gj_active : gj" alt="" />
					轨迹回放
				</div>
			</div>
			<span v-if="active_index === 1" class="close" @click="emit('update:modelValue', false)">×</span>
			<el-button  v-else class="back" @click="() => (active_index = 1)">返回</el-button>
		</div>
		<div v-if="active_index === 1" class="track-content">
			<table border style="border-collapse: collapse">
				<tbody>
					<tr>
						<td>企业</td>
						<td>test测试test测试test测试te</td>
						<td>车牌号</td>
						<td>湘D123456</td>
						<td>驾驶员姓名</td>
						<td>test测试</td>
					</tr>
					<tr>
						<td>定位时间</td>
						<td>2025年5月27日 16:55:44</td>
						<td>实时状态</td>
						<td>在线-ACC开-行驶</td>
						<td>实时速度</td>
						<td>49km/h（东）</td>
					</tr>
					<tr>
						<td>企业</td>
						<td>test测试test测试test测试te</td>
						<td>车牌号</td>
						<td>湘D123456</td>
						<td>驾驶员姓名</td>
						<td>test测试</td>
					</tr>
					<tr>
						<td>定位时间</td>
						<td>2025年5月27日 16:55:44</td>
						<td>实时状态</td>
						<td>在线-ACC开-行驶</td>
						<td>实时速度</td>
						<td>49km/h（东）</td>
					</tr>
				</tbody>
			</table>
		</div>
		<div v-else class="track-content">
			
			<div class="inner">
				<div class="left">
					日期：
					<el-select  placeholder="Select" size="large" style="width: 140px"></el-select>
				</div>
				<div class="right">
					<TrackAnimation ref="trackAnimationRef"/>
				</div>
			</div>
		</div>
	</div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, ref } from 'vue'
import gj from '@/assets/images/transportation/gj.png'
import gj_active from '@/assets/images/transportation/gj_active.png'
import jk from '@/assets/images/transportation/jk.png'
import jk_active from '@/assets/images/transportation/jk_active.png'
import TrackAnimation from '@/components/map/TrackAnimation.vue'

const emit = defineEmits(['update:modelValue']);
const props = withDefaults(defineProps<{
  modelValue: boolean
}>(), {
  modelValue: false
})

const trackAnimationRef = ref<InstanceType<typeof TrackAnimation>>()
const active_index = ref(1)


//测试车辆轨迹
import CityUtils from '@/utils/common/CityUtils'
import shanxiDistrict from '@/assets/json/bmap/shanxi_district_boundary.json';
const createTrack = () => {
	/**
	 * 设置轨迹时长，点位不带时间戳的匀速轨迹
	 */
	// nextTick(()=>{
	// 	let formatCity = CityUtils.formatCity('忻州市')
    // 	let pathList = (shanxiDistrict as any)[formatCity].slice(0, 300)
	// 	trackAnimationRef.value?.initData({ pathList, duration: 45 })
	// })

	/**
	 * 不设置轨迹时长，点位带时间戳的变速轨迹
	 */
	nextTick(()=>{
		let formatCity = CityUtils.formatCity('忻州市')
    	let pathList = (shanxiDistrict as any)[formatCity].slice(0, 300)
		let timeStamp = 0
		for(let i=0; i<pathList.length; i++){
			let pathArr = pathList[i]
			timeStamp = timeStamp + (Math.floor(Math.random() * 10) + 1) * 50
			pathArr[2] = timeStamp
		}
		trackAnimationRef.value?.initData({ pathList })
	})
}

</script>

<style scoped>
.track-pop-box {
	position: fixed;
  z-index: 1000;
	right: 10px;
	bottom: 10px;
	width: 1200px;
	height: 340px;
	background: #ffffff;
	padding: 30px;
	box-shadow: 0 5px 10px 0 rgba(0, 0, 0, 0.1);
	border-radius: 4px;

	.title {
		display: flex;
		justify-content: space-between;

		.left {
			display: flex;

			.btn {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 136px;
				height: 50px;
				border-radius: 4px;
				font-weight: 400;
				font-size: 16px;
				color: #86909c;
				text-align: center;
				margin-right: 20px;
				cursor: pointer;
				border: 1px solid #dcdfe6;

				img {
					margin-right: 5px;
				}
        &:hover{
          border: 1px solid #165dff;
          color: #1862ff;
        }
			}

			.btn_active {
				border: 1px solid #165dff;
				background: rgba(22, 93, 255, 0.05);
				color: #1862ff;
			}
		}

		.close {
			cursor: pointer;
		}

		.back {
			cursor: pointer;
			width: 90px;
			height: 46px;
			line-height: 46px;
			border-radius: 23px;
			font-family: Alibaba-PuHuiTi, Alibaba-PuHuiTi;
			font-weight: 400;
			font-size: 18px;
		}
	}

	.track-content {
		margin-top: 30px;
		width: 100%;
		height: 220px;

		.inner {
			display: flex;
			width: 100%;
			height: 100%;
			align-items: center;

			.right {
        display: flex;
        align-items: center;
				margin-left: 30px;

				.slider {
					display: flex;
          width: 500px;
          margin-left: 20px;
					span {
						margin-left: 20px;
					}
				}

				.control-btn {
					display: flex;
					margin-top: 10px;
					justify-content: center;

					.play,
					.pause,
					.stop {
						cursor: pointer;
					}

					.stop {
						margin-left: 20px;
					}
				}
			}
		}

		table {
			width: 100%;
			border-color: #e5e6ec;

			tr {
				td {
					font-family: Alibaba-PuHuiTi, Alibaba-PuHuiTi;
					font-weight: 400;
					font-size: 14px;
				}

				td:nth-child(odd) {
					width: 136px;
					height: 50px;
					background: #f7f8fa;
					text-align: center;
					color: #1d2129;
				}

				td:nth-child(even) {
					width: 266px;
					padding-left: 10px;
				}
			}
		}
	}
}
</style>
