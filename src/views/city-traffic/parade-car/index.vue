<template>
    <div class="full-container">
        <LeftDrawer>
        <Tabs :menus="tabs_data" v-slot="{ active }">
            <template v-if="active == 0">
            <DataOverview />
            </template>
            <template v-if="active == 1">
            <PassengerMap />
            </template>
            <template v-if="active == 2">
            <MonitoringWarning />
            </template>
        </Tabs>
        </LeftDrawer>
  </div>
</template>
<script lang="ts" setup>
import { ref } from 'vue'
import LeftDrawer from '@/components/v2/LeftDrawer.vue'
import Tabs from '@/components/v2/Tabs.vue'
import DataOverview from './DataOverview.vue'
import PassengerMap from './PassengerMap.vue'
import MonitoringWarning from './MonitoringWarning.vue'

const tabs_data: any[] = [
  {
    name: '巡游车概览'
  },
  {
    name: '巡游车客运地图'
  },
  {
    name: '巡游车监测预警'
  }
]

const activeIndex = ref(0)

</script>