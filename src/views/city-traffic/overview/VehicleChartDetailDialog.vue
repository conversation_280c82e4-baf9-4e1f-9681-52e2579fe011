<template>
    <div>
        <RightDataDetailPopup title="总体概况详情" v-model:visible="showDetail">
            <BaseTable :api="page" :export-api="exportExcel" export-file-name="总体概况详情">
            <template #search>
                <DateRangeSearch v-model:date="date"/>
            </template>
            <template #default="scope">
                <el-table-column type="index" label="序号" width="60"/>
                <el-table-column prop="event_cnt_pre" label="时间"/>
                <el-table-column prop="event_cnt_pre" label="长途客运"/>
                <el-table-column prop="event_cnt_pre" label="旅游大巴"/>
                <el-table-column prop="event_cnt_pre" label="长途客运入网率"/>
                <el-table-column prop="event_cnt_pre" label="旅游大巴入网率"/>
                <el-table-column prop="event_cnt_pre" label="长途客运上线率"/>
                <el-table-column prop="event_cnt_pre" label="旅游大巴上线率"/>
            </template>
            </BaseTable>
        </RightDataDetailPopup>
    </div>
</template>

<script lang="ts" setup>
import {Dayjs} from "dayjs";
import {ref} from "vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
const date = ref<Array<Dayjs>>()
// 详情
const showDetail = ref(false)

async function page (params: object) {
//   const {data} = await http.post('/cockpit/highway/operatorIndex', params)
//   return data
}

async function exportExcel (params: object) {
//   const {data} = await http.post('/export', params, {responseType: "blob"})
//   return data
}

const showDetailDialog = () => {
    showDetail.value = true
    console.log('showDetailDialog',showDetail.value)
}

defineExpose({
    showDetailDialog
})
</script>