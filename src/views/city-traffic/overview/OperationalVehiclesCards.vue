<template>
  <div>
    <el-row :gutter="12">
      <el-col :span="8">
        <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
      </el-col>
      <el-col :span="8">
        <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
      </el-col>
      <el-col :span="8">
        <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
      </el-col>
    </el-row>
  </div>
</template>
<script setup lang="ts">
import StatisCard from "@/components/common/StatisCard.vue";
import {ref} from "vue";
import qy from "@/assets/images/transportation/qy.png";
import qy_active from "@/assets/images/transportation/qy_active.png";
import zs from "@/assets/images/transportation/zs.png";
import zs_active from "@/assets/images/transportation/zs_active.png";
import cyry from "@/assets/images/transportation/cyry.png";
import cyry_active from "@/assets/images/transportation/cyry_active.png";
const tags_data = ref([
  {
    name: '企业数量',
    count: 12,
    unit: '家',
    icon: qy,
    active_icon: qy_active
  }
])

</script>