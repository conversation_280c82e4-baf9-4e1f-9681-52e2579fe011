<template>
    <div style="height: calc(100vh - 4.16667rem);width: 100%;">
      <el-row style="height: 20%;">
            <ItemTitle title="网约出租车行业概况">
              <div class="flex-box">
                <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
              </div>
              <!-- <IndustryMoreDetailDialog ref="industryMoreDetailRef"/> -->
            </ItemTitle>
        </el-row>
        <el-row style="height: 80%;">
            <ItemTitle title="网约出租车营运概况" more-text="营运趋势" :more-click="handleMoreClick">
              <div class="flex-box">
                <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
              </div>
              <CarStatsCard 
                  :statsData="[
                    { label: '重车数', value: 29, percentage: 53.2, color: '#FF9A23', unit: '辆'},
                    { label: '空车数', value: 26, percentage: 46.8, color: '#00BFFF', unit: '辆' }
                  ]"
                />
                <div class="flex-box">
                <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
              </div>
              <CarStatsCard 
                  :statsData="[
                    { label: '重车数', value: 29, percentage: 53.2, color: '#FF9A23', unit: '辆' },
                    { label: '空车数', value: 26, percentage: 46.8, color: '#00BFFF',unit: '辆' }
                  ]"
                />
              <TrendAnalysisDetailChart ref="trendAnalysisDetailChartRef"/>
            </ItemTitle>
        </el-row>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import cyry from '@/assets/images/transportation/cyry.png'
import cyry_active from '@/assets/images/transportation/cyry_active.png'
import qy from '@/assets/images/transportation/qy.png'
import qy_active from '@/assets/images/transportation/qy_active.png'
import zs from '@/assets/images/transportation/zs.png'
import zs_active from '@/assets/images/transportation/zs_active.png'
import ItemTitle from '@/components/common/ItemTitle.vue'
import StatisCard from '@/components/common/StatisCard.vue'
import LeftDrawer from "@/components/v2/LeftDrawer.vue";
import TrendAnalysisDetailChart from './components/TrendAnalysisDetailChart.vue'
import CarStatsCard from '@/components/v2/CarStatsCard.vue'
const trendAnalysisDetailChartRef = ref<InstanceType<typeof TrendAnalysisDetailChart> | null>(null)
const tags_data = ref([
  {
    name: '企业数量',
    count: 12,
    unit: '家',
    icon: qy,
    active_icon: qy_active
  },
  {
    name: '车辆总数',
    count: 12,
    unit: '辆',
    icon: zs,
    active_icon: zs_active
  },
  {
    name: '从业人员',
    count: 12,
    unit: '人',
    icon: cyry,
    active_icon: cyry_active
  }
])


const items = ref(
  Array.from({ length: 25 }, (_, i) => ({
    id: i + 1,
    plate: '晋B 080MH',
    operator: '张三',
    count: 1,
    time: '12:00:00'
  }))
)

function onRowClick(item) {
  console.log('点击行：', item)
}

function onPageChange(page) {
  console.log('当前页：', page)
}

// function handleIndustryMoreClick() {
//     industryMoreDetailRef.value?.showDetailDialog()
// }

function handleMoreClick() {
    console.log('点击更多按钮')
    trendAnalysisDetailChartRef.value?.showDetailDialog()
}
</script>

<style scoped>
/* 页面特定样式 */
</style>