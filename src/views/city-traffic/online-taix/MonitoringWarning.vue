<template>
  <div>
    <ItemTitle title="今日预警记录" :more-click="handleWarningClick">
        <div class="el-collapse-box">
            <el-collapse accordion>
            <el-collapse-item name="1">
                <template #title="{ isActive }">
                <div class="item-info">
                    <img class="item-icon" :src="cs" alt="" />
                    超速报警
                </div>
                </template>
                <div>
                    <SimpleList
                        :data="items"
                        :fields="['plate', 'operator', 'count', 'time']"
                        item-key="id"
                        scroll-height="300px"
                        :pagination="false"
                        :page-size="5"
                    >
                        <!-- 插槽自定义列内容（可选） -->
                        <template #cell="{ item, field }">
                        <span v-if="field === 'time'">🕒 {{ item[field] }}</span>
                        <span v-else>{{ item[field] }}</span>
                        </template>
                    </SimpleList>
                </div>
            </el-collapse-item>
            <el-collapse-item title="长期不在线预警" name="2">
                <template #title="{ isActive }">
                <div class="item-info">
                    <img class="item-icon" :src="bzx" alt="" />
                    长期不在线预警
                </div>
                </template>
                <div>
                    <SimpleList
                        :data="items"
                        :fields="['plate', 'operator', 'count', 'time']"
                        item-key="id"
                        scroll-height="300px"
                        :pagination="false"
                        :page-size="5"
                    >
                        <!-- 插槽自定义列内容（可选） -->
                        <template #cell="{ item, field }">
                        <span v-if="field === 'time'">🕒 {{ item[field] }}</span>
                        <span v-else>{{ item[field] }}</span>
                        </template>
                    </SimpleList>
                </div>
            </el-collapse-item>
            <el-collapse-item title="疲劳驾驶预警" name="3">
                <template #title="{ isActive }">
                <div class="item-info">
                    <img class="item-icon" :src="pl" alt="" />
                    疲劳驾驶预警
                </div>
                </template>
                <div>
                    <SimpleList
                        :data="items"
                        :fields="['plate', 'operator', 'count', 'time']"
                        item-key="id"
                        scroll-height="300px"
                        :pagination="false"
                        :page-size="5"
                    >
                        <!-- 插槽自定义列内容（可选） -->
                        <template #cell="{ item, field }">
                        <span v-if="field === 'time'">🕒 {{ item[field] }}</span>
                        <span v-else>{{ item[field] }}</span>
                        </template>
                    </SimpleList>
                </div>
            </el-collapse-item>
            <el-collapse-item title="非运营时段行驶预警" name="4">
                <template #title="{ isActive }">
                <div class="item-info">
                    <img class="item-icon" :src="fyy" alt="" />
                    非运营时段行驶预警
                </div>
                </template>
                <div>
                    <SimpleList
                        :data="items"
                        :fields="['plate', 'operator', 'count', 'time']"
                        item-key="id"
                        scroll-height="300px"
                        :pagination="false"
                        :page-size="5"
                    >
                        <!-- 插槽自定义列内容（可选） -->
                        <template #cell="{ item, field }">
                        <span v-if="field === 'time'">🕒 {{ item[field] }}</span>
                        <span v-else>{{ item[field] }}</span>
                        </template>
                    </SimpleList>
                </div>
            </el-collapse-item>
            </el-collapse>
        </div>
        <!-- <MonitorWarningDetailDialog ref="monitorWarningDetailDialogRef"/> -->
    </ItemTitle>
  </div>

</template>

<script setup lang="ts">
import { ref } from 'vue'
import pl from '@/assets/images/transportation/pl.png'
import cs from '@/assets/images/transportation/cs.png'
import bzx from '@/assets/images/transportation/bzx.png'
import fyy from '@/assets/images/transportation/fyy.png'
import SimpleList from '@/components/v2/SimpleList.vue'
// import MonitorWarningDetailDialog from './components/MonitorWarningDetailDialog.vue'
import ItemTitle from '@/components/common/ItemTitle.vue'
// const monitorWarningDetailDialogRef = ref<InstanceType<typeof MonitorWarningDetailDialog> | null>(null)

const items = ref(
  Array.from({ length: 25 }, (_, i) => ({
    id: i + 1,
    plate: '晋B 080MH',
    operator: '张三',
    count: 1,
    time: '12:00:00'
  }))
)

const handleWarningClick = () => {
//   monitorWarningDetailDialogRef.value?.showDetailDialog()
}
</script>

<style scoped>
.title {
  display: flex;
  justify-content: space-between;
  padding: 10px 0 20px 0;
  margin: 0 10px;

  .left {
    font-weight: 500;
    font-size: var(--menu-item-font-2);
    color: var(--color-text-emphasize1);
  }

  .right {
    width: 65px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    background: rgba(22, 93, 255, 0.1);
    border-radius: 4px;
    color: var(--more-btn-color);
    font-size: 16px;
    cursor: pointer;
  }
}

:deep(.el-collapse-item__header) {
  height: 60px;
}
</style>
