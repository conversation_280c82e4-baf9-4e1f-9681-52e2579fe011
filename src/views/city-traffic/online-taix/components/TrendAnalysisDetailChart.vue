<template>
    <div>
        <RightDataDetailPopup title="出租车趋势分析" v-model:visible="showDetail" width="60%">
            <el-row style="height: 30%;width: 100%;"> 
                <ItemTitle title="今日实载率趋势" style="height: 100%;">
                <div ref="pieChart" style="height: 300px;width: 100%;"></div>
              </ItemTitle>
            </el-row>
            <el-row style="height: 30%;width: 100%;"> 
                <ItemTitle title="今日营业时长趋势" style="height: 100%;">
                <div ref="pieChart" style="height: 300px;width: 100%;"></div>
              </ItemTitle>
            </el-row>
            <el-row style="height: 40%;">
              <ItemTitle title="今日营业时长排名" style="height: 100%;">
                <div ref="pieChart" style="height: 300px;width: 100%;"></div>
              </ItemTitle>
            </el-row>
        </RightDataDetailPopup>
    </div>
</template>

<script lang="ts" setup>
import {Dayjs} from "dayjs";
import {ref, Reactive, onMounted} from "vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import ItemTitle from '@/components/common/ItemTitle.vue'
import { useECharts } from '@/hooks/useECharts' // 引入 useECharts hook
import { basePieConfig00 } from '@/echarts/pieConfig'
const pieChart = ref<HTMLElement | null>(null)
import { remove } from "lodash";
const date = ref<Array<Dayjs>>()
// 详情
const showDetail = ref(false)


const handleSearchChange = (values) => {
  console.log('搜索条件:', values);
  // 执行搜索逻辑
};

const handleDateChange = (date) => {
  console.log('日期范围:', date);
  // 处理日期变化
};

async function page (params: object) {
//   const {data} = await http.post('/cockpit/highway/operatorIndex', params)
//   return data
}

async function exportExcel (params: object) {
//   const {data} = await http.post('/export', params, {responseType: "blob"})
//   return data
}

const showDetailDialog = () => {
  showDetail.value = true
    // loadData()
    console.log('showDetailDialog',showDetail.value)
}

const config = basePieConfig00()
// config.series[0].radius = ['50%', '70%']
// config.series[0].center = ['32%', '50%']

// config.series[1].radius = ['80%', '83%']
// config.series[1].center = ['32%', '50%']

// config.series[2].radius = ['36%', '45%']
// config.series[2].center = ['32%', '50%']
// const { updateChart, emptyDataCheck } = useECharts(config, pieChart)
// const loadData = () => {
//     const data = [
//         { value: 2, name: '柴油'},
//         { value: 2, name: '天然气' },
//         { value: 111, name: '纯电动'},
//         { value: 5, name: '汽油'},
//         { value: 2, name: '其他' }
//       ]
//     config.legend.data = data.map(item => item.name)
//     config.series[0].data = data
//     config.series[1].data = data
//     console.log(config)
//     emptyDataCheck(config.series[0].data)
//     updateChart(config)
// }

// onMounted(() => {
//     loadData()
// })

defineExpose({
    showDetailDialog
})
</script>