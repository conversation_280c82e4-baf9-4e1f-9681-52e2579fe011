<script setup lang="ts">
import { ref, defineProps } from 'vue'

const props = defineProps<{
  name: string
  count: number | string
  unit: string
  icon: string
  activeIcon: string
}>()

// 用于控制鼠标悬停效果
const isActive = ref(false)
</script>

<template>
  <div 
    class="num-tags-item"
    @mouseenter="isActive = true"
    @mouseleave="isActive = false"
  >
    <div class="content">
      <div class="left">
        <span class="count">{{ count }}</span>
        <span class="unit">{{ unit }}</span>
      </div>
      <div class="right">
        <img 
          :src="isActive ? activeIcon : icon" 
          :alt="name"
          class="icon-img"
        />
      </div>
    </div>
    <div class="title">{{ name }}</div>
  </div>
</template>

<style scoped>
.num-tags-item {
  /* 使用最小宽度，确保在小屏幕下不会过窄 */
  min-width: 120px;
  /* 允许宽度根据容器自适应 */
  width: 100%;
  /* 限制最大宽度，避免在大屏幕下过宽 */
  max-width: 160px;
  height: auto;
  min-height: 80px;
  border-radius: 4px;
  background: #ffffff;
  border: 1px solid #165dff;
  color: #1862ff;
  box-shadow: 0 4px 10px 0 rgba(22, 93, 255, 0.1);
  transition: all 0.3s ease;
  cursor: pointer;
  /* 确保内容不溢出 */
  overflow: hidden;
}

.num-tags-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 6px 12px rgba(22, 93, 255, 0.15);
}

.num-tags-item .content {
  display: flex;
  padding: 12px 12px 0 12px;
  align-items: center;
}

.num-tags-item .content .left,
.num-tags-item .content .right {
  flex: 1;
  min-width: 0;
}

.num-tags-item .content .right {
  text-align: right;
}

.num-tags-item .content .left .count {
  font-weight: 600;
  /* 字体大小根据容器宽度自适应 */
  font-size: clamp(16px, 2.5vw, 20px);
  margin-right: 4px;
}

.num-tags-item .content .left .unit {
  font-weight: 400;
  font-size: clamp(12px, 1.8vw, 14px);
  color: #1862ff;
}

.num-tags-item .title {
  font-family: 'PingFang SC', -apple-system, BlinkMacSystemFont, sans-serif;
  font-weight: 400;
  font-size: clamp(12px, 1.8vw, 14px);
  margin: 6px 0 0 12px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.icon-img {
  /* 确保图标大小自适应 */
  width: clamp(20px, 3vw, 30px);
  height: auto;
  max-height: 30px;
}
</style>
