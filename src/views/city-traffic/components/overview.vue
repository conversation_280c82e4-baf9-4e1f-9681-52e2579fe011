<template>
    <div style="height: calc(100vh - 4.16667rem);width: 100%;">
        <el-row style="height: 20%;">
            <ItemTitle title="城市客运营运车辆">
              <OperationalVehiclesCards/>
            </ItemTitle>
        </el-row>
        <el-row style="height: 40%;">
            <ItemTitle title="当前车辆分类占比">
              <template #actions>
                <el-button class="item-title-right" link size="mini" type="primary" @click="handleMoreClick('当前车辆分类占比')">更多></el-button>
              </template>

            </ItemTitle>
        </el-row>
        <el-row style="height: 40%;">
            <ItemTitle title="当前运力分类占比" more-text="查看详情" :more-click="handleMoreClick">
              <SimpleList
                :data="items"
                :fields="['plate', 'operator', 'count', 'time']"
                item-key="id"
                scroll-height="300px"
                :pagination="true"
                :page-size="5"
                @row-click="onRowClick"
                @page-change="onPageChange"
              >
    <!-- 插槽自定义列内容（可选） -->
    <template #cell="{ item, field }">
      <span v-if="field === 'time'">🕒 {{ item[field] }}</span>
      <span v-else>{{ item[field] }}</span>
    </template>
  </SimpleList>
            </ItemTitle>
        </el-row>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import cyry from '@/assets/images/transportation/cyry.png'
import cyry_active from '@/assets/images/transportation/cyry_active.png'
import qy from '@/assets/images/transportation/qy.png'
import qy_active from '@/assets/images/transportation/qy_active.png'
import zs from '@/assets/images/transportation/zs.png'
import zs_active from '@/assets/images/transportation/zs_active.png'
import cl from '@/assets/images/transportation/cl.png'
import cl_active from '@/assets/images/transportation/cl_active.png'
import rw from '@/assets/images/transportation/rw.png'
import rw_active from '@/assets/images/transportation/rw_active.png'
import sxl from '@/assets/images/transportation/sxl.png'
import sxl_active from '@/assets/images/transportation/sxl_active.png'
import ItemTitle from '@/components/common/ItemTitle.vue'
import StatisCard from '@/components/common/StatisCard.vue'
import SimpleList from '@/components/v2/SimpleList.vue'
import OperationalVehiclesCards from "@/views/city-traffic/overview/OperationalVehiclesCards.vue";
const tags_data = ref([
  {
    name: '企业数量',
    count: 12,
    unit: '家',
    icon: qy,
    active_icon: qy_active
  },
  {
    name: '车辆总数',
    count: 12,
    unit: '辆',
    icon: zs,
    active_icon: zs_active
  },
  {
    name: '从业人员',
    count: 12,
    unit: '人',
    icon: cyry,
    active_icon: cyry_active
  }
])


const items = ref(
  Array.from({ length: 25 }, (_, i) => ({
    id: i + 1,
    plate: '晋B 080MH',
    operator: '张三',
    count: 1,
    time: '12:00:00'
  }))
)

function onRowClick(item) {
  console.log('点击行：', item)
}

function onPageChange(page) {
  console.log('当前页：', page)
}

function handleMoreClick(type: string) {
  console.log('点击更多按钮：', type)
  // 这里可以添加具体的业务逻辑
}
</script>

<style scoped>
/* 页面特定样式 */
</style>