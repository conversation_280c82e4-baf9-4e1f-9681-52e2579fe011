<template>
    <div style="height: calc(100vh - 4.16667rem);width: 100%;">
      <el-row style="height: 20%;">
            <ItemTitle title="公交行业概况" :more-click="handleIndustryMoreClick">
              <div class="flex-box">
                <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
              </div>
              <IndustryMoreDetailDialog ref="industryMoreDetailRef"/>
            </ItemTitle>
        </el-row>
        <el-row style="height: 30%;">
            <ItemTitle title="公交车线路概况" :more-click="handleRouteMoreClick">
              <div class="flex-box">
                <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
              </div>
              <RouteOverviewDetailDialog ref="routeOverviewDetailDialogRef"/>
            </ItemTitle>
        </el-row>
        <el-row style="height: 20%;">
            <ItemTitle title="公交车站点概况" :more-click="handleSiteMoreClick">
              <div class="flex-box">
                <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
              </div>
              <BusSiteDetailDialog ref="busSiteDetailDialogRef"/>
            </ItemTitle>
        </el-row>
        <el-row style="height: 30%;">
            <ItemTitle title="公交车运营概况" :more-click="handleOperationsMoreClick">
              <div class="flex-box">
                <StatisCard v-for="(item, index) in tags_data" :key="index"
                    :title="item.name" :count="item.count" :unit="item.unit"
                    :main-img="item.active_icon"/>
              </div>
              <BusOperationDetailDialog ref="busOperationDetailDialogRef"/>
            </ItemTitle>
        </el-row>
    </div>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import cyry from '@/assets/images/transportation/cyry.png'
import cyry_active from '@/assets/images/transportation/cyry_active.png'
import qy from '@/assets/images/transportation/qy.png'
import qy_active from '@/assets/images/transportation/qy_active.png'
import zs from '@/assets/images/transportation/zs.png'
import zs_active from '@/assets/images/transportation/zs_active.png'
import ItemTitle from '@/components/common/ItemTitle.vue'
import StatisCard from '@/components/common/StatisCard.vue'
import LeftDrawer from "@/components/v2/LeftDrawer.vue";
import IndustryMoreDetailDialog from './components/IndustryMoreDetailDialog.vue'
import RouteOverviewDetailDialog from './components/RouteOverviewDetailDialog.vue'
import BusSiteDetailDialog from './components/BusSiteDetailDialog.vue'
import BusOperationDetailDialog from './components/BusOperationDetailDialog.vue'

const busOperationDetailDialogRef = ref<InstanceType<typeof BusOperationDetailDialog> | null>(null)
const busSiteDetailDialogRef = ref<InstanceType<typeof BusSiteDetailDialog> | null>(null)
const routeOverviewDetailDialogRef = ref<InstanceType<typeof RouteOverviewDetailDialog> | null>(null)
const industryMoreDetailRef = ref<InstanceType<typeof IndustryMoreDetailDialog> | null>(null)
const tags_data = ref([
  {
    name: '企业数量',
    count: 12,
    unit: '家',
    icon: qy,
    active_icon: qy_active
  },
  {
    name: '车辆总数',
    count: 12,
    unit: '辆',
    icon: zs,
    active_icon: zs_active
  },
  {
    name: '从业人员',
    count: 12,
    unit: '人',
    icon: cyry,
    active_icon: cyry_active
  }
])


const items = ref(
  Array.from({ length: 25 }, (_, i) => ({
    id: i + 1,
    plate: '晋B 080MH',
    operator: '张三',
    count: 1,
    time: '12:00:00'
  }))
)

function onRowClick(item) {
  console.log('点击行：', item)
}

function onPageChange(page) {
  console.log('当前页：', page)
}

function handleIndustryMoreClick() {
    industryMoreDetailRef.value?.showDetailDialog()
}

function handleRouteMoreClick() {
    routeOverviewDetailDialogRef.value?.showDetailDialog()
}
function handleSiteMoreClick() {
    busSiteDetailDialogRef.value?.showDetailDialog()
}

function handleOperationsMoreClick() {
  busOperationDetailDialogRef.value?.showDetailDialog()
}

</script>

<style scoped>
/* 页面特定样式 */
</style>