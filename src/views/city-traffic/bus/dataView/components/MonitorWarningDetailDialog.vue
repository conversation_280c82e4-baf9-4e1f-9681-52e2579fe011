<template>
    <div>
        <RightDataDetailPopup title="预警车辆详情" v-model:visible="showDetail">
            <BaseTable :api="page" @page-before="handlerPageBefore" :export-api="exportExcel" export-file-name="预警车辆详情">
            <template #search>
                <DateRangeSearch
                :showDateSelection="true"
                :dateButtonsConfig="dateButtons"
                :showSearchInput="true"
                :searchFields="searchFields"
                @searchChange="handleSearchChange"
                @update:date="handleDateChange"
            />
            </template>
            <template #default="scope">
                <el-table-column type="index" label="序号" width="60"/>
                <el-table-column prop="event_cnt_pre" label="报警时间"/>
                <el-table-column prop="event_cnt_pre" label="所属企业"/>
                <el-table-column prop="event_cnt_pre" label="车牌号"/>
                <el-table-column prop="event_cnt_pre" label="驾驶员"/>
                <el-table-column prop="event_cnt_pre" label="报警类型"/>
                <el-table-column prop="event_cnt_pre" label="报警级别"/>
                <el-table-column prop="event_cnt_pre" label="报警信息"/>
                <el-table-column prop="event_cnt_pre" label="操作">
                  <template #default="scope">
                    <el-button link>详情</el-button>
                  </template>
                  </el-table-column>
            </template>
            </BaseTable>
        </RightDataDetailPopup>
    </div>
</template>

<script lang="ts" setup>
import {Dayjs} from "dayjs";
import {ref, Reactive} from "vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
const date = ref<Array<Dayjs>>()
// 详情
const showDetail = ref(false)

const dateButtons = [
  { label: '本月', value: 3 },
];

const searchFields = [
  { name: 'keyword', label: '关键词', type: 'input', width: '200px' },
  { name: 'status', label: '状态', type: 'select', width: '150px', options: [
    { label: '全部', value: '' },
    { label: '正常', value: 1 },
    { label: '异常', value: 0 }
  ]}
];

function handlerPageBefore(params: Reactive<Record<string, any>>) {
  let extraParams: Record<string, any> = {
    info: "extra"
  }
  Object.keys(extraParams).forEach(key => {
    if (extraParams[key]) {
      params[key] = extraParams[key]
    }
  })
  console.log(params)
}

const handleSearchChange = (values) => {
  console.log('搜索条件:', values);
  // 执行搜索逻辑
};

const handleDateChange = (date) => {
  console.log('日期范围:', date);
  // 处理日期变化
};

async function page (params: object) {
//   const {data} = await http.post('/cockpit/highway/operatorIndex', params)
//   return data
}

async function exportExcel (params: object) {
//   const {data} = await http.post('/export', params, {responseType: "blob"})
//   return data
}

const showDetailDialog = () => {
    showDetail.value = true
    console.log('showDetailDialog',showDetail.value)
}

defineExpose({
    showDetailDialog
})
</script>