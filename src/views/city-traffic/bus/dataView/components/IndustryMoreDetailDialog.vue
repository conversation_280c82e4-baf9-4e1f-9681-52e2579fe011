<template>
    <div>
        <RightDataDetailPopup title="公交行业详情" v-model:visible="showDetail" width="40%">
            <el-row style="height: 60%;width: 100%;"> 
                <BaseTable :api="page" @page-before="handlerPageBefore" :export-api="exportExcel" export-file-name="公交行业详情" table-height="15rem ">
              <template #search>
                  <DateRangeSearch
                  :showDateSelection="true"
                  :dateButtonsConfig="dateButtons"
                  :showSearchInput="true"
                  :searchFields="searchFields"
                  @searchChange="handleSearchChange"
                  @update:date="handleDateChange"
              />
              </template>
              <template #default="scope">
                  <el-table-column type="index" label="序号" width="60"/>
                  <el-table-column prop="event_cnt_pre" label="时间"/>
                  <el-table-column prop="event_cnt_pre" label="长途客运"/>
                  <el-table-column prop="event_cnt_pre" label="旅游大巴"/>
                  <el-table-column prop="event_cnt_pre" label="长途客运入网率"/>
                  <el-table-column prop="event_cnt_pre" label="旅游大巴入网率"/>
                  <el-table-column prop="event_cnt_pre" label="长途客运上线率"/>
                  <el-table-column prop="event_cnt_pre" label="旅游大巴上线率"/>
              </template>
              </BaseTable> 
            </el-row>
            <el-row style="height: 40%;">
              <ItemTitle title="公交车燃料类型占比情况" style="height: 100%;">
                <div ref="pieChart" style="height: 300px;width: 100%;"></div>
              </ItemTitle>
            </el-row>
        </RightDataDetailPopup>
    </div>
</template>

<script lang="ts" setup>
import {Dayjs} from "dayjs";
import {ref, Reactive, onMounted} from "vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import ItemTitle from '@/components/common/ItemTitle.vue'
import { useECharts } from '@/hooks/useECharts' // 引入 useECharts hook
import { basePieConfig00 } from '@/echarts/pieConfig'
const pieChart = ref<HTMLElement | null>(null)
import { remove } from "lodash";
const date = ref<Array<Dayjs>>()
// 详情
const showDetail = ref(false)

const dateButtons = [
  { label: '今日', value: 1 },
  { label: '近7日', value: 2 },
  { label: '近30日', value: 3 },
  { label: '自定义', value: 4 },
];

const searchFields = [
  { name: 'keyword', label: '关键词', type: 'input', width: '200px' },
  { name: 'status', label: '状态', type: 'select', width: '150px', options: [
    { label: '全部', value: '' },
    { label: '正常', value: 1 },
    { label: '异常', value: 0 }
  ]}
];

function handlerPageBefore(params: Reactive<Record<string, any>>) {
  let extraParams: Record<string, any> = {
    info: "extra"
  }
  Object.keys(extraParams).forEach(key => {
    if (extraParams[key]) {
      params[key] = extraParams[key]
    }
  })
  console.log(params)
}

const handleSearchChange = (values) => {
  console.log('搜索条件:', values);
  // 执行搜索逻辑
};

const handleDateChange = (date) => {
  console.log('日期范围:', date);
  // 处理日期变化
};

async function page (params: object) {
//   const {data} = await http.post('/cockpit/highway/operatorIndex', params)
//   return data
}

async function exportExcel (params: object) {
//   const {data} = await http.post('/export', params, {responseType: "blob"})
//   return data
}

const showDetailDialog = () => {
  showDetail.value = true
    loadData()
    console.log('showDetailDialog',showDetail.value)
}

// const config = basePieConfig00()
// config.series[0].radius = ['50%', '70%']
// config.series[0].center = ['32%', '50%']

// config.series[1].radius = ['80%', '83%']
// config.series[1].center = ['32%', '50%']

// config.series[2].radius = ['36%', '45%']
// config.series[2].center = ['32%', '50%']
// const { updateChart, emptyDataCheck } = useECharts(config, pieChart)
// const loadData = () => {
//     const data = [
//         { value: 2, name: '柴油'},
//         { value: 2, name: '天然气' },
//         { value: 111, name: '纯电动'},
//         { value: 5, name: '汽油'},
//         { value: 2, name: '其他' }
//       ]
//     config.legend.data = data.map(item => item.name)
//     config.series[0].data = data
//     config.series[1].data = data
//     console.log(config)
//     emptyDataCheck(config.series[0].data)
//     updateChart(config)
// }

// onMounted(() => {
//     loadData()
// })

defineExpose({
    showDetailDialog
})
</script>