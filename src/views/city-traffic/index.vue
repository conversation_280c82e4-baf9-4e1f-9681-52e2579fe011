<template>
  <div class="full-container">
    <LeftDrawer>
      <!--城市客运-->
      <div v-if="Number(currentSonMenuIndex) === 0">
        <Overview/>
      </div>
      <div v-if="Number(currentSonMenuIndex) === 1">
        <Bus/>
      </div>
      <div v-if="Number(currentSonMenuIndex) === 2">
        
      </div>
      <div v-if="Number(currentSonMenuIndex) === 3">

      </div>
      <div v-if="Number(currentSonMenuIndex) === 4">

      </div>
    </LeftDrawer>
  </div>

</template>

<script lang="ts" setup>
import { onMounted } from 'vue'
import { useStore } from '@/store'
import { storeToRefs } from 'pinia'
import Overview from './overview/index.vue'
import Bus from './bus/index.vue'
import LeftDrawer from "@/components/v2/LeftDrawer.vue";
const store = useStore()

const { menu, currentMenuIndex, currentSonMenuIndex, legend_arr } = storeToRefs(store)



</script>
<style scoped></style>
