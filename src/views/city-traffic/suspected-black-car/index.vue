<template>
    <div>
        <LeftDrawer>
          <ItemTitle title="疑似社会黑车列表">
            <el-row style="height: 100%;width: 100%;"> 
                <el-row style="height: 20%;">
                  <el-form>
                    <el-form-item label="日期">
                      <el-date-picker
                        v-model="date"
                        type="date"
                        value-format="yyyy-MM-dd"
                        />
                    </el-form-item>
                    <el-form-item label="车牌号">
                      <el-input v-model="searchValue" placeholder="请输入车牌号"></el-input>
                    </el-form-item>
                  </el-form>
                </el-row>
                <el-row style="height: 80%;width: 100%;">
                  <el-table :data="tableData" border>
                      <el-table-column label="序号" width="80" align="center">
                          <template #default="scope">
                              {{ scope.$index + 1 }}
                          </template>
                      </el-table-column>
                      <el-table-column prop="plateNumber" label="车牌号"/>
                      <el-table-column prop="plateColor" label="车牌颜色"/>
                      <el-table-column prop="reportTime" label="上报时间"/>
                    </el-table>
                </el-row>
            </el-row>
            </ItemTitle>
        </LeftDrawer>
    </div>
</template>

<script lang="ts" setup>
import {Dayjs} from "dayjs";
import {ref, Reactive, onMounted} from "vue";
import LeftDrawer from '@/components/v2/LeftDrawer.vue'
import ItemTitle from '@/components/common/ItemTitle.vue'
import { remove } from "lodash";
const date = ref<Array<Dayjs>>()
// 详情
const showDetail = ref(false)

const tableData = [
  {
    plateNumber: '京A12345',
    plateColor: '蓝色',
    reportTime: '2023-08-15 09:30:22'
  },
  {
    plateNumber: '沪B56789',
    plateColor: '黄色',
    reportTime: '2023-08-15 10:15:03'
  },
  {
    plateNumber: '粤C24680',
    plateColor: '绿色',
    reportTime: '2023-08-15 11:45:17'
  }
]

const handleSearchChange = (values) => {
  console.log('搜索条件:', values);
  // 执行搜索逻辑
};

const handleDateChange = (date) => {
  console.log('日期范围:', date);
  // 处理日期变化
};

async function page (params: object) {
//   const {data} = await http.post('/cockpit/highway/operatorIndex', params)
//   return data
}

async function exportExcel (params: object) {
//   const {data} = await http.post('/export', params, {responseType: "blob"})
//   return data
}

const showDetailDialog = () => {
  showDetail.value = true
    console.log('showDetailDialog',showDetail.value)
}

defineExpose({
    showDetailDialog
})
</script>