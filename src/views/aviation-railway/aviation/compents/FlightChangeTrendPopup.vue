<template>
    <div class="components-container" style="height: 300px;width: 30rem;">
        <div style="height: 28px;">
            <DateRangeSearch @update:date="handleDateChange" />
        </div>
        <div style="height: calc(100% - 28px);">
            <div ref="chart" class="echart-container"></div>
        </div>
    </div>
</template>
<script setup lang="ts">
import { onMounted, ref, watch } from 'vue'
import { useECharts } from '@/hooks/useECharts'
import {  baseStackedLineConfig } from "@/echarts/lineConfig";
import { basePieConfig00 } from '@/echarts/pieConfig';
import DateRangeSearch from '@/components/v2/DateRangeSearch.vue';

const chart = ref<HTMLElement | null>(null);
const chartConfig = basePieConfig00();
const { updateChart, emptyDataCheck } = useECharts(chartConfig, chart);

onMounted(() => {
    loadChartData1()
})

const loadChartData = async () => {
    // chartConfig.xAxis.data = [2020, 2021, 2022, 2023, 2024, 2025]
    
    // let series = [[100, 200, 300, 500, 50, 120],[50, 300, 100, 350, 250, 12]]
    // chartConfig.series[0].data = series[0]
    // chartConfig.series[1].data = series[1]

    // /** Y的刻度还一直在，需要设置min和max值 */
    // chartConfig.yAxis.min = parseInt(Math.min(...series[0])) 
    // chartConfig.yAxis.max = parseInt(Math.max(...series[0]) * 1.2)

    // emptyDataCheck(chartConfig.series[0].data)
    // updateChart(chartConfig)

}

const loadChartData1 = async () => {
    // chartConfig.xAxis.data = [2020, 2021, 2022, 2023, 2024, 2025]
    
    // let series = [[100, 200, 300, 500, 50, 120],[50, 300, 100, 350, 250, 12]]
    // chartConfig.series[0].data = series[0]
    // chartConfig.series[1].data = series[1]

    // /** Y的刻度还一直在，需要设置min和max值 */
    // chartConfig.yAxis.min = parseInt(Math.min(...series[0])) 
    // chartConfig.yAxis.max = parseInt(Math.max(...series[0]) * 1.2)

    // emptyDataCheck(chartConfig.series[0].data)
    updateChart(chartConfig)

}

const datetime = ref()

const handleDateChange = (datetime: any) => {
    console.log('---------------------->>>', datetime)
}

// watch(() => datetime, (newVal, oldVal) => {
//     console.log('---------------------->>>', newVal, oldVal)
//   },{ immediate: true, deep: false }
// )


</script>