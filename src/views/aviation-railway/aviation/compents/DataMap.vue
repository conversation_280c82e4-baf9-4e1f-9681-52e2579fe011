<template>
  <VehicleTree :data-tree="dataTree"/>
  <TrackCard v-model="trackVisible"/>
</template>

<script setup lang="ts">
import { ref} from 'vue'
import TrackCard from "./TrackCard.vue";
import VehicleTree from '@/components/common/VehicleTree.vue';
const trackVisible = ref(false)

const handleClick = (treeRef: any)=> {
  console.log('--------father.handleClick-----------', treeRef)
}

const dataTree: VehicleTree[] = [
  {
    id: '1',
    label: '五台山机场(5/7)',
    children: [
      {
        id: '1-1',
        label: '晋A15171 赵小刚',
        online: true,
        lng: 112.735295, 
        lat: 38.448514
      },
      {
        id: '1-2',
        label: '晋A15172 赵小刚',
        online: true,
        lng: 112.73368581216272, 
        lat: 38.***************
      },
      {
        id: '1-3',
        label: '晋A15173 赵小刚',
        online: true,
      },
      {
        id: '1-4',
        label: '晋A15174 赵小刚',
        online: false,
      },
    ],
  },
  {
    id: '2',
    label: '五台山机场(5/7)',
    children: [
      {
        id: '2-1',
        label: '晋B15171 赵小刚',
        online: true,
      },
      {
        id: '2-2',
        label: '晋B15172 赵小刚',
      },
      {
        id: '2-3',
        label: '晋B15171 赵小刚',
      },
      {
        id: '2-4',
        label: '晋B15172 赵小刚',
      },
    ],
  },
]
</script>

<style>

</style>

<style lang="css" scoped>

</style>

<style scoped>
/* ::v-deep .el-input__inner::placeholder {
  font-size: 16px; 
} */

/* ::v-deep .el-tree .el-checkbox__inner {
    background-color: #165DFF !important;
    border-color: #165DFF !important;
}

::v-deep .el-tree .is-checked .el-checkbox__inner {
    background-color: #165DFF !important;
    border-color: #165DFF !important;
}
 
::v-deep .el-tree .is-checked .el-checkbox__inner::after {
    border-color: #fff !important;
} */
</style>