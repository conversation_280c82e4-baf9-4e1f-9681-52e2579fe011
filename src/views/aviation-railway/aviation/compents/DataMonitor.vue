<template>
  <ItemTitle title="今日预警记录" :show-more="true" :more-click="gotoView">
    <div class="el-collapse-box">
      <el-collapse accordion>
        <el-collapse-item name="1">
          <template #title="{ isActive }">
            <div class="item-info">
              <img class="item-icon" :src="cs" alt="" />
              出租车违规上下客
            </div>
          </template>
          <div>
            <simple-list :data="data" :fields="['plate', 'name', 'rank', 'time']" />
          </div>
        </el-collapse-item>
        <el-collapse-item title="长期不在线预警" name="2">
          <template #title="{ isActive }">
            <div class="item-info">
              <img class="item-icon" :src="bzx" alt="" />
              疑似黑车揽客
            </div>
          </template>
          <div>
            <simple-list :data="data" :fields="['plate', 'name', 'rank', 'time']" />
          </div>
        </el-collapse-item>
        <el-collapse-item title="疲劳驾驶预警" name="3">
          <template #title="{ isActive }">
            <div class="item-info">
              <img class="item-icon" :src="pl" alt="" />
              出租车未开GPS
            </div>
          </template>
          <div>
            <simple-list :data="data" :fields="['plate', 'name', 'rank', 'time']" />
          </div>
        </el-collapse-item>
        <el-collapse-item title="非运营时段行驶预警" name="4">
          <template #title="{ isActive }">
            <div class="item-info">
              <img class="item-icon" :src="fyy" alt="" />
              出租车违规载客
            </div>
          </template>
          <div>
            <simple-list :data="data" :fields="['plate', 'name', 'rank', 'time']" />
          </div>
        </el-collapse-item>
      </el-collapse>
    </div>
  </ItemTitle>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import pl from '@/assets/images/transportation/pl.png'
import cs from '@/assets/images/transportation/cs.png'
import bzx from '@/assets/images/transportation/bzx.png'
import fyy from '@/assets/images/transportation/fyy.png'

import SimpleList from '@/components/v2/SimpleList.vue'
import ItemTitle from '@/components/common/ItemTitle.vue'


const data = ref([
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  }
])

const gotoView = ()=> {

}

</script>

<style scoped>
.title {
  display: flex;
  justify-content: space-between;
  padding: 10px 0 20px 0;
  margin: 0 10px;

  .left {
    font-weight: 500;
    font-size: var(--menu-item-font-2);
    color: var(--color-text-emphasize1);
  }

  .right {
    width: 65px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    background: rgba(22, 93, 255, 0.1);
    border-radius: 4px;
    color: var(--more-btn-color);
    font-size: 16px;
    cursor: pointer;
  }
}

:deep(.el-collapse-item__header) {
  height: 60px;
}
</style>
