<template>
  <ItemTitle title="基础指标" :show-more="true">
    <div class="flex-box">
      <StatisCard v-for="(item, index) in tags_data" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.img" />
    </div>
  </ItemTitle>

  <ItemTitle title="航班数" :more-click="gotoFlightCountPopup">
    <div class="flex-box">
      <StatisCard v-for="(item, index) in tags_data2" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.name" />
    </div>
  </ItemTitle>

  <ItemTitle title="航班统计情况" :more-click="gotoFlightCountPopup">
    <div class="flex-box">
      <StatisCard v-for="(item, index) in tags_data2" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.name" />
    </div>
  </ItemTitle>

  <ItemTitle title="民航客运量监测">
    <div class="flex-box">
      <StatisCard v-for="(item, index) in tags_data2" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.name" />
    </div>
  </ItemTitle>

  <ItemTitle title="民航货运量监测">
    <div class="flex-box">
      <StatisCard v-for="(item, index) in tags_data2" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.name" />
    </div>
  </ItemTitle>

  <ItemTitle title="机场大巴运行">
    <div class="flex-box">
      <StatisCard v-for="(item, index) in tags_data2" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.name" />
    </div>
  </ItemTitle>

  <!-- <ItemTitle title="航班数占比">
    <div class="components-container" style="height: 200px;">
        <div ref="lineChart" class="echart-style" style="height: 100%;width: 100%;"></div>
    </div>
  </ItemTitle> -->

  <RightDataDetailPopup title="班次变化趋势详情" v-model:visible="visible">
    <FlightChangeTrendPopup/>
  </RightDataDetailPopup>

</template>

<script setup lang="ts">
import cyry from '@/assets/images/transportation/cyry.png'
import cyry_active from '@/assets/images/transportation/cyry_active.png'
import qy from '@/assets/images/transportation/qy.png'
import qy_active from '@/assets/images/transportation/qy_active.png'
import zs from '@/assets/images/transportation/zs.png'
import zs_active from '@/assets/images/transportation/zs_active.png'
import { onMounted, reactive, ref } from 'vue'

import StatisCard from '@/components/common/StatisCard.vue'

import { useECharts } from '@/hooks/useECharts'
import { verticalBarChartConfig } from "@/echarts/barConfig";
import ItemTitle from '@/components/common/ItemTitle.vue'
import RightDataDetailPopup from '@/components/v2/RightDataDetailPopup.vue'
import FlightChangeTrendPopup from './FlightChangeTrendPopup.vue'

import taxiApi from '@/service/tdss/taxi'

const visible = ref(false)

const queryParam = reactive({
  s: 0,
  n: 100,
})

onMounted(async ()=> {
  //const res: any = await taxiApi.getOperatingSpeed(queryParam)
  //console.log('-------结果集---------', res, res.data.data.list)
})

/** 打开航班数量面板 */
const gotoFlightCountPopup = ()=> {
  visible.value = true
}

const tags_data = ref([
  {
    name: '企业数量',
    count: 12,
    unit: '家',
    img: 'enterprise-count',
  },
  {
    name: '车辆总数',
    count: 12,
    unit: '辆',
    img: 'vehicle-count',
  },
  {
    name: '从业人员',
    count: 12,
    unit: '人',
    img: 'employees'
  },
])

const tags_data2 = ref([
  {
    name: '企业数量',
    count: 12,
    unit: '家',
    icon: qy,
    active_icon: qy_active
  },
  {
    name: '车辆总数',
    count: 12,
    unit: '辆',
    icon: zs,
    active_icon: zs_active
  },
  {
    name: '从业人员',
    count: 12,
    unit: '人',
    icon: cyry,
    active_icon: cyry_active
  },
])
// watch(currentSonMenuIndex, () => {
//   activeItemIndex.value = -1
//   // 今日客运站点监测
//   if (currentSonMenuIndex.value === '0') {
//     console.log('今日客运站点监测')
//   }
// })

onMounted(()=> {
  lineChartData()
})

const lineChart = ref<HTMLElement | null>(null);
const chartConfig = verticalBarChartConfig();
chartConfig.grid.top = 30
chartConfig.grid.bottom = 20
chartConfig.grid.right = '2%'
chartConfig.yAxis.splitNumber = 2
chartConfig.yAxis.name = '单位/万辆'
chartConfig.tooltip.formatter = function(params: any) {
  if(params[0].seriesIndex === 0) {
    return params[0].name + ' ：' + params[0].value + '万辆';
  }
}
const { updateChart, emptyDataCheck } = useECharts(chartConfig, lineChart);

const lineChartData = async () => {
  chartConfig.xAxis.data = [2020, 2021, 2022, 2023, 2024]
  chartConfig.series[0].data = [100, 200, 300, 400, 500]
  emptyDataCheck(chartConfig.series[0].data)
  updateChart(chartConfig)
}


</script>

