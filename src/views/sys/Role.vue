<template>
  <div class="crud-main">
    <div class="crud-main-query">
      <el-form :inline="true" class="query-form-inline">
        <el-form-item label="关键字">
          <el-input placeholder="请输入角色名称 / 角色代码" v-model="queryForm.keyword" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getPageList">&nbsp;&nbsp;&nbsp;&nbsp;查询&nbsp;&nbsp;&nbsp;&nbsp;</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="crud-main-content">
      <div class="mb-4 crud-main-content-toolbar">
        <el-button type="primary" icon="Plus" @click="() => {
          dialogFormVisible = true
          openAdd()
          resetForm(ruleFormRef)
        }
          ">新增</el-button>
        <el-button type="danger" icon="Delete" @click="_Delete()" :disabled="selectedRows.length === 0">删除</el-button>
      </div>

      <el-table :data="dataList" @selection-change="handleSelectionChange"
        header-cell-class-name="tocc-table-header-cell" header-row-class-name="tocc-table-header-row"
        row-class-name="tocc-table-row">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="编号" width="80" align="center" />
        <el-table-column prop="roleCode" label="角色代码" />
        <el-table-column prop="roleName" label="角色名称" />
        <el-table-column prop="roleDescription" label="角色描述" />
        <el-table-column fixed="right" label="操作" min-width="100" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="openUpdate(scope.row)">编辑</el-button>
            <el-button link type="primary" @click="authMenuOpen(scope.row)">授权资源</el-button>
            <el-button link type="primary" @click="authUserOpen(scope.row)">授权用户</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="crud-main-content-pagination">
        <el-pagination v-model:current-page="queryForm.pageNumber" v-model:page-size="queryForm.pageSize"
          :page-sizes="[10, 25, 50, 100]" layout="->,total, sizes, prev, pager, next" :total="queryForm.total"
          @current-change="handleCurrentChange" />
      </div>
    </div>
  </div>

  <!-- 弹出框 -->
  <el-dialog v-model="dialogFormVisible" title="新增" width="600px" :close-on-click-modal="false"
    :close-on-press-escape="false">
    <el-form :model="dataForm" label-width="120px" style="max-width: 520px" :rules="rules" ref="ruleFormRef"
      >
      <el-form-item label="角色代码" prop="roleCode">
        <el-input v-model="dataForm.roleCode" placeholder="选填" />
      </el-form-item>
      <el-form-item label="角色名称" prop="roleName">
        <el-input v-model="dataForm.roleName" placeholder="必填" />
      </el-form-item>
      <el-form-item label="角色描述" prop="roleDescription">
        <el-input v-model="dataForm.roleDescription" type="textarea" :rows="5" placeholder="选填" />
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="_AddorUpdate(ruleFormRef)"> 确定 </el-button>
        <el-button @click="dialogFormVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 授权资源 -->
  <el-dialog v-model="authMenuDialogVisible" title="授权资源" width="900px" :close-on-click-modal="false"
    :close-on-press-escape="false">
    <el-row justify="center">
      <el-col :span="24" style="border: 1px solid #ebeef5; padding: 0">
        <div style="height: 450px; overflow: auto">
          <el-tree v-if="authMenuDialogVisible" :data="menuTree" show-checkbox node-key="value" default-expand-all
            :expand-on-click-node="false" @check-change="handleCheckChange" :props="{ class: customNodeClass }"
            ref="menuTreeRef">
            <template #default="{ node, data }">
              <span class="custom-tree-node">
                <span>{{ node.label }}</span>
                <span v-if="!data.children && data.checkList != null">
                  <el-checkbox-group v-model="data.checkList" @change="checkboxChange(node, data)" size="small">
                    <el-checkbox-button v-for="permission in data.permissionList" :label="permission.actionName"
                      :value="permission.id" :key="permission.id" />
                  </el-checkbox-group>
                </span>
              </span>
            </template>
          </el-tree>
        </div>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="_AuthMenuSubmit()"> 确定 </el-button>
        <el-button @click="authMenuDialogVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>

  <!-- 授权用户 -->
  <el-dialog v-model="authUserDialogVisible" title="授权用户" width="1100px" :close-on-click-modal="false" :close-on-press-escape="false" class="authUserDialog">
    <el-row justify="center">
      <el-col :span="12" style="border: 1px solid #e4e7ed;border-bottom: 0;">
        <el-form :inline="true" class="query-form-inline" style="padding: 16px 12px 0 12px">
          <el-form-item label="所属机构">
            <el-tree-select placeholder="请选择" v-model="userQueryForm.unitId" :data="unitTree" :default-expand-all="true"
              :check-on-click-node="true" check-strictly clearable @node-click="authUnitTreeHandleNodeClick"
              @clear="authUnitTreeHandleClear" />
          </el-form-item>
        </el-form>
        <el-table :data="unAuthUserDataList" height="404" style="width: 100%"
          @selection-change="handleAddUserSelectionChange" header-cell-class-name="tocc-table-header-cell"
          header-row-class-name="tocc-table-header-row" row-class-name="tocc-table-row">
          <el-table-column type="selection" width="40" align="center" />
          <el-table-column type="index" label="编号" width="60" align="center" />
          <el-table-column prop="userName" label="用户名称" />
          <el-table-column prop="unit.uname" label="所属机构" />
        </el-table>
        <!-- <div class="crud-main-content-pagination">
          <el-pagination
              v-model:current-page="userQueryForm.pageNumber"
              v-model:page-size="userQueryForm.pageSize"
              hide-on-single-page=true
              :page-sizes="[10, 25, 50, 100]"
              layout="->,total, sizes, prev, pager, next"
              :total="userQueryForm.total"
              @current-change="UnAuthUserHandleCurrentChange"
            />
        </div> -->
      </el-col>
      <el-col :span="3">
        <div style="height: 100%; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center;">
          <div>
            <el-button type="primary" icon="ArrowRight" @click="addUserSelectedClick" :disabled="userSelectedRows.length === 0">授权</el-button>
          </div>
          <div style="margin-top: 14px">
            <el-button type="danger" icon="ArrowLeft" @click="removeUserSelectedClick" :disabled="userRemoveSelectedRows.length === 0">取消</el-button>
          </div>
        </div>
      </el-col>
      <el-col :span="9" style="border: 1px solid #e4e7ed;border-bottom: 0; padding: 0">
        <el-table :data="authUserDataList" height="470" style="width: 100%"
          @selection-change="handleRemoveUserSelectionChange" header-cell-class-name="tocc-table-header-cell"
          header-row-class-name="tocc-table-header-row" row-class-name="tocc-table-row">
          <el-table-column type="selection" width="55" align="center" />
          <el-table-column type="index" label="编号" width="80" align="center" />
          <el-table-column prop="userName" label="用户名称" />
        </el-table>
      </el-col>
    </el-row>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="_AuthUserSubmit()"> 确定 </el-button>
        <el-button @click="authUserDialogVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElTable } from 'element-plus'
import { ElMessage, ElMessageBox } from 'element-plus'
import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
import { reactive, ref, onMounted, nextTick, getCurrentInstance } from 'vue'

import roleApi from '@/service/sys/role'
import userApi from '@/service/sys/user'
import unitTreeService from '@/service/sys/unit-tree'
import roleUserApi from '@/service/sys/roleUser'
import menuApi from '@/service/sys/menu'
import roleMenuApi from '@/service/sys/roleMenu'

const dialogFormVisible = ref(false)
const authMenuDialogVisible = ref(false)
const authUserDialogVisible = ref(false)

const formLabelWidth = '140px'


const ruleFormRef = ref<FormInstance>()

const dataForm: any = reactive({
  id: null,
  roleCode: '',
  roleName: '',
  roleDescription: ''
})

const queryForm = reactive({
  keyword: '',
  pageNumber: getCurrentInstance()!.appContext.config.globalProperties.$pageNumber,
  pageSize: getCurrentInstance()!.appContext.config.globalProperties.$pageSize,
  total: 0
})

const dataList = ref([])
const menuTreeRef = ref()

onMounted(() => {
  console.log('onMounted')
  getPageList()
})

const unitTree = ref([])
async function getUnitTree() {
  unitTree.value = await unitTreeService.getUnitTree();
}

async function getPageList() {
  const res = await roleApi.getPageList(queryForm)
  dataList.value = res.data.data.records
  queryForm.total = res.data.data.total
}

const _AddorUpdate = async (formEl: FormInstance | undefined) => {
  if (dataForm.id == null || dataForm.id == '') {
    _Add(formEl)
  } else {
    _Update(formEl)
  }
}

/**A 新增 */
function openAdd() {
  dataForm.id = '';
}

const _Add = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (!valid) {
      return
    }
    const res = await roleApi.create(dataForm)
    dialogFormVisible.value = false
    getPageList()
  })
}

/**U 修改 */
const openUpdate = async (data: any) => {
  dialogFormVisible.value = true
  const res = await roleApi.getById(data.id)
  dataForm.id = res.data.data.id
  dataForm.roleCode = res.data.data.roleCode
  dataForm.roleName = res.data.data.roleName
  dataForm.roleDescription = res.data.data.roleDescription
}

const _Update = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid, fields) => {
    if (!valid) {
      return
    }
    const res = await roleApi.update(dataForm)
    dialogFormVisible.value = false
    getPageList()
  })
}

/*** D 删除 */
const selectedRows = ref([])
const handleSelectionChange = (row: any) => {
  selectedRows.value = row
}

function _Delete() {
  ElMessageBox.confirm('请确认是否删除数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let ids: any[] = []
    selectedRows.value.forEach((row: any) => {
      ids.push(row.id)
    })
    const res = await roleApi.removeAll(ids)
    getPageList()
    selectedRows.value = [] // 删除后清空选中项
  })
}

/**-------------授权资源 start------------------*/
const authMenuOpen = async (data: any) => {
  curRoleId = data.id
  //const resMenuTree = await menuApi.getJsonTree()
  const resMenuTree:any = await menuApi.getMenuPermissionTree()
  menuTree.value = resMenuTree.data.data
  const resRoleMenus:any = await roleMenuApi.queryRoleMenusByRoleId(data.id)
  authMenuDialogVisible.value = true
  const keys = []
  nextTick(() => {
    for (let item of resRoleMenus.data.data) {
      let menuId = item.menuId
      keys.push(menuId)
      let node = menuTreeRef.value.getNode(menuId)
      /* //从标识字符串perms中获取标识
        if (item.perms && item.perms.length > 0) {
          node.data.checkList = JSON.parse(item.perms)
        } else {
          node.data.checkList = []
        } */
      //从permissionList中获取标识
      if (item.permissionList) {
        node.data.checkList = item.permissionList.map((item:any) => item.permissonId);
      } else {
        node.data.checkList = []
      }
      //
      //判断是叶子节点才进行选中
      if (node.isLeaf) {
        menuTreeRef.value.setChecked(node, true)
      }
    }
    //设置选中的节点key
    //menuTreeRef.value.setCheckedKeys(keys, false)
  })
}

/**-------------授权资源 end------------------*/


const handleCurrentChange = (current: number) => {
  queryForm.pageNumber = current
  getPageList()
}

const authUnitTreeHandleNodeClick = (data: any) => {
  userQueryForm.unitId = data.value
  getUnAuthUserDataList()
}

const authUnitTreeHandleClear = (data: any) => {
  userQueryForm.unitId = ''
  getUnAuthUserDataList()
}

/**-------------授权用户 start------------------*/

let curRoleId: String
const unAuthUserDataList = ref([] as Array<any>) //未被授权的用户
const authUserDataList = ref([] as Array<any>) //已经被授权的用户

const authUserOpen = async (data: any) => {
  curRoleId = data.id
  authUserDataList.value = []
  getUnitTree()
  await getAuthUserDataList()
  await getUnAuthUserDataList()
  authUserDialogVisible.value = true
}

/**1) 查询已被授权用户*/
async function getAuthUserDataList() {
  const res = await roleUserApi.getListByRoleId(curRoleId)
  let dataList = res.data.data
  dataList.forEach(function (value: any, index: any, array: any) {
    authUserDataList.value.push({ id: value.USER_ID, userName: value.USER_NAME })
  })
}

/**2) 查询未被授权用户*/
async function getUnAuthUserDataList() {
  let userIds: any[] = []
  authUserDataList.value.forEach(function (value, index, array) {
    userIds.push(value.id)
  })
  userQueryForm.userIds = userIds;
  const res = await userApi.getUnAuthPageList(userQueryForm)
  unAuthUserDataList.value = res.data.data.records
  userQueryForm.total = res.data.data.total
}

/**3) 选中授权 */
function addUserSelectedClick() {
  console.log('----->', userSelectedRows)
  if (userSelectedRows.value.length == 0) {
    ElMessage.warning("请选择要授权的用户")
    return;
  }
  userSelectedRows.value.forEach(function (value, index, array) {
    authUserDataList.value.push(value)
  })
  getUnAuthUserDataList()
}

/**4) 取消授权 */
function removeUserSelectedClick() {
  console.log('---------------');
  if (userRemoveSelectedRows.value.length == 0) {
    ElMessage.warning("请选择要取消的用户")
    return;
  }
  let tempDataList = [] as Array<any>
  authUserDataList.value.forEach(function (value, index, array) {
    let foundItem = userRemoveSelectedRows.value.find((item: any) => item.id === value.id)
    if (!foundItem) {
      tempDataList.push(value)
    }
  })
  authUserDataList.value = tempDataList
  getUnAuthUserDataList()
}

const UnAuthUserHandleCurrentChange = (current: number) => {
  userQueryForm.pageNumber = current
  getUnAuthUserDataList()
}

//未选择用户
const userQueryForm = reactive({
  unitId: '',
  userIds: [] as any,
  pageNumber: 1,
  pageSize: 10000,
  total: 0
})

const userSelectedRows = ref([])
const handleAddUserSelectionChange = (val: []) => {
  userSelectedRows.value = val
}

const userRemoveSelectedRows = ref([])
const handleRemoveUserSelectionChange = (row: any) => {
  userRemoveSelectedRows.value = row
  console.log('handleRemoveUserSelectionChange');
}

const _AuthUserSubmit = () => {
  let userIds: string[] = []
  authUserDataList.value.forEach(function (value) {
    userIds.push(value.id)
  })
  let data = { roleId: curRoleId, userIds: userIds }
  roleUserApi.create(data)
  authUserDialogVisible.value = false
}

/**-------------授权用户 end------------------*/

/****----------------------校验------------------------- */
interface RuleForm {
  id: string
  roleCode: string
  roleName: string
  roleDescription: string
}

const rules = reactive<FormRules<RuleForm>>({
  roleCode: [
    {
      required: true,
      message: '角色代码不能为空',
      trigger: 'change'
    }
  ],
  roleName: [
    {
      required: true,
      message: '角色名称不能为空',
      trigger: 'change'
    }
  ],
  roleDescription: [
    {
      required: false,
      message: '角色描述不能为空',
      trigger: 'change'
    }
  ]
})

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

interface Tree {
  id: number
  label: string
  children?: Tree[] | null
  checkList?: String[] | null
}

const menuTree = ref<Tree[]>([])

const customNodeClass = (data: Tree, node: Node) => {
  return 'treeNodeContent'
}

/** 获取菜单结构树 */
async function getMenuTree() {
  const res = await menuApi.getJsonTree()
  menuTree.value = res.data.data
}

const handleCheckChange = (data: Tree, checked: boolean, indeterminate: boolean) => {
  if (checked) {
    if (data.checkList == null) {
      data.checkList = []
    }
  } else {
    data.checkList = null
  }
}

const checkboxChange = (node: any, data: any) => {
  /* console.dir('checkboxChange----------------------------------------')
  console.dir(node)
  console.dir(data) */
}

const _AuthMenuSubmit = () => {
  const checkedNodes = menuTreeRef.value!.getCheckedNodes(false, true)
  /* console.dir('AuthMenuSubmit----------------------------------------')
  console.log(menuTreeRef.value!.getCheckedNodes(false, true))
  console.log(menuTreeRef.value!.getCheckedKeys(false)) */
  const roleMenuList = []
  for (let node of checkedNodes) {
    let menuId = node.value
    let perms = '[]'
    let permissionList = []
    if (node.checkList != null) {
      perms = JSON.stringify(node.checkList)
      permissionList = node.checkList.map(function (itemId: string) {
        return { permissonId: itemId };
      });
    }
    roleMenuList.push({ roleId: curRoleId, menuId: menuId, perms: perms, permissionList: permissionList })
  }
  roleMenuApi
    .save({ roleId: curRoleId, roleMenuList: roleMenuList })
    .then(() => {
      ElMessage.success('保存成功！')
    })
    .catch(() => { })
    .finally(() => {
      authMenuDialogVisible.value = false
    })
}
</script>

<style>
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}

.treeNodeContent>.el-tree-node__content {
  padding-top: 3px;
  padding-bottom: 3px;
}

.treeNodeContent>.el-tree-node__children>div {
  padding-top: 6px;
  padding-bottom: 6px;
  border-bottom: 1px solid #ebeef5;
}
</style>
