<template>
	<div class="crud-main">
		<div class="crud-main-query">
			<el-form :model="queryParam" :inline="true" class="demo-form-inline">
				<el-form-item label="查询关键字">
					<el-input v-model="queryParam.queryKeyword" clearable
							  placeholder="名称 / 标识"
							  style="width: 200px;"
					/>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="getPageList">
						&nbsp;&nbsp;&nbsp;&nbsp;查询&nbsp;&nbsp;&nbsp;&nbsp;
					</el-button>
				</el-form-item>
			</el-form>
		</div>

		<div class="crud-main-content">
			<div class="mb-4 crud-main-content-toolbar">
				<el-button type="primary" :icon="Plus" @click="openAdd()">新增</el-button>
				<el-button type="danger" :icon="Delete" @click="_Delete()" :disabled="selectedRows.length === 0">删除
				</el-button>
			</div>
			<el-table :data="dataList" style="width: 100%; height: 300px" show-overflow-tooltip
					  @selection-change="handleSelectionChange" header-cell-class-name="tocc-table-header-cell"
					  header-row-class-name="tocc-table-header-row" row-class-name="tocc-table-row">
				<el-table-column type="selection" width="55" />
				<el-table-column type="index" label="编号" width="80" align="center" />
				<el-table-column prop="pageName" label="页面名称" align="center" />
				<el-table-column prop="pageCode" label="页面标识" align="center" />
				<el-table-column prop="pageType" label="页面类型" align="center">
					<template #default="scope">
						<el-tag v-if="scope.row.pageType == 1" type="success">栅格布局</el-tag>
						<el-tag v-if="scope.row.pageType == 2" type="primary">定位布局</el-tag>
					</template>
				</el-table-column>
				<el-table-column prop="remark" label="备注" align="center" />
				<el-table-column prop="sort" label="排序" align="center" />
				<el-table-column prop="status" label="状态" width="100" align="center">
					<template #default="scope">
						<el-tag v-if="scope.row.status == 1" type="success">可用</el-tag>
						<el-tag v-if="scope.row.status == 0" type="danger">禁用</el-tag>
					</template>
				</el-table-column>
				<el-table-column fixed="right" label="操作" min-width="80" align="center">
					<template #default="scope">
						<el-button link type="primary" @click="openUpdate(scope.row)">编辑</el-button>
						<el-button link type="primary" @click="layout(scope.row)">布局</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div class="crud-main-content-pagination">
				<el-pagination v-model:current-page="queryParam.pageNumber" v-model:page-size="queryParam.pageSize"
							   :page-sizes="[defPageSize, 10, 25, 50, 100]" layout="->,total, sizes, prev, pager, next"
							   :total="queryParam.total" @current-change="handleCurrentChange" />
			</div>
		</div>
	</div>

	<!-- 弹出框 -->
	<el-dialog v-model="dialogFormVisible" :title="dataForm.id ? '修改' : '新增'" width="680px"
			   :close-on-click-modal="false" :close-on-press-escape="false" class="main-form-dialog">
		<el-form :model="dataForm" label-width="100px" style="max-width: 600px" :rules="rules" ref="ruleFormRef"
				 >
			<el-form-item label="页面名称" prop="pageName">
				<el-input v-model="dataForm.pageName" placeholder="必填" />
			</el-form-item>
			<el-form-item label="页面标识" prop="pageCode">
				<el-input v-model="dataForm.pageCode" placeholder="必填" />
			</el-form-item>
			<el-form-item label="页面类型" prop="pageType">
				<el-radio-group v-model="dataForm.pageType" :disabled="dataForm.id ? true : false">
					<el-radio :value="1" size="large">栅格布局</el-radio>
					<el-radio :value="2" size="large">定位布局</el-radio>
				</el-radio-group>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="dataForm.remark" type="textarea" />
			</el-form-item>
			<el-form-item label="排序" prop="sort">
				<el-input-number v-model="dataForm.sort" controls-position="right" style="width: 100%" :min="-1" :max="9999"
				:step="1" step-strictly placeholder="必填" />
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-switch v-model="dataForm.status" placeholder="必填" inline-prompt active-text="可用" inactive-text="禁用"
          			:active-value="1" :inactive-value="0" size="large" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="_AddorUpdate(ruleFormRef)"> 确定 </el-button>
				<el-button @click="dialogFormVisible = false">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup name="pageFlex" lang="ts">
	import { ElTable } from 'element-plus'
	import { Delete, Plus } from '@element-plus/icons-vue'
	import { ElMessageBox, ElMessage } from 'element-plus'
	import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
	import { reactive, ref, onMounted, nextTick, getCurrentInstance } from 'vue'
	import pageFlexApi from '@/service/sys/pageFlex'
	import { useRouter } from 'vue-router'

	const router = useRouter()
	const dialogFormVisible = ref(false)
	
	const ruleFormRef = ref<FormInstance>()
	const defPageNumber = getCurrentInstance()!.appContext.config.globalProperties.$pageNumber
	const defPageSize = 5

	export declare type PageFlex = {
		id: string | null
		pageName: string | null
		pageCode: string | null
		pageType: number | null
		colNum: number | null
		rowHeight: number | null
		width: number | null
		height: number | null
		draggable: boolean
  		resizable: boolean
		pageParams: string | null
		remark: string | null
		sort: number | null
		status: number | null
		pageLayoutList?: any[]
	}

	const queryParam = reactive({
		queryKeyword: '',
		
		pageName: null,
		pageCode: null,
		colNum: null,
		rowHeight: null,
		remark: null,
		sort: null,
		status: null,
		pageNumber: defPageNumber,
		pageSize: defPageSize,
		total: 0
	})

	const dataForm = reactive<PageFlex>({
		id: null,
		pageName: null,
		pageCode: null,
		pageType: 1,
		colNum: 24,
		rowHeight: 50,
		width: 1920,
		height: 1080,
		draggable: true,
    	resizable: true,
		pageParams: null,
		remark: null,
		sort: 1,
		status: 1
	})

	const dataList = ref([])

	onMounted(() => {
		_init()
	})

	function _init() {
		getPageList()
	}

	const _AddorUpdate = async (formEl: FormInstance | undefined) => {
		if (dataForm.id == null || dataForm.id == '') {
			_Add(formEl)
		} else {
			_Update(formEl)
		}
	}

	/**A 新增 */
	async function openAdd() {
		dataForm.id = null
		dialogFormVisible.value = true
		nextTick(() => {
			resetForm(ruleFormRef.value)
		})
		const res:any = await pageFlexApi.getNextSort()
		const nextSort = res.data.data
		if (nextSort) {
			dataForm.sort = nextSort
		}
	}

	const _Add = async (formEl: FormInstance | undefined) => {
		if (!formEl) return
		await formEl.validate(async (valid, fields) => {
			if (!valid) {
				return
			}
			const res = await pageFlexApi.create(dataForm)
			dialogFormVisible.value = false
			ElMessage.success('保存成功！')
			_init()
		})
	}

	/**U 修改 */
	const openUpdate = async (data: PageFlex) => {
		const res: any = await pageFlexApi.getById(data.id!)
		dialogFormVisible.value = true
		nextTick(() => {
			globalCopyObject(dataForm, res.data.data)
		})
	}

	const _Update = async (formEl: FormInstance | undefined) => {
		if (!formEl) return
		await formEl.validate(async (valid, fields) => {
			if (!valid) {
				return
			}
			const res = await pageFlexApi.update(dataForm)
			dialogFormVisible.value = false
			ElMessage.success('保存成功！')
			_init()
		})
	}

	/*** D 删除 */
	const selectedRows = ref<Array<PageFlex>>([])
	const handleSelectionChange = (val: Array<PageFlex>) => {
		selectedRows.value = val
	}

	function _Delete() {
		ElMessageBox.confirm('请确认是否删除数据?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(async () => {
			const ids: Array<string> = []
			selectedRows.value.forEach((row: PageFlex) => {
				ids.push(row.id!)
			})
			const res = await pageFlexApi.remove(ids)
			_init()
			selectedRows.value = [] // 删除后清空选中项
		})
	}

	/**查询 */
	async function getPageList() {
		const res: any = await pageFlexApi.pageList(queryParam)
		dataList.value = res.data.data.records
		queryParam.total = res.data.data.total
	}

	const handleCurrentChange = (current: number) => {
		queryParam.pageNumber = current
		getPageList()
	}

	/****----------------------校验------------------------- */

	const rules = reactive<FormRules<PageFlex>>({
		pageName: [
				{
					required: true,
					message: '页面名称不能为空',
					trigger: 'change'
				}
			],
		pageCode: [
				{
					required: true,
					message: '页面标识不能为空',
					trigger: 'change'
				}
			],
		colNum: [
				{
					required: true,
					message: '栅格列数不能为空',
					trigger: 'change'
				}
			],
		rowHeight: [
				{
					required: true,
					message: '栅格行高不能为空',
					trigger: 'change'
				}
			],
		sort: [
				{
					required: true,
					message: '排序不能为空',
					trigger: 'change'
				}
			],
		status: [
				{
					required: true,
					message: '状态不能为空',
					trigger: 'change'
				}
			]
	})

	const resetForm = (formEl: FormInstance | undefined) => {
		if (!formEl) return
		formEl.resetFields()
	}

	//-------------------------------------------------

	const layout = async (data: PageFlex) => {
		if(data.pageType === 1){
			router.push({ path: '/sys/pageLayout', query: {id: data.id} })
		}else{
			router.push({ path: '/sys/pageLayoutPosition', query: {id: data.id} })
		}
		
	}

</script>

<style></style>
