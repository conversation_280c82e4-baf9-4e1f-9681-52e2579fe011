<template>
  <div class="crud-main">
    <div class="crud-main-query">
      <el-form :inline="true" class="query-form-inline">
        <el-form-item label="机构名称">
          <el-input placeholder="请输入机构名称" v-model="queryForm.uname" />
        </el-form-item>
        <el-form-item label="机构状态">
          <el-select placeholder="请选择" v-model="queryForm.status">
            <el-option label="有效" value="shanghai" />
            <el-option label="无效" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item label="创建时间">
          <el-date-picker type="date" placeholder="请选择" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getPageList"
            >&nbsp;&nbsp;&nbsp;&nbsp;查询&nbsp;&nbsp;&nbsp;&nbsp;
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <el-row :gutter="0">
      <el-col :span="5" style="background: white; border-radius: 4px; padding: 8px 8px 10px 8px">
        <el-tree
          style="max-width: 600px"
          v-model="dataForm.unitId"
          empty-text=""
          :default-expand-all="true"
          :data="unitTree"
          :highlight-current="true"
          @node-click="handleNodeClick"
        />
      </el-col>
      <el-col :span="19" style="background: #f2f6fc">
        <div class="crud-main-content" style="margin-left: 8px">
          <div class="mb-4 crud-main-content-toolbar">
            <el-button
              type="primary"
              icon="Plus"
              @click="
                () => {
                  dialogFormVisible = true
                  openAdd()
                  resetForm(ruleFormRef)
                }
              "
              >新增
            </el-button>
            <el-button
              type="danger"
              icon="Delete"
              @click="_Delete()"
              :disabled="selectedRows.length === 0"
              >删除
            </el-button>
            <el-button type="success" @click="syncOrgan" :disabled="disabledSync"
              >&nbsp;&nbsp;&nbsp;&nbsp;同步机构&nbsp;&nbsp;&nbsp;&nbsp;
            </el-button>
          </div>
          <el-table
            :data="dataList"
            @selection-change="handleSelectionChange"
            header-cell-class-name="tocc-table-header-cell"
            header-row-class-name="tocc-table-header-row"
            row-class-name="tocc-table-row"
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column type="index" label="编号" width="80" align="center" />
            <!-- <el-table-column prop="id" label="ID" width="120" /> -->
            <el-table-column prop="ucode" label="机构代码" />
            <el-table-column prop="uname" label="机构名称" />
            <el-table-column prop="usort" label="排序" width="80" align="center" />
            <el-table-column prop="createTime" label="创建时间" />
            <el-table-column fixed="right" label="操作" min-width="30" align="center">
              <template #default="scope">
                <el-button link type="primary" @click="openUpdate(scope.row)">编辑</el-button>
              </template>
            </el-table-column>
          </el-table>
          <div class="crud-main-content-pagination">
            <el-pagination
              v-model:current-page="queryForm.pageNumber"
              v-model:page-size="queryForm.pageSize"
              :page-sizes="[10, 25, 50, 100]"
              layout="->,total, sizes, prev, pager, next"
              :total="queryForm.total"
              @current-change="handleCurrentChange"
            />
          </div>
        </div>
      </el-col>
    </el-row>
  </div>

  <!-- 弹出框 -->
  <el-dialog
    class="main-form-dialog"
    v-model="dialogFormVisible"
    title="新增机构"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    style="width: 600px"
    align-center
  >
    <el-form
      :model="dataForm"
      label-width="120px"
      style="max-width: 520px"
      :rules="rules"
      ref="ruleFormRef"
    >
      <el-form-item label="上级机构" prop="parentId">
        <el-tree-select
          placeholder="选填"
          v-model="dataForm.parentId"
          :data="unitTree"
          :default-expand-all="true"
          :check-on-click-node="true"
          check-strictly
          clearable
        />
      </el-form-item>
      <el-form-item label="机构代码" prop="ucode">
        <el-input v-model="dataForm.ucode" placeholder="选填" />
      </el-form-item>
      <el-form-item label="机构名称" prop="uname">
        <el-input v-model="dataForm.uname" placeholder="必填" />
      </el-form-item>
      <el-form-item label="机构排序" prop="usort">
        <el-input-number v-model="dataForm.usort" style="width: 100%; text-align: left" />
      </el-form-item>
      <el-form-item label="机构类型" prop="type">
        <el-radio-group v-model="dataForm.type">
          <el-radio label="Account">集团、单位</el-radio>
          <el-radio label="Department">部门</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否是集团" prop="isGroup">
        <el-radio-group v-model="dataForm.isGroup">
          <el-radio :label="true">是</el-radio>
          <el-radio :label="false">否</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item v-show="dataForm.id" label="数据来源" prop="dataSource">
        <el-radio-group disabled v-model="dataForm.dataSource">
          <el-radio :label="1">系统内部</el-radio>
          <el-radio :label="0">第三方同步</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button type="primary" @click="_AddorUpdate(ruleFormRef)"> 确定</el-button>
        <el-button @click="dialogFormVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
import { ElMessage, ElMessageBox, ElTable } from 'element-plus'
import { onMounted, reactive, ref } from 'vue'
import unitApi from '@/service/sys/unit'

import unitTreeService from '@/service/sys/unit-tree'
import type { SyncResultVO } from '@/entity/sync.ts'

const dialogFormVisible = ref(false)

const ruleFormRef = ref<FormInstance>()

const queryForm = reactive({
  parentId: '',
  uname: '',
  status: '',
  pageNumber: 1,
  pageSize: 7,
  total: 0
})

const dataForm: any = reactive({
  id: '',
  parentId: '',
  ucode: '',
  uname: '',
  usort: '',
  unitId: ''
})

onMounted(() => {
  _init()
})

const unitTree = ref([])
const dataList = ref([])

async function _init() {
  getPageList()
  unitTree.value = await unitTreeService.getUnitTree()
}

/**查询 */
async function getPageList() {
  const res = await unitApi.getPageList(queryForm)
  dataList.value = res.data.data.records
  queryForm.total = res.data.data.total
}

const _AddorUpdate = async (formEl: FormInstance | undefined) => {
  if (dataForm.id == null || dataForm.id == '') {
    _Add(formEl)
  } else {
    _Update(formEl)
  }
}

/**A 新增 */
function openAdd() {
  dataForm.id = ''
}

const _Add = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (!valid) {
      return
    }
    await unitApi.create(dataForm)
    dialogFormVisible.value = false
    _init()
  })
}

/**U 修改 */
const openUpdate = async (data: any) => {
  dialogFormVisible.value = true
  const res = await unitApi.getById(data.id)
  Object.assign(dataForm, res.data.data)
  // for (let i in dataForm) {
  //   dataForm[i] = res.data.data[i]
  // }
}

const _Update = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (!valid) {
      return
    }
    await unitApi.update(dataForm)
    dialogFormVisible.value = false
    _init()
  })
}

/*** D 删除 */
const selectedRows = ref([])
const handleSelectionChange = (row: any) => {
  selectedRows.value = row
}

function _Delete() {
  ElMessageBox.confirm('请确认是否删除数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let ids: any[] = []
    selectedRows.value.forEach((row: any) => {
      ids.push(row.id)
    })
    const res = await unitApi.removeAll(ids.join(','))
    _init()
    selectedRows.value = [] // 删除后清空选中项
  })
}

const handleCurrentChange = (current: any) => {
  queryForm.pageNumber = current
  getPageList()
}

const handleNodeClick = (data: any) => {
  console.log(data)
  queryForm.parentId = data.value
  getPageList()
}

/****----------------------校验------------------------- */
interface RuleForm {
  ucode: string
  uname: string
  usort: number
}

const rules = reactive<FormRules<RuleForm>>({
  ucode: [
    {
      required: false,
      message: '用户名称不能为空',
      trigger: 'change'
    }
  ],
  uname: [
    {
      required: true,
      message: '机构名称不能为空',
      trigger: 'change'
    }
  ],
  usort: [
    {
      required: false,
      message: '登录密码不能为空',
      trigger: 'change'
    }
  ]
})

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

// 机构信息同步
const disabledSync = ref(false)

function syncOrgan() {
  ElMessageBox.confirm('您确认要同步吗?', '用户同步', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    disabledSync.value = true
    try {
      const data = await unitApi.syncUnit()
      const result = data.data.data as SyncResultVO
      ElMessage({
        message: `同步成功,本次共新增${result.insertCount}条,更新${result.updateCount}条,删除${result.deleteCount}条数据。`,
        type: 'success'
      })
    } finally {
      disabledSync.value = false
    }
  })
}
</script>
