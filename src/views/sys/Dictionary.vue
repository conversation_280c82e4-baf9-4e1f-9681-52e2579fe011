<template>
  <div class="crud-main">
    <el-tabs v-model="activeName" @tab-click="handleClick" class="tabs-main">
      <el-tab-pane label="全部字典" name="all">
        <DictionaryData ref="all"></DictionaryData>
      </el-tab-pane>
      <el-tab-pane label="系统字典" name="system" lazy>
        <DictionaryData :classify="'1'" ref="system"></DictionaryData>
      </el-tab-pane>
      <el-tab-pane label="业务字典" name="business" lazy>
        <DictionaryData :classify="'2'" ref="business"></DictionaryData>
      </el-tab-pane>
    </el-tabs>
  </div>
</template>

<script lang="ts" setup>
import { reactive, toRefs } from 'vue'
import DictionaryData from '@/components/DictionaryData.vue'
import type { TabsPaneContext } from 'element-plus'

const data = reactive({
  activeName: 'all'
})
const { activeName } = toRefs(data)

const handleClick = (tab: TabsPaneContext, event: Event) => {
  console.log(tab, event)
}
</script>

<style>
.tabs-main > .el-tabs__content {
  padding: 0;
  margin: 0;
  overflow: visible;
}
.tabs-main > .el-tabs__nav-scroll {
  background-color: #0091ff;
}
.tabs-main .custom-tabs-label .el-icon {
  vertical-align: middle;
}
.tabs-main .el-tabs__nav-wrap {
  background: white;
  border-radius: 4px;
  padding-left: 8px;
  padding-right: 8px;
  padding-bottom: 10px;
}
</style>
