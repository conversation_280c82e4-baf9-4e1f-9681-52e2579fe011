<template>
	<div class="crud-main">
		<div class="crud-main-query">
			<el-form :model="queryParam" :inline="true" class="demo-form-inline">
				<el-form-item label="查询关键字">
					<el-input v-model="queryParam.queryKeyword" clearable
							  placeholder="名称 / 标识 / 路径 "
							  style="width: 200px;"
					/>
				</el-form-item>
				<el-form-item>
					<el-button type="primary" @click="getPageList">
						&nbsp;&nbsp;&nbsp;&nbsp;查询&nbsp;&nbsp;&nbsp;&nbsp;
					</el-button>
				</el-form-item>
			</el-form>
		</div>

		<div class="crud-main-content">
			<div class="mb-4 crud-main-content-toolbar">
				<el-button type="primary" :icon="Plus" @click="openAdd()">新增</el-button>
				<el-button type="danger" :icon="Delete" @click="_Delete()" :disabled="selectedRows.length === 0">删除
				</el-button>
			</div>
			<el-table :data="dataList" style="width: 100%; height: 340px" show-overflow-tooltip
					  @selection-change="handleSelectionChange" header-cell-class-name="tocc-table-header-cell"
					  header-row-class-name="tocc-table-header-row" row-class-name="tocc-table-row">
				<el-table-column type="selection" width="55" />
				<el-table-column type="index" label="编号" width="80" align="center" />
				<el-table-column prop="compImage" label="缩略图" width="100" align="center">
					<template #default="scope">
						<el-image
							style="width: 55px; height: 35px;"
							:src="getPageComponentImg(scope.row.compImage)"
							fit="fill">
							<template #error>
								<div class="image-slot">
									<el-icon><icon-picture /></el-icon>
								</div>
							</template>
						</el-image>
					</template>
				</el-table-column>
				<el-table-column prop="compName" label="组件名称" align="center" />
				<el-table-column prop="compCode" label="组件标识" align="center" />
				<el-table-column prop="compPath" label="组件路径" width="300" align="center" />
				<el-table-column prop="remark" label="备注" align="center" />
				<el-table-column prop="sort" label="排序" align="center" />
				<el-table-column prop="status" label="状态" width="100" align="center">
					<template #default="scope">
						<el-tag v-if="scope.row.status == 1" type="success">可用</el-tag>
						<el-tag v-if="scope.row.status == 0" type="danger">禁用</el-tag>
					</template>
				</el-table-column>
				<el-table-column fixed="right" label="操作" min-width="50" align="center">
					<template #default="scope">
						<el-button link type="primary" @click="openUpdate(scope.row)">编辑</el-button>
					</template>
				</el-table-column>
			</el-table>
			<div class="crud-main-content-pagination">
				<el-pagination v-model:current-page="queryParam.pageNumber" v-model:page-size="queryParam.pageSize"
							   :page-sizes="[defPageSize, 10, 25, 50, 100]" layout="->,total, sizes, prev, pager, next"
							   :total="queryParam.total" @current-change="handleCurrentChange" />
			</div>
		</div>
	</div>

	<!-- 弹出框 -->
	<el-dialog v-model="dialogFormVisible" align-center :title="dataForm.id ? '修改' : '新增'" width="680px"
			   :close-on-click-modal="false" :close-on-press-escape="false" class="main-form-dialog">
		<el-form :model="dataForm" label-width="100px" style="max-width: 600px" :rules="rules" ref="ruleFormRef"
				 >
			<el-form-item label="组件名称" prop="compName">
				<el-input v-model="dataForm.compName" placeholder="必填" />
			</el-form-item>
			<el-form-item label="组件标识" prop="compCode">
				<el-input v-model="dataForm.compCode" placeholder="必填" />
			</el-form-item>
			<el-form-item label="组件路径" prop="compPath">
				<el-input v-model="dataForm.compPath" placeholder="必填" />
			</el-form-item>
			<el-form-item label="组件缩略图" prop="compImage">
				<el-input v-model="dataForm.compImage" placeholder="默认路径：/src/assets/images/pageComponent/" />
			</el-form-item>
			<el-form-item label="组件配置示例" prop="exampleParams">
				<el-collapse style="width: 100%;">
					<el-collapse-item title="组件参数示例">
						<VAceEditor
							v-model:value="dataForm.exampleParams!"
							lang="json"
							:theme="theme"
							:options="editorOptions"
							:wrap-enabled="true"
							style="height: 120px; width: 100%;"
							/>
					</el-collapse-item>
					<el-collapse-item title="组件数据示例">
						<VAceEditor
							v-model:value="dataForm.exampleDatas!"
							lang="json"
							:theme="theme"
							:options="editorOptions"
							:wrap-enabled="true"
							style="height: 120px; width: 100%;"
						/>
					</el-collapse-item>
					<el-collapse-item title="组件事件示例">
						<VAceEditor
							v-model:value="dataForm.exampleEvents!"
							lang="javascript"
							:theme="theme"
							:options="editorOptions"
							:wrap-enabled="true"
							style="height: 120px; width: 100%;"
						/>
					</el-collapse-item>
				</el-collapse>
			</el-form-item>
			<el-form-item label="备注" prop="remark">
				<el-input v-model="dataForm.remark" type="textarea" />
			</el-form-item>
			<el-form-item label="排序" prop="sort">
				<el-input-number v-model="dataForm.sort" controls-position="right" style="width: 100%" :min="-1" :max="9999"
				:step="1" step-strictly placeholder="必填" />
			</el-form-item>
			<el-form-item label="状态" prop="status">
				<el-switch v-model="dataForm.status" placeholder="必填" inline-prompt active-text="可用" inactive-text="禁用"
          			:active-value="1" :inactive-value="0" size="large" />
			</el-form-item>
		</el-form>
		<template #footer>
			<div class="dialog-footer">
				<el-button type="primary" @click="_AddorUpdate(ruleFormRef)"> 确定 </el-button>
				<el-button @click="dialogFormVisible = false">取消</el-button>
			</div>
		</template>
	</el-dialog>
</template>

<script setup name="pageComponent" lang="ts">
	import { VAceEditor } from 'vue3-ace-editor';
	import 'ace-builds/src-noconflict/mode-json';
	import 'ace-builds/src-noconflict/mode-javascript';
	import 'ace-builds/src-noconflict/theme-chrome';
	import 'ace-builds/src-noconflict/theme-github';
	import { ElTable } from 'element-plus'
	import { Picture as IconPicture } from '@element-plus/icons-vue'
	import { Delete, Plus } from '@element-plus/icons-vue'
	import { ElMessageBox, ElMessage } from 'element-plus'
	import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
	import { reactive, ref, onMounted, nextTick, getCurrentInstance } from 'vue'
	import { getPageComponentImg } from '@/utils/common/PageComponentUtils'
	import pageComponentApi from '@/service/sys/pageComponent'

	const dialogFormVisible = ref(false)
	
	const ruleFormRef = ref<FormInstance>()
	const defPageNumber = getCurrentInstance()!.appContext.config.globalProperties.$pageNumber
	const defPageSize = 5


	export declare type PageComponent = {
		id: string | null
		compName: string | null
		compCode: string | null
		compPath: string | null
		compImage: string | null
		exampleParams: string | null
		exampleDatas: string | null
		exampleEvents: string | null
		remark: string | null
		sort: number | null
		status: number | null
		component?: any
	}

	const queryParam = reactive({
		queryKeyword: '',
		
		compName: null,
		compCode: null,
		compPath: null,
		compImage: null,
		remark: null,
		sort: null,
		status: null,
		pageNumber: defPageNumber,
		pageSize: defPageSize,
		total: 0
	})

	const dataForm = reactive<PageComponent>({
		id: null,
		compName: null,
		compCode: null,
		compPath: null,
		compImage: null,
		exampleParams: null,
		exampleDatas: null,
		exampleEvents: null,
		remark: null,
		sort: 1,
		status: 1
	})

	const theme = ref('chrome');
	const editorOptions = ref({
	tabSize: 2,
	showPrintMargin: false,
	fontSize: 12,
	wrap: "free",
	});

	const dataList = ref([])

	onMounted(() => {
		_init()
	})

	function _init() {
		getPageList()
	}

	const _AddorUpdate = async (formEl: FormInstance | undefined) => {
		if (dataForm.id == null || dataForm.id == '') {
			_Add(formEl)
		} else {
			_Update(formEl)
		}
	}

	/**A 新增 */
	async function openAdd() {
		dataForm.id = null
		dialogFormVisible.value = true
		nextTick(() => {
			resetForm(ruleFormRef.value)
		})
		const res:any = await pageComponentApi.getNextSort()
		const nextSort = res.data.data
		if (nextSort) {
			dataForm.sort = nextSort
		}
	}

	const _Add = async (formEl: FormInstance | undefined) => {
		if (!formEl) return
		await formEl.validate(async (valid, fields) => {
			if (!valid) {
				return
			}
			const res = await pageComponentApi.create(dataForm)
			dialogFormVisible.value = false
			ElMessage.success('保存成功！')
			_init()
		})
	}

	/**U 修改 */
	const openUpdate = async (data: PageComponent) => {
		const res: any = await pageComponentApi.getById(data.id!)
		dialogFormVisible.value = true
		nextTick(() => {
			globalCopyObject(dataForm, res.data.data)
		})
	}

	const _Update = async (formEl: FormInstance | undefined) => {
		if (!formEl) return
		await formEl.validate(async (valid, fields) => {
			if (!valid) {
				return
			}
			const res = await pageComponentApi.update(dataForm)
			dialogFormVisible.value = false
			ElMessage.success('保存成功！')
			_init()
		})
	}

	/*** D 删除 */
	const selectedRows = ref<Array<PageComponent>>([])
	const handleSelectionChange = (val: Array<PageComponent>) => {
		selectedRows.value = val
	}

	function _Delete() {
		ElMessageBox.confirm('请确认是否删除数据?', '提示', {
			confirmButtonText: '确定',
			cancelButtonText: '取消',
			type: 'warning'
		}).then(async () => {
			const ids: Array<string> = []
			selectedRows.value.forEach((row: PageComponent) => {
				ids.push(row.id!)
			})
			const res = await pageComponentApi.remove(ids)
			_init()
			selectedRows.value = [] // 删除后清空选中项
		})
	}

	/**查询 */
	async function getPageList() {
		const res: any = await pageComponentApi.pageList(queryParam)
		dataList.value = res.data.data.records
		queryParam.total = res.data.data.total
	}

	const handleCurrentChange = (current: number) => {
		queryParam.pageNumber = current
		getPageList()
	}

	/****----------------------校验------------------------- */

	const rules = reactive<FormRules<PageComponent>>({
		compName: [
				{
					required: true,
					message: '组件名称不能为空',
					trigger: 'change'
				}
			],
		compCode: [
				{
					required: true,
					message: '组件标识不能为空',
					trigger: 'change'
				}
			],
		compPath: [
				{
					required: true,
					message: '组件路径不能为空',
					trigger: 'change'
				}
			],
		compImage: [
				{
					required: true,
					message: '组件缩略图不能为空',
					trigger: 'change'
				}
			],
		sort: [
				{
					required: true,
					message: '排序不能为空',
					trigger: 'change'
				}
			],
		status: [
				{
					required: true,
					message: '状态不能为空',
					trigger: 'change'
				}
			]
	})

	const resetForm = (formEl: FormInstance | undefined) => {
		if (!formEl) return
		formEl.resetFields()
	}



</script>

<style>
.demo-image__error .block {
  padding: 30px 0;
  text-align: center;
  border-right: solid 1px var(--el-border-color);
  display: inline-block;
  width: 49%;
  box-sizing: border-box;
  vertical-align: top;
}
.demo-image__error .demonstration {
  display: block;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  margin-bottom: 20px;
}
.demo-image__error .el-image {
  padding: 0 5px;
  max-width: 300px;
  max-height: 200px;
  width: 100%;
  height: 200px;
}

.demo-image__error .image-slot {
  display: flex;
  justify-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
  background: var(--el-fill-color-light);
  color: var(--el-text-color-secondary);
  font-size: 30px;
}
.demo-image__error .image-slot .el-icon {
  font-size: 30px;
}
</style>
