<template>
	<div class="crud-main">
	  <div class="crud-main-query">
		<el-form :model="queryParam" :inline="true" class="crud-query-form-inline">
		  <el-form-item label="查询关键字">
			<el-input v-model="queryParam.queryKeyword" placeholder="名称 / 标识 / 值" 
			  style="width: 200px;" clearable />
		  </el-form-item>
		  <el-form-item label="状态">
			<el-select v-model="queryParam.status" placeholder="请选择" clearable>
			  <el-option label="可用" :value="0" />
			  <el-option label="不可用" :value="1" />
			</el-select>
		  </el-form-item>
		  <el-form-item>
			<el-button type="primary" @click="getPageList">&nbsp;&nbsp;&nbsp;&nbsp;查询&nbsp;&nbsp;&nbsp;&nbsp;</el-button>
		  </el-form-item>
		</el-form>
	  </div>
  
	  <div class="crud-main-content">
		<div class="mb-2 crud-main-content-toolbar">
		  <el-button type="primary" icon="Plus" @click="openAdd()">新增</el-button>
		  <el-button type="danger" icon="Delete" @click="_Delete()" :disabled="selectedRows.length === 0">删除</el-button>
		</div>
		<el-table :data="dataList" @selection-change="handleSelectionChange"
		  header-cell-class-name="tocc-table-header-cell" header-row-class-name="tocc-table-header-row"
		  row-class-name="tocc-table-row">
		  <el-table-column type="selection" width="55" align="center"/>
		  <el-table-column type="index" label="编号" width="80" align="center" />
		  <el-table-column prop="parmName" label="参数名称" align="center" />
		  <el-table-column prop="parmCode" label="参数标识" align="center" />
		  <el-table-column prop="parmValue" label="参数的值" align="center" />
		  <el-table-column prop="sort" label="排序" width="100" align="center" />
		  <el-table-column prop="status" label="状态" width="100" align="center">
			<template #default="scope">
			  <el-tag v-if="scope.row.status == 0" type="success">可用</el-tag>
			  <el-tag v-if="scope.row.status == 1" type="danger">禁用</el-tag>
			</template>
		  </el-table-column>
		  <el-table-column prop="remark" label="备注" width="200" align="center" />
		  <el-table-column fixed="right" label="操作" min-width="30" align="center">
			<template #default="scope">
			  <el-button link type="primary" @click="openUpdate(scope.row)">编辑</el-button>
			</template>
		  </el-table-column>
		</el-table>
		<div class="crud-main-content-pagination">
		  <el-pagination v-model:current-page="queryParam.pageNumber" v-model:page-size="queryParam.pageSize"
			:page-sizes="[defPageSize, 10, 25, 50, 100]" layout="->,total, sizes, prev, pager, next" :total="queryParam.total"
			@current-change="handleCurrentChange" />
		</div>
	  </div>
	</div>
  
	<!-- 弹出框 -->
	<el-dialog v-model="dialogFormVisible" :title="dataForm.id ? '修改' : '新增'" width="680px" :close-on-click-modal="false"
	  :close-on-press-escape="false" class="main-form-dialog">
	  <el-form :model="dataForm" label-width="100px" style="max-width: 600px" :rules="rules" ref="ruleFormRef"
		>
		<el-form-item label="参数名称" prop="parmName">
		  <el-input v-model="dataForm.parmName" placeholder="必填" />
		</el-form-item>
		<el-form-item label="参数标识" prop="parmCode">
		  <el-input v-model="dataForm.parmCode" placeholder="必填" />
		</el-form-item>
		<el-form-item label="参数的值" prop="parmValue">
		  <el-input v-model="dataForm.parmValue" placeholder="必填" />
		</el-form-item>
		<el-form-item label="排序" prop="sort">
		  <el-input-number v-model="dataForm.sort" controls-position="right" style="width: 100%" :min="-1" :max="9999"
			:step="1" step-strictly placeholder="必填" />
		</el-form-item>
		<el-form-item label="备注" prop="remark">
		  <el-input v-model="dataForm.remark" type="textarea" />
		</el-form-item>
		<el-form-item label="字典状态" prop="status">
		  <el-switch v-model="dataForm.status" placeholder="必填" inline-prompt active-text="可用" inactive-text="禁用"
			:active-value="0" :inactive-value="1" size="large" />
		</el-form-item>
	  </el-form>
	  <template #footer>
		<div class="dialog-footer">
		  <el-button type="primary" @click="_AddorUpdate(ruleFormRef)"> 确定 </el-button>
		  <el-button @click="dialogFormVisible = false">取消</el-button>
		</div>
	  </template>
	</el-dialog>
  </template>
  
  <script lang="ts" setup>
  import { ElTable } from 'element-plus'
  import { ElMessageBox, ElMessage } from 'element-plus'
  import type { ComponentSize, FormInstance, FormRules } from 'element-plus'
  import { reactive, ref, onMounted, nextTick, getCurrentInstance } from 'vue'
  import settingDmApi from '@/service/sys/settingDm'
  
  const dialogFormVisible = ref(false)
  
  
  const ruleFormRef = ref<FormInstance>()
  interface settingDm {
	id: string | null
	parmName: string | null
	parmCode: string | null
	parmValue: string | null
	remark: string | null
	sort: number | null
	status: number | null
  }
  const defPageNumber = getCurrentInstance()!.appContext.config.globalProperties.$pageNumber
  const defPageSize = getCurrentInstance()!.appContext.config.globalProperties.$pageSize
  const queryParam = reactive({
	queryKeyword: '',
	status: null,
	pageNumber: defPageNumber,
	pageSize: defPageSize,
	total: 0
  })
  
  const dataForm = reactive<settingDm>({
	id: null,
	parmName: null,
	parmCode: null,
	parmValue: null,
	remark: '',
	sort: 1,
	status: 0,
  })
  
  const dataList = ref([])
  
  onMounted(() => {
	console.log('onMounted')
	_init()
  })
  
  function _init() {
	getPageList()
  }
  
  const _AddorUpdate = async (formEl: FormInstance | undefined) => {
	if (dataForm.id == null || dataForm.id == '') {
	  _Add(formEl)
	} else {
	  _Update(formEl)
	}
  }
  
  /**A 新增 */
  async function openAdd() {
	dataForm.id = null
	dialogFormVisible.value = true
	nextTick(() => {
	  resetForm(ruleFormRef.value)
	})
	const res:any = await settingDmApi.getNextSort()
	const nextSort = res.data.data
	if (nextSort) {
	  dataForm.sort = nextSort
	}
  }
  
  const _Add = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
	  if (!valid) {
		return
	  }
	  const res = await settingDmApi.create(dataForm)
	  dialogFormVisible.value = false
	  ElMessage.success('保存成功！')
	  _init()
	})
  }
  
  /**U 修改 */
  const openUpdate = async (data: settingDm) => {
	dialogFormVisible.value = true
	const res:any = await settingDmApi.getById(data.id!)
	globalCopyObject(dataForm, res.data.data)
  }
  
  const _Update = async (formEl: FormInstance | undefined) => {
	if (!formEl) return
	await formEl.validate(async (valid, fields) => {
	  if (!valid) {
		return
	  }
	  const res = await settingDmApi.update(dataForm)
	  dialogFormVisible.value = false
	  ElMessage.success('保存成功！')
	  _init()
	})
  }
  
  /*** D 删除 */
  const selectedRows = ref<Array<settingDm>>([])
  const handleSelectionChange = (val: Array<settingDm>) => {
	selectedRows.value = val
  }
  
  function _Delete() {
	ElMessageBox.confirm('请确认是否删除数据?', '提示', {
	  confirmButtonText: '确定',
	  cancelButtonText: '取消',
	  type: 'warning'
	}).then(async () => {
	  const ids: Array<string> = []
	  selectedRows.value.forEach((row: settingDm) => {
		ids.push(row.id!)
	  })
	  const res = await settingDmApi.remove(ids)
	  _init()
	  selectedRows.value = [] // 删除后清空选中项
	})
  }
  
  
  /**查询 */
  async function getPageList() {
	const res: any = await settingDmApi.pageList(queryParam)
	dataList.value = res.data.data.records
	queryParam.total = res.data.data.total
  }
  
  
  const handleCurrentChange = (current: number) => {
	queryParam.pageNumber = current
	getPageList()
  }
  
  /****----------------------校验------------------------- */
  const rules = reactive<FormRules<settingDm>>({
	parmName: [
	  {
		required: true,
		message: '参数名称不能为空',
		trigger: 'change'
	  }
	],
	parmCode: [
	  {
		required: true,
		message: '参数标识不能为空',
		trigger: 'change'
	  }
	],
	parmValue: [
	  {
		required: true,
		message: '参数的值不能为空',
		trigger: 'change'
	  }
	],
	status: [
	  {
		required: true,
		message: '状态不能为空',
		trigger: 'change'
	  }
	],
	sort: [
	  {
		required: true,
		message: '排序不能为空',
		trigger: 'change'
	  }
	]
  })
  
  const resetForm = (formEl: FormInstance | undefined) => {
	if (!formEl) return
	formEl.resetFields()
  }
  
  </script>
  
  <style></style>
  