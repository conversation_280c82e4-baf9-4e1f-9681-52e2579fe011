<template>
  <div class="crud-main" id="main-container">
    <div class="crud-main-query">
      <el-form :inline="true" class="query-form-inline">
        <el-form-item label="所属机构">
          <el-tree-select
            placeholder="请选择"
            v-model="queryForm.unitId"
            :data="unitTree"
            :default-expand-all="true"
            :check-on-click-node="true"
            check-strictly
            clearable
          />
        </el-form-item>
        <el-form-item label="用户姓名">
          <el-input placeholder="请输入用户名称" v-model="queryForm.userName" clearable />
        </el-form-item>
        <el-form-item label="用户状态">
          <el-select placeholder="请选择" clearable>
            <el-option label="有效" value="shanghai" />
            <el-option label="无效" value="beijing" />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="getPageList"
            >&nbsp;&nbsp;&nbsp;&nbsp;查询&nbsp;&nbsp;&nbsp;&nbsp;
          </el-button>
        </el-form-item>
      </el-form>
    </div>

    <div class="crud-main-content">
      <div class="mb-4 crud-main-content-toolbar">
        <el-button
          type="primary"
          icon="Plus"
          @click="
            () => {
              dialogFormVisible = true
              openAdd()
              resetForm(ruleFormRef)
            }
          "
          >新增
        </el-button>
        <el-button
          type="danger"
          icon="Delete"
          @click="_Delete()"
          :disabled="selectedRows.length === 0"
          >删除
        </el-button>
        <el-button type="success" @click="syncUser" :disabled="disabledSync"
          >&nbsp;&nbsp;&nbsp;&nbsp;同步用户&nbsp;&nbsp;&nbsp;&nbsp;
        </el-button>
      </div>

      <el-table
        :data="dataList"
        @selection-change="handleSelectionChange"
        header-cell-class-name="tocc-table-header-cell"
        header-row-class-name="tocc-table-header-row"
        row-class-name="tocc-table-row"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column type="index" label="编号" width="80" align="center" />
        <!-- <el-table-column prop="id" label="ID" width="120" /> -->
        <el-table-column prop="userName" label="用户名称" />
        <el-table-column prop="loginName" label="登录账号" />
        <el-table-column prop="unit.uname" label="所属机构" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column fixed="right" label="操作" min-width="30" align="center">
          <template #default="scope">
            <el-button link type="primary" @click="openUpdate(scope.row)">编辑</el-button>
          </template>
        </el-table-column>
      </el-table>

      <div class="crud-main-content-pagination">
        <el-pagination
          v-model:current-page="queryForm.pageNumber"
          v-model:page-size="queryForm.pageSize"
          :page-sizes="[10, 25, 50, 100]"
          layout="->,total, sizes, prev, pager, next"
          :total="queryForm.total"
          @current-change="handleCurrentChange"
          @size-change="handleSizeChange"
        />
      </div>
    </div>
  </div>

  <!-- 弹出框 -->
  <el-dialog
    class="main-form-dialog"
    v-model="dialogFormVisible"
    title="新增用户"
    style="width: 600px"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
  >
    <el-form
      :model="dataForm"
      label-width="120px"
      style="max-width: 520px"
      :rules="rules"
      ref="ruleFormRef"
    >
      <el-form-item label="上级机构" prop="parentId">
        <el-tree-select
          placeholder="选填"
          v-model="dataForm.unitId"
          :data="unitTree"
          :default-expand-all="true"
          :check-on-click-node="true"
          check-strictly
        />
      </el-form-item>
      <el-form-item label="用户名称" prop="userName">
        <el-input v-model="dataForm.userName" placeholder="必填" />
      </el-form-item>
      <el-form-item label="登录账号" prop="loginName">
        <el-input v-model="dataForm.loginName" placeholder="必填" />
      </el-form-item>
      <el-form-item label="手机号码" prop="phone">
        <el-input v-model="dataForm.phone" placeholder="选填" />
      </el-form-item>
      <el-form-item v-show="dataForm.state" label="在职状态" prop="state">
        <el-radio-group v-model="dataForm.state">
          <el-radio :label="1">在职</el-radio>
          <el-radio :label="2">离职</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="登录密码" prop="password" v-show="!dataForm.id">
        <el-input v-model="dataForm.password" type="password" placeholder="必填" />
      </el-form-item>
      <el-form-item v-show="dataForm.id" label="数据来源" prop="dataSource">
        <el-radio-group disabled v-model="dataForm.dataSource">
          <el-radio :label="1">系统内部</el-radio>
          <el-radio :label="0">第三方同步</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <div class="dialog-footer">
        <el-button v-show="dataForm.id" type="danger" @click="resetPassword(dataForm.id)">
          重置密码
        </el-button>
        <el-button type="primary" @click="_AddorUpdate(ruleFormRef)"> 确定</el-button>
        <el-button @click="dialogFormVisible = false">取消</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import type { FormInstance, FormRules } from 'element-plus'
// 用户同步
import { ElMessage, ElMessageBox, ElTable } from 'element-plus'
import { getCurrentInstance, onMounted, reactive, ref } from 'vue'

import userApi from '@/service/sys/user'
import unitTreeService from '@/service/sys/unit-tree'
import type { SyncResultVO } from '@/entity/sync.ts'

const dialogFormVisible = ref(false)

const ruleFormRef = ref<FormInstance>()

const queryForm = reactive({
  keyword: '',
  unitId: '',
  userName: '',
  pageNumber: getCurrentInstance()!.appContext.config.globalProperties.$pageNumber,
  pageSize: getCurrentInstance()!.appContext.config.globalProperties.$pageSize,
  total: 0
})

const dataForm: any = reactive({
  id: '',
  userName: '',
  loginName: '',
  password: '',
  unitId: ''
})

const unitTree = ref([])
const dataList = ref([])

onMounted(() => {
  getUnitTree()
  getPageList()
})

async function getPageList() {
  const res = await userApi.getPageList(queryForm)
  dataList.value = res.data.data.records
  queryForm.total = res.data.data.total
}

async function getUnitTree() {
  unitTree.value = await unitTreeService.getUnitTree()
}

const _AddorUpdate = async (formEl: FormInstance | undefined) => {
  if (dataForm.id == null || dataForm.id == '') {
    _Add(formEl)
  } else {
    _Update(formEl)
  }
}

/**A 新增 */
function openAdd() {
  dataForm.id = ''
}

const _Add = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (!valid) {
      return
    }
    await userApi.create(dataForm)
    dialogFormVisible.value = false
    getPageList()
  })
}

/**U 修改 */
const openUpdate = async (data: any) => {
  dialogFormVisible.value = true
  dataForm.id = data.id
  const res = await userApi.getById(data.id)
  Object.assign(dataForm, res.data.data)
  // for (let i in dataForm) {
  //   dataForm[i] = res.data.data[i]
  // }
}

const _Update = async (formEl: FormInstance | undefined) => {
  if (!formEl) return
  await formEl.validate(async (valid) => {
    if (!valid) {
      return
    }
    await userApi.update(dataForm)
    dialogFormVisible.value = false
    getPageList()
  })
}

/*** D 删除 */
const selectedRows = ref([])
const handleSelectionChange = (row: any) => {
  selectedRows.value = row
}

function _Delete() {
  ElMessageBox.confirm('请确认是否删除数据?', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    let ids: any[] = []
    selectedRows.value.forEach((row: any) => {
      ids.push(row.id)
    })
    const res = await userApi.removeAll(ids)
    getPageList()
    selectedRows.value = [] // 删除后清空选中项
  })
}

const handleSizeChange = (size: number) => {
  console.log('--------------------')
  queryForm.pageSize = size
  getPageList()
}

const handleCurrentChange = (current: number) => {
  console.log('--------------------')
  queryForm.pageNumber = current
  getPageList()
}

/****----------------------校验------------------------- */
interface RuleForm {
  id: string
  userName: string
  loginName: string
  password: string
  unitId: string
}

const rules = reactive<FormRules<RuleForm>>({
  userName: [
    {
      required: true,
      message: '用户名称不能为空',
      trigger: 'change'
    }
  ],
  loginName: [
    {
      required: true,
      message: '登录账号不能为空',
      trigger: 'change'
    }
  ],
  password: [
    {
      required: true,
      message: '登录密码不能为空',
      trigger: 'change'
    }
  ],
  unitId: [
    {
      required: false
    }
  ]
})

const resetForm = (formEl: FormInstance | undefined) => {
  if (!formEl) return
  formEl.resetFields()
}

// 用户信息同步
const disabledSync = ref(false)

function syncUser() {
  ElMessageBox.confirm('您确认要同步吗?', '用户同步', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    disabledSync.value = true
    try {
      const data = await userApi.syncUser()
      const result = data.data.data as SyncResultVO
      ElMessage({
        message: `同步成功,本次共新增${result.insertCount}条,更新${result.updateCount}条,删除${result.deleteCount}条数据。`,
        type: 'success'
      })
    } finally {
      disabledSync.value = false
    }
  })
}

function resetPassword(id: string) {
  ElMessageBox.confirm('您确认要重置该用户的密码吗?', '密码重置', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  }).then(async () => {
    const data = await userApi.resetPassword(id)
    ElMessage({
      message: `密码重置成功`,
      type: 'success'
    })
    dialogFormVisible.value = false
    getPageList()
  })
}
</script>

<style></style>
