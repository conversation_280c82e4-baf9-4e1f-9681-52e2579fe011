<template>
  <div class="container">
    <div class="parent">
      <div class="img-badge" style="height: 100px"></div>
      <div class="login-frame">
        <div class="fw-titleBar">
          <el-row class="fw-titleBar-content">
            <el-col :span="24" style="padding: 56px 0 36px 0; text-align: center">
              <div class="title">
                <span>山西省综合交通<br />运输运行监测平台</span>
              </div>
              <el-form
                style="width: 380px"
                label-width="auto"
                :model="dataForm"
                status-icon
                @keydown.enter="submitForm"
              >
                <!-- <el-form style="width: 380px" label-width="auto" :model="dataForm" status-icon> -->
                <el-form-item prop="username">
                  <el-input
                    class="transparent-input"
                    v-model="dataForm.username"
                    size="large"
                    placeholder="请输入登录账号"
                  />
                </el-form-item>
                <el-form-item prop="password">
                  <el-input
                    v-model="dataForm.password"
                    size="large"
                    placeholder="请输入登录密码"
                    type="password"
                    class="password-input"
                  />
                </el-form-item>
                <el-form-item>
                  <el-col :span="24" class="text-center" style="margin-top: 15px">
                    <el-button class="submit-btn" @click="submitForm()" size="large">
                      登录系统
                    </el-button>
                  </el-col>
                </el-form-item>
              </el-form>
            </el-col>
          </el-row>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
export default {
  name: 'LoginPage'
}
</script>

<script lang="ts" setup>
import { reactive, nextTick, onMounted, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSessionStore, type Menu } from '@/store/session'
import { ElLoading } from 'element-plus'
import loginApi from '@/service/login'

const session = useSessionStore()
const router = useRouter()

const loadingInstance = ref(null)

const dataForm = reactive({
  username: '',
  password: '',
  target: 'form'
})

const submitForm = async () => {
  console.log('----------------submitForm-------------------')

  if (loadingInstance.value != null) {
    return
  }

  if (dataForm.username == null || dataForm.username == '') {
    ElMessage({
      message: '请输入登录账号',
      type: 'warning'
    })
    return
  }
  if (dataForm.password == null || dataForm.password == '') {
    ElMessage({
      message: '请输入登录密码',
      type: 'warning'
    })
    return
  }
  session.$patch({
    token: ''
  })

  let temp = {
    ...dataForm,
    username1: dataForm.username
  }
  delete temp.username

  loadingInstance.value = ElLoading.service({
    fullscreen: false,
    target: '.login-frame',
    background: 'rgba(0, 0, 0, 0.2)' // 背景颜色
  })

  const res = await loginApi.login(temp)
  if (res.data.data.code && res.data.data.code == '-1') {
    ElMessage({
      message: res.data.data.msg,
      type: 'error'
    })
    setTimeout(() => {
      loadingInstance.value.close()
      loadingInstance.value = null
    }, 500)

    return
  }

  /** 是否跳转到领导驾驶舱 */
  let roles = res.data.data.sessionUser.roleList
  let role = roles.find((val: { roleCode: string }) => val.roleCode === 'COCKPIT_ADMIN')
  if (role) {
    if (role.roleDescription && role.roleDescription.indexOf('http') == 0) {
      window.location.href = role.roleDescription + '?' + 'token=' + res.data.data.token
    } else {
      ElMessage({
        message: '领导驾驶舱跳转路径不能为空',
        type: 'warning'
      })
      setTimeout(() => {
        loadingInstance.value.close()
        loadingInstance.value = null
      }, 500)
    }

    return
  }

  session.$patch({
    token: res.data.data.token,
    menuTree: res.data.data.menuTree,
    sessionUser: res.data.data.sessionUser
  })
  let successUrl = res.data.data.sessionUser?.sysSettingMap?.successUrl
  console.log(successUrl)
  if (successUrl) {
    router.push({ name: successUrl })
  } else {
    router.push({ name: 'highwayMonitor' })
  }
}
</script>

<style lang="less" scoped>
.container {
  background-image: url('@/assets/images/login/背景.gif');
  background-size: cover;
  /* 设置白色背景 */
  min-height: 100vh;
  /* 确保背景覆盖整个视口高度 */
  /* opacity: 0.7; */
  /* background: rgba(0, 24, 42, 1); */
  display: flex;
  justify-content: center;
  align-items: center;
  height: 100%;
  background-repeat: no-repeat;
  background-position: center;
}

.parent {
  position: relative;
  margin-bottom: 150px;
}

.img-badge {
  position: relative;
  top: 50px;
  z-index: 999;
  display: flex;
  justify-content: center;
}

.fw-titleBar {
  width: 100%;
  display: inline-block;
  position: relative;
  background-image: url('@/assets/images/login/login-frame-bg.png');
  background-size: cover;
  background-repeat: no-repeat;
  background-position: center;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-frame {
  position: relative;
  box-sizing: border-box;
  width: 450px;
  height: 406px;
}

.title {
  word-wrap: break-word;
  font-style: normal;
  line-height: 40px;
  letter-spacing: 2px;
  text-align: center;
  font-weight: 500;
  font-size: 28px;
  padding-bottom: 40px;
  color: #d0e3ff;
  position: relative;

  &::before {
    position: absolute;
    content: '';
    width: 63px;
    height: 5px;
    background-image: url('@/assets/images/login/左-标题@1x.png');
    background-position: center;
    left: 0;
    top: 30px;
    background-size: 100% 100%;
  }

  &::after {
    position: absolute;
    content: '';
    width: 63px;
    height: 5px;
    background-image: url('@/assets/images/login/右-标题@1x.png');
    background-position: center;
    right: 0;
    top: 30px;
    background-size: 100% 100%;
  }
}

:deep(.el-input__wrapper) {
  background-color: rgba(0, 0, 0, 0);
  background-image: url('@/assets/images/login/输入框@1x.png');
  background-size: 100% 100%;
  background-repeat: no-repeat;
  background-position: center;
  box-shadow: none;
  width: 330px;
  height: 52px;
  padding-left: 50px;
  position: relative;
  font-weight: 400;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  letter-spacing: 1px;
  text-align: left;
  font-style: normal;

  &::before {
    position: absolute;
    content: '';
    width: 30px;
    height: 30px;
    left: 10px;
    background-position: center;
    background-repeat: no-repeat;
  }

  /* border: 1px solid #32c3c5; */
}

:deep(.el-input__wrapper:nth-child(1)::before) {
  background-image: url('@/assets/images/login/账户名@1x.png');
}

:deep(.password-input .el-input__wrapper::before) {
  background-image: url('@/assets/images/login/密码@1x.png');
}

:deep(.el-input__inner) {
  background-color: rgba(0, 0, 0, 0) !important;
  color: #fff;
  font-weight: 400;
  font-size: 14px;
}

:deep(.el-input input:focus) {
  outline: none;
  border-color: none;
  /* 你可以根据需要修改边框颜色 */
  box-shadow: none;
}

:deep(input:-webkit-autofill) {
  -webkit-text-fill-color: #fff;
  transition: background-color 5000s ease-out 0.5s;
}

:deep(input::-webkit-input-placeholder) {
  color: #fff;
}

:deep(.el-input) {
  input {
    box-shadow: none;
  }
}

:deep(.el-button--large) {
  background-image: url('@/assets/images/login/登录@1x.png');
  background-repeat: no-repeat;
  background-size: 100% 100%;
  border: none;
  background-color: transparent;
  width: 330px;
  height: 43px;
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  line-height: 20px;
  letter-spacing: 1px;
  text-align: center;

  &:active,
  &:hover {
    opacity: 0.8;
  }

  font-style: normal;
}

.submit-btn {
  color: white;
  width: 100%;
}
</style>
