<template>
  <ItemTitle title="公路路网基础指标" :show-more="true" 
    :more-click="() => roadBaseRef?.handleMoreClick()" more-text="公路里程占比">
    <RoadBase ref="roadBaseRef"/>
  </ItemTitle>

  <ItemTitle title="交通量信息">
    <TrafficInfo ref="trafficInfoRef"/>
  </ItemTitle>


</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

import ItemTitle from '@/components/common/ItemTitle.vue'
import RoadBase from './RoadBase.vue'
import TrafficInfo from './TrafficInfo.vue'

const roadBaseRef = ref<InstanceType<typeof RoadBase> | null>(null)
const trafficInfoRef = ref<InstanceType<typeof TrafficInfo> | null>(null)





</script>

