
<template>
  <div class="flex-box">
    <StatisCard v-for="(item, index) in tags_data" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.img" />
  </div>
  <RightDataDetailPopup 
    title="公路总里程占比" 
    v-model:visible="showDetail"
    width="400px"
    >
    <el-select v-model="area" @change=""
      placeholder="地区" 
      style="width: 100px">
        <el-option v-for="(item, index) in areaOptions" 
          :label="item.label" :value="item.value"
        />
    </el-select>

    <div class="components-container chart-container-350">
        <div ref="chartRef" class="echart-style" style="height: 100%;width: 100%;"></div>
    </div>

  </RightDataDetailPopup>
</template>

<script setup lang="ts">
import {nextTick, onMounted, ref} from "vue";
import StatisCard from "@/components/common/StatisCard.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import { barLineChartConfig } from "@/echarts/barConfig";
import { useECharts } from "@/hooks/useECharts";
import { pieChartOption } from "@/components/v2/echarts/options/bar-chart-options";

const tags_data = ref([
  {
    name: '普通公路总里程',
    count: 123,
    unit: 'km',
    img: 'enterprise-count',
  },
  {
    name: '桥梁数',
    count: 12,
    unit: '座',
    img: 'enterprise-count',
  },
  {
    name: '隧道数',
    count: 12,
    unit: '条',
    img: 'enterprise-count'
  },
  {
    name: '涵洞数',
    count: 80,
    unit: '个',
    img: 'enterprise-count'
  },
  {
    name: '危桥数',
    count: 80,
    unit: '座',
    img: 'enterprise-count'
  },
  {
    name: '治超站数',
    count: 60,
    unit: '个',
    img: 'enterprise-count'
  },
  {
    name: '边坡',
    count: 60,
    unit: '米',
    img: 'enterprise-count'
  },
  {
    name: '公路管理站数',
    count: 60,
    unit: '个',
    img: 'enterprise-count'
  },
  {
    name: '断面观测站数',
    count: 60,
    unit: '个',
    img: 'enterprise-count'
  },
  {
    name: '应急中心',
    count: 60,
    unit: '个',
    img: 'enterprise-count'
  }
])

const areaOptions = ref([
  {label: '全市', value: '全市'},
  {label: '原平市', value: '原平市'},
  {label: '五台县', value: '五台县'}
])
const area = ref('全市')

const showDetail = ref(false)

function handleMoreClick() {
  showDetail.value = true
  nextTick(()=>{
    resetAfterInitChart()
    loadChartData()
  })
}

onMounted(()=> {

})

const chartRef = ref<HTMLElement | null>(null);
const chartConfig: any = pieChartOption([])
const { updateChart, emptyDataCheck, resetAfterInitChart } = useECharts(chartConfig, chartRef, true);

const loadChartData = async () => {
  chartConfig.series[0].data = [
    { name: '高速', value: 20 },
    { name: '国道', value: 20 },
    { name: '省道', value: 10 },
    { name: '县道', value: 10 },
    { name: '乡道', value: 10 },
    { name: '村道', value: 10 },
    { name: '其他', value: 10 }]
  emptyDataCheck(chartConfig.series[0].data)
  updateChart(chartConfig)
}


defineExpose({
  handleMoreClick
})
</script>

<style scoped lang="scss">

</style>