
<template>
  <div class="flex-box">
    <StatisCard v-for="(item, index) in tags_data" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.img" />
  </div>
  <div class="components-container chart-container-200">
      <div ref="lineChart" class="echart-style" style="height: 100%;width: 100%;"></div>
  </div>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import StatisCard from "@/components/common/StatisCard.vue";
import { barLineChartConfig } from "@/echarts/barConfig";
import { useECharts } from "@/hooks/useECharts";

const tags_data = ref([
  {
    name: '今日累计交通量',
    count: 60,
    unit: '量',
    img: 'enterprise-count'
  },
  {
    name: '今日超限车辆',
    count: 120,
    unit: '辆',
    img: 'enterprise-count'
  }
])

onMounted(()=> {
  lineChartData()
})

const lineChart = ref<HTMLElement | null>(null);
const chartConfig = barLineChartConfig();
chartConfig.grid.top = 30
chartConfig.grid.bottom = 20
chartConfig.grid.right = '2%'
chartConfig.yAxis.splitNumber = 2
chartConfig.yAxis[0].name = '单位：辆'
chartConfig.legend.data = ['超限车辆','交通量']
chartConfig.series[0].name = '交通量'
chartConfig.series[1].name = '超限车辆'
const { updateChart, emptyDataCheck } = useECharts(chartConfig, lineChart);

const lineChartData = async () => {
  chartConfig.xAxis.data = ['05/01', '05/02', '05/03', '05/04', '05/05']
  chartConfig.series[0].data = [100, 200, 300, 400, 500]
  chartConfig.series[1].data = [30, 10, 80, 55, 100]
  emptyDataCheck(chartConfig.series[0].data)
  updateChart(chartConfig)
}
</script>

<style scoped lang="scss">

</style>