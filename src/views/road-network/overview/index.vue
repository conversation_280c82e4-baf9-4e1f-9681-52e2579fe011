<template>
  <div class="full-container">
    <LeftDrawer>
      <Tabs :menus="tabs_data" v-slot="{ active }">
        <template v-if="active == 0">
          <DataView/>
        </template>
        <template v-if="active == 1">
          <!-- <DataMap/> -->
        </template>
        <template v-if="active == 2">
          <!-- <DataMonitor/> -->
        </template>
      </Tabs>
    </LeftDrawer>
  </div>
</template>
<script lang="ts" setup>
import LeftDrawer from '@/components/v2/LeftDrawer.vue'
import Tabs from '@/components/v2/Tabs.vue'

import DataView from './compents/DataView.vue'
import DataMap from './compents/DataMap.vue'
import DataMonitor from './compents/DataMonitor.vue'

const tabs_data: any[] = [
  {
    name: '路网概览'
  },
  {
    name: '公路路网地图'
  },
  {
    name: '视频监控'
  }
]

</script>
<style scoped lang="scss">

</style>
