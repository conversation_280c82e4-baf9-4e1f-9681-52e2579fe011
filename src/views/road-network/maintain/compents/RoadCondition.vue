
<template>
  <div class="components-container chart-container-250">
      <div ref="chartRef" class="echart-style" style="height: 100%;width: 100%;"></div>
  </div>
  <RightDataDetailPopup 
    title="MQI指标排名" 
    v-model:visible="showDetail"
    width="400px"
    >
    <el-select v-model="area" @change=""
      placeholder="地区" 
      style="width: 100px">
        <el-option v-for="(item, index) in areaOptions" 
          :label="item.label" :value="item.value"
        />
    </el-select>

  </RightDataDetailPopup>
</template>

<script setup lang="ts">
import {nextTick, onMounted, ref} from "vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import { useECharts } from "@/hooks/useECharts";
import { stackedRadarChart } from "@/components/v2/echarts/options/radar-chart-options";



const areaOptions = ref([
  {label: '全市', value: '全市'},
  {label: '原平市', value: '原平市'},
  {label: '五台县', value: '五台县'}
])
const area = ref('全市')

const showDetail = ref(false)

function handleMoreClick() {
  showDetail.value = true
}


const chartRef = ref<HTMLElement | null>(null);
let chartConfig: any = stackedRadarChart({
      legendData: [],
      indicator: [],
      data: []
    })
const { updateChart, emptyDataCheck } = useECharts(chartConfig, chartRef, true);

onMounted(() => {
  loadChartData()
})

const indicator = [
  {
    name: "沿线设施",
    max: 100,
  },
  {
    name: "桥隧构造物",
    max: 100,
  },
  {
    name: "路基",
    max: 100,
  },
  {
    name: "路面",
    max: 100,
  },
  {
    name: "其他",
    max: 100,
  }]

const loadChartData = async () => {
  let data = [[[60, 60, 65, 60, 70]]]
  chartConfig = stackedRadarChart({
      legendData: ['MQI'],
      indicator: indicator,
      data: data
    })
  emptyDataCheck(data)
  updateChart(chartConfig)
}

onMounted(()=> {

})



defineExpose({
  handleMoreClick
})
</script>

<style scoped lang="scss">

</style>