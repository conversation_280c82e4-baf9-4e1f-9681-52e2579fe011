
<template>
  <div class="components-container chart-container-250">
      <div ref="chartRef" class="echart-style" style="height: 100%;width: 100%;"></div>
  </div>
  <RightDataDetailPopup 
    title="路面灾害详情" 
    v-model:visible="showDetail"
    width="400px"
    >
    <el-select v-model="area" @change=""
      placeholder="地区" 
      style="width: 100px">
        <el-option v-for="(item, index) in areaOptions" 
          :label="item.label" :value="item.value"
        />
    </el-select>

  </RightDataDetailPopup>
</template>

<script setup lang="ts">
import {nextTick, onMounted, ref} from "vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import { useECharts } from "@/hooks/useECharts";
import { borderLineChart } from "@/components/v2/echarts/options/line-chart-options";



const areaOptions = ref([
  {label: '全市', value: '全市'},
  {label: '原平市', value: '原平市'},
  {label: '五台县', value: '五台县'}
])
const area = ref('全市')

const showDetail = ref(false)

function handleMoreClick() {
  showDetail.value = true
}


const chartRef = ref<HTMLElement | null>(null);
let chartConfig: any = borderLineChart({
      legendData: [],
      xAxisData: [],
      data: []
    })
const { updateChart, emptyDataCheck } = useECharts(chartConfig, chartRef, true);

onMounted(() => {
  loadChartData()
})

const loadChartData = async () => {
  let data = [[820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290, 820, 932]]
  chartConfig = borderLineChart({
    legendData: ['数量'],
    xAxisData: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12],
    data: data,
    colors: ['rgba(113, 204, 251, 0.3)', 'rgb(156,255,224)']
  })
  chartConfig.legend.show = false
  chartConfig.grid.top = 20
  emptyDataCheck(data)
  updateChart(chartConfig)
}

onMounted(()=> {

})



defineExpose({
  handleMoreClick
})
</script>

<style scoped lang="scss">

</style>