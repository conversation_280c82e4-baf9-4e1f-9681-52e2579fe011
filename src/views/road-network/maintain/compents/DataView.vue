<template>
  <ItemTitle title="基础指标" :show-more="true" 
    :more-click="() => roadBaseRef?.handleMoreClick()" more-text="更多">
    <RoadBase ref="roadBaseRef"/>
  </ItemTitle>

  <ItemTitle title="路面灾害趋势" :show-more="true" 
    :more-click="() => roadDisasterRef?.handleMoreClick()" more-text="更多">
    <RoadDisaster ref="roadDisasterRef"/>
  </ItemTitle>

  <ItemTitle title="公路技术状况" :show-more="true" 
    :more-click="() => roadDisasterRef?.handleMoreClick()" more-text="MQI排名">
    <RoadCondition ref="roadConditionRef"/>
  </ItemTitle>


</template>

<script setup lang="ts">
import { onMounted, ref } from 'vue'

import ItemTitle from '@/components/common/ItemTitle.vue'
import RoadBase from './RoadBase.vue'
import RoadDisaster from './RoadDisaster.vue'
import RoadCondition from './RoadCondition.vue'

const roadBaseRef = ref<InstanceType<typeof RoadBase> | null>(null)
const roadDisasterRef = ref<InstanceType<typeof RoadDisaster> | null>(null)
const roadConditionRef = ref<InstanceType<typeof RoadCondition> | null>(null)





</script>

