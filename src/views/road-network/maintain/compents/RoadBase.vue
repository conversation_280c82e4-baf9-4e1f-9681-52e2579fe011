
<template>
  <div class="flex-box">
    <StatisCard v-for="(item, index) in tags_data" :key="index" :title="item.name" :count="item.count" :unit="item.unit" :img="item.img" />
  </div>
  <RightDataDetailPopup 
    title="路面灾害详情" 
    v-model:visible="showDetail"
    width="400px"
    >
    <el-select v-model="area" @change=""
      placeholder="地区" 
      style="width: 100px">
        <el-option v-for="(item, index) in areaOptions" 
          :label="item.label" :value="item.value"
        />
    </el-select>

  </RightDataDetailPopup>
</template>

<script setup lang="ts">
import {nextTick, onMounted, ref} from "vue";
import StatisCard from "@/components/common/StatisCard.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import { barLineChartConfig } from "@/echarts/barConfig";
import { useECharts } from "@/hooks/useECharts";
import { pieChartOption } from "@/components/v2/echarts/options/bar-chart-options";

const tags_data = ref([
  {
    name: '养护企业',
    count: 123,
    unit: '个',
    img: 'enterprise-count',
  },
  {
    name: '养护工程',
    count: 12,
    unit: '个',
    img: 'enterprise-count',
  },
  {
    name: '道路灾害',
    count: 12,
    unit: '个',
    img: 'enterprise-count'
  }
])

const areaOptions = ref([
  {label: '全市', value: '全市'},
  {label: '原平市', value: '原平市'},
  {label: '五台县', value: '五台县'}
])
const area = ref('全市')

const showDetail = ref(false)

function handleMoreClick() {
  showDetail.value = true
}

onMounted(()=> {

})



defineExpose({
  handleMoreClick
})
</script>

<style scoped lang="scss">

</style>