<template>
  <div class="full-container">
    <LeftDrawer>
      <Tabs :menus="tabs_data" v-slot="{ active }">
        <template v-if="active == 0">
          <DataView/>
        </template>
        <template v-if="active == 1">

        </template>
        <template v-if="active == 2">

        </template>
      </Tabs>
    </LeftDrawer>
  </div>
</template>
<script lang="ts" setup>
import LeftDrawer from '@/components/v2/LeftDrawer.vue'
import Tabs from '@/components/v2/Tabs.vue'

import DataView from './compents/DataView.vue'


const tabs_data: any[] = [
  {
    name: '公路养护概览'
  },
  {
    name: '公路路网地图'
  },
  {
    name: '视频监控'
  }
]

</script>
<style scoped lang="scss">

</style>
