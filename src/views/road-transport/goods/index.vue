<!--
@description: TODO 客运
@author: liudingbang
@date: 2025/5/30 10:01
-->
<template>
  <div class="full-container">
    <!--左侧抽屉-->
    <LeftDrawer :show="show_left_panel">
      <Tabs :menus="menu[Number(currentMenuIndex)]!.children![0]!.tabs_data" v-slot="{active}">
        <template v-if="active == 0">
          <ItemTitle title="基础指标">
            <BasicIndex ref="BasicIndexRef"/>
          </ItemTitle>
          <ItemTitle title="重型货车占比" :more-click="() => handleMoreClick('重型货车占比')">
            <WeightCarRate ref="TodaySidePointMonitorRef"/>
          </ItemTitle>
        </template>
        <template v-if="active == 1">
          <VehicleTree :data-tree="dataTree"/>
        </template>
        <template v-if="active == 2">
          <ItemTitle title="今日预警记录" :more-click="() => handleMoreClick('今日预警记录')">
            <TodayWarnRecord ref="TodayWarnRecordRef"/>
          </ItemTitle>
        </template>
      </Tabs>
    </LeftDrawer>
    <!--地图-->
    <!--todo 地图图例及其修饰信息-->
  </div>
</template>
<script lang="ts" setup>
import {ref} from 'vue'
import {useStore} from '@/store'
import {storeToRefs} from 'pinia'
import LeftDrawer from "@/components/v2/LeftDrawer.vue";
import Tabs from "@/components/v2/Tabs.vue";
import ItemTitle from "@/components/common/ItemTitle.vue";
import BasicIndex from "@/views/road-transport/goods/components/data-total/BasicIndex.vue";
import WeightCarRate from "@/views/road-transport/goods/components/data-total/WeightCarRate.vue";
import TodayWarnRecord from "@/views/road-transport/passenger/components/monitor-warn/TodayWarnRecord.vue";
import VehicleTree from "@/components/common/VehicleTree.vue";

const store = useStore()
const show_left_panel = ref(true)
const {menu, currentMenuIndex} = storeToRefs(store)

const BasicIndexRef = ref<InstanceType<typeof BasicIndex> | null>(null)
const TodaySidePointMonitorRef = ref<InstanceType<typeof WeightCarRate> | null>(null)
const TodayWarnRecordRef = ref<InstanceType<typeof TodayWarnRecord> | null>(null)

/**
 * 更多按钮点击
 * @param type 点击类型
 */
function handleMoreClick(type: '基础指标' | '重型货车占比' | '今日预警记录'): void {
  switch (type) {
    case '基础指标':
      BasicIndexRef.value?.handleMoreClick()
      break;
    case '重型货车占比':
      TodaySidePointMonitorRef.value?.handleMoreClick()
      break;
    case "今日预警记录":
      TodayWarnRecordRef.value?.handleMoreClick()
      break
  }
}

const dataTree: unknown[] = [
  {
    id: '1',
    label: 'XX公司(5/7)',
    children: [
      {
        id: '1-1',
        label: '晋A15171 赵小刚',
        online: true,
        lng: 112.735295,
        lat: 38.448514
      },
      {
        id: '1-2',
        label: '晋A15172 赵小刚',
        online: true,
        lng: 112.73368581216272,
        lat: 38.410806760867665
      },
      {
        id: '1-3',
        label: '晋A15173 赵小刚',
        online: true,
      },
      {
        id: '1-4',
        label: '晋A15174 赵小刚',
        online: false,
      },
    ],
  },
  {
    id: '2',
    label: 'XX公司(5/7)',
    children: [
      {
        id: '2-1',
        label: '晋B15171 赵小刚',
        online: true,
      },
      {
        id: '2-2',
        label: '晋B15172 赵小刚',
      },
      {
        id: '2-3',
        label: '晋B15171 赵小刚',
      },
      {
        id: '2-4',
        label: '晋B15172 赵小刚',
      },
    ],
  },
]
</script>
<style scoped lang="scss">

</style>
