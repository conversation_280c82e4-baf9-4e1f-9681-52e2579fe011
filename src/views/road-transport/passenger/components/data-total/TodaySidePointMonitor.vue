<!--
@description: TODO 今日客运站点监测
@author: liudingbang
@date: 2025/5/30 15:00
-->

<template>
  <Echarts :options="options" :class-name="'today-side-point-chart'"/>
  <RightDataDetailPopup title="客运站发车班次详情" v-model:visible="visible">
    <BaseTable :action-fixed-right="true" :api="page" :export-api="exportExcel" export-file-name="客运站发车班次详情"  :extra-table-config="{'max-height': '45vh'}">
      <template #search>
        <DateRangeSearch v-model:date="date"/>
      </template>
      <template #table-top>
        <Echarts :options="tableOptions" class-name="chart"/>
      </template>
      <template #default>
        <el-table-column type="index" label="序号" width="60"/>
        <el-table-column prop="dt" label="时间"/>
        <el-table-column prop="name" label="客运站名称"/>
        <el-table-column prop="warn_cnt" label="计划发车班次"/>
        <el-table-column prop="warn_cnt" label="实际发车班次"/>
      </template>
    </BaseTable>
  </RightDataDetailPopup>
</template>

<script setup lang="ts">
import Echarts from "@/components/v2/echarts/Echarts.vue";
import {onMounted, ref} from "vue";
import type {EChartsOption} from "echarts";
import * as echarts from "echarts";
import {borderLineChart, stackedLineChart} from "@/components/v2/echarts/options/line-chart-options";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import http from '@/utils/http/http'

let options = ref<EChartsOption | undefined>({})

onMounted(() => {
  const data3: Array<number> = []
  const data4: Array<number> = []
  for (let i = 0; i < 7; i++) {
    data3.push(+Math.random().toFixed(2))
    data4.push(+Math.random().toFixed(2))
  }
  setTimeout(() => {
    options.value = stackedLineChart({
      legendData: ['入市', '出市'],
      xAxisData: ['忻州汽车客运站', '忻州客运中心', '砂河汽车站', '岢岚汽车客运站', '五台山汽车站', '五寨汽车站', '保德汽车站'],
      data: [data3, data4]
    })
  }, 200)
})

/**
 * 更多
 */
const visible = ref(false)

let tableOptions = ref()
function handleMoreClick() {
  setTimeout(() => {
    tableOptions.value = borderLineChart({
      legendData: ['入市', '出市'],
      xAxisData: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24],
      data: [[820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290], [220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090]],
      colors: ['rgba(113, 204, 251, 0.3)', 'rgb(156,255,224)']
    })
  }, 200)
  visible.value = true
}

const date = ref([])

async function page(params: object) {
  const {data} = await http.post('/tdss/cockpit/highway/operatorIndex', params, {target: '.el-table'})
  return data
}

async function exportExcel(params: object) {
  const {data} = await http.post('/export', params, {responseType: "blob"})
  return data
}


defineExpose({
  handleMoreClick
})
</script>

<style scoped>
.today-side-point-chart {
  width: 100%;
  height: 23vh;
}
.chart {
  width: 100%;
  height: 200px;
}
</style>