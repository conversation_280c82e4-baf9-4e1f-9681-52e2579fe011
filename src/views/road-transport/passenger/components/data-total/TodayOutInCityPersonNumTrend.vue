<!--
@description: TODO 今日出入市客运量趋势
@author: liudingbang
@date: 2025/5/30 15:00
-->

<template>
  <Echarts :options="options" class-name="chart"/>
  <RightDataDetailPopup title="客运量出入市详情" v-model:visible="visible">
    <BaseTable :action-fixed-right="true" :api="page" :export-api="exportExcel" export-file-name="客运站发车班次详情">
      <template #search>
        <DateRangeSearch v-model:date="date"/>
      </template>
      <template #default>
        <el-table-column type="index" label="序号" width="60"/>
        <el-table-column prop="dt" label="时间"/>
        <el-table-column prop="name" label="客运站名称"/>
        <el-table-column prop="warn_cnt" label="入市"/>
        <el-table-column prop="warn_cnt" label="出市"/>
      </template>
    </BaseTable>
  </RightDataDetailPopup>
</template>

<script setup lang="ts">
import Echarts from "@/components/v2/echarts/Echarts.vue";
import {onMounted, ref} from "vue";
import type {EChartsOption} from "echarts";
import {borderLineChart} from "@/components/v2/echarts/options/line-chart-options";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";

let options = ref<EChartsOption | null>()

onMounted(() => {
  setTimeout(() => {
    options.value = borderLineChart({
      legendData: ['入市', '出市'],
      xAxisData: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24],
      data: [[820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290, 820, 932, 901, 934, 1290], [220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090, 220, 732, 601, 834, 1090]],
      colors: ['rgba(113, 204, 251, 0.3)', 'rgb(156,255,224)']
    })
  }, 200)
})


/**
 * 更多
 */
const visible = ref(false)
function handleMoreClick() {
  visible.value  = true
}
import http from '@/utils/http/http'
const date = ref([])
async function page (params: object) {
  const {data} = await http.post('/tdss/cockpit/highway/operatorIndex', params, {target: '.el-table'})
  return data
}

async function exportExcel (params: object) {
  const {data} = await http.post('/export', params, {responseType: "blob"})
  return data
}


defineExpose({
  handleMoreClick
})
</script>

<style scoped>
.chart {
  width: 100%;
  height: 25vh;
}
</style>