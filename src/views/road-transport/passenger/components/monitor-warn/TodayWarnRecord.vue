<!--
@description: TODO 今日预警记录
@author: liudingbang
@date: 2025/6/5 10:22
-->

<template>
  <div class="el-collapse-box">
    <el-collapse accordion>
      <el-collapse-item name="1">
        <template #title="{ isActive }">
          <div class="item-info">
            <img class="item-icon" :src="cs" alt=""/>
            超速报警
          </div>
        </template>
        <div>
          <simple-list @row-click="handleClick"  :data="data" scroll-height="46vh" :page-size="data.length"
                       :fields="['plate', 'name', 'rank', 'time']"/>
        </div>
      </el-collapse-item>
      <el-collapse-item title="长期不在线预警" name="2">
        <template #title="{ isActive }">
          <div class="item-info">
            <img class="item-icon" :src="bzx" alt=""/>
            长期不在线预警
          </div>
        </template>
        <div>
          <simple-list @row-click="handleClick"  :data="data" scroll-height="46vh" :page-size="data.length"
                       :fields="['plate', 'name', 'rank', 'time']"/>
        </div>
      </el-collapse-item>
      <el-collapse-item title="疲劳驾驶预警" name="3">
        <template #title="{ isActive }">
          <div class="item-info">
            <img class="item-icon" :src="pl" alt=""/>
            疲劳驾驶预警
          </div>
        </template>
        <div>
          <simple-list @row-click="handleClick"  :data="data" :fields="['plate', 'name', 'rank', 'time']" scroll-height="46vh"
                       :page-size="data.length"/>
        </div>
      </el-collapse-item>
      <el-collapse-item title="非运营时段行驶预警" name="4">
        <template #title="{ isActive }">
          <div class="item-info">
            <img class="item-icon" :src="fyy" alt=""/>
            非运营时段行驶预警
          </div>
        </template>
        <div>
          <simple-list @row-click="handleClick" :data="data" scroll-height="46vh" :page-size="data.length"
                       :fields="['plate', 'name', 'rank', 'time']"/>
        </div>
      </el-collapse-item>
    </el-collapse>
  </div>
  <RightDataDetailPopup v-model:visible="visible" title="预警车辆详情">
    <BaseTable @pageBefore="handlerPageBefore" :api="page" :export-api="exportExcel" export-file-name="预警车辆详情">
      <template #search>
        <el-row :gutter="20">
          <el-col :span="11">
            <el-date-picker
                v-model="searchParams.date"
                type="daterange"
                range-separator="至"
                start-placeholder="开始日期"
                end-placeholder="结束日期">
            </el-date-picker>
          </el-col>
          <el-col :span="5">
            <el-input
                placeholder="车牌号"
                :suffix-icon="Search"
                v-model="searchParams.carId">
            </el-input>
          </el-col>
          <el-col :span="5">
            <el-input
                placeholder="驾驶员"
                :suffix-icon="Search"
                v-model="searchParams.person">
            </el-input>
          </el-col>
        </el-row>

      </template>
      <template #default="scope">
        <el-table-column type="index" label="序号" width="60"/>
        <el-table-column prop="dt" label="报警时间"/>
        <el-table-column prop="name" label="所属企业"/>
        <el-table-column prop="name" label="车牌号"/>
        <el-table-column prop="name" label="驾驶员"/>
        <el-table-column prop="name" label="车辆类型"/>
        <el-table-column prop="name" label="报警类型"/>
        <el-table-column prop="name" label="报警级别"/>
        <el-table-column prop="name" label="报警信息"/>
        <el-table-column
            fixed="right"
            label="操作"
            width="100">
          <template #default="item">
            <el-button @click="handleClick(item)" type="text" size="small">详情</el-button>
          </template>
        </el-table-column>
      </template>
    </BaseTable>
  </RightDataDetailPopup>
  <AlertorDialog :dialog-visible="dialogVisible"/>
</template>

<script setup lang="ts">
import {onMounted, onUnmounted, reactive, ref} from 'vue'
import pl from '@/assets/images/transportation/pl.png'
import cs from '@/assets/images/transportation/cs.png'
import bzx from '@/assets/images/transportation/bzx.png'
import fyy from '@/assets/images/transportation/fyy.png'

import SimpleList from '@/components/v2/SimpleList.vue'
import http from '@/utils/http/http'
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";
import {Search} from "@element-plus/icons-vue";
import AlertorDialog from "@/components/v2/alertor-dialog.vue";


const data = ref([
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  },
  {
    plate: '晋B 080MH',
    name: '张三',
    rank: 1,
    time: '12:00:00'
  }
])

function handleClick(item: object) {
  // todo
  console.log(item)
  dialogVisible.value = true
}

let searchParams = reactive<any>({})

function handlerPageBefore(params: object) {
  Object.assign(params, searchParams)
}

/**
 * 更多
 */
const visible = ref(false)

function handleMoreClick() {
  visible.value = true
}

const date = ref([])

async function page(params: object) {
  const {data} = await http.post('/tdss/cockpit/highway/operatorIndex', params, {target: '.el-table'})
  return data
}

async function exportExcel(params: object) {
  const {data} = await http.post('/export', params, {responseType: "blob"})
  return data
}

const dialogVisible  = ref(false)

defineExpose({
  handleMoreClick
})

</script>

<style scoped>
.el-collapse-box {
  overflow: hidden;
}

.title {
  display: flex;
  justify-content: space-between;
  padding: 10px 0 20px 0;
  margin: 0 10px;

  .left {
    font-weight: 500;
    font-size: var(--menu-item-font-2);
    color: var(--color-text-emphasize1);
  }

  .right {
    width: 65px;
    height: 28px;
    text-align: center;
    line-height: 28px;
    background: rgba(22, 93, 255, 0.1);
    border-radius: 4px;
    color: var(--more-btn-color);
    font-size: 16px;
    cursor: pointer;
  }
}

:deep(.el-collapse-item__header) {
  height: 60px;
}
</style>
