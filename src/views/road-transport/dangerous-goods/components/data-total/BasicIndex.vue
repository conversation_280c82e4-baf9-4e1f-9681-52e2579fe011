<!--
@description: TODO 基础指标
@author: liudingbang
@date: 2025/5/30 14:56
-->

<template>
  <div class="num_tags">
    <StatisCard
        v-for="(item, index) in tags_data"
        :key="index"
        :title="item.name"
        :count="item.count"
        :unit="item.unit"
        :img="item.img"
    />
  </div>
  <RightDataDetailPopup title="危货运行车辆详情" v-model:visible="showDetail">
    <BaseTable :action-fixed-right="true" :api="page" :export-api="exportExcel" export-file-name="危货运行车辆详情">
      <template #search>
        <DateRangeSearch v-model:date="date"/>
      </template>
      <template #default="scope">
        <el-table-column type="index" label="序号" width="60"/>
        <el-table-column prop="dt" label="时间"/>
        <el-table-column prop="name" label="危货运输车辆"/>
        <el-table-column prop="warn_cnt" label="入网率"/>
        <el-table-column prop="warn_cnt" label="上线率"/>
      </template>
    </BaseTable>

  </RightDataDetailPopup>
</template>

<script setup lang="ts">
import {onMounted, ref} from "vue";
import qy from "@/assets/images/transportation/qy.png";
import qy_active from "@/assets/images/transportation/qy_active.png";
import zs from "@/assets/images/transportation/zs.png";
import zs_active from "@/assets/images/transportation/zs_active.png";
import cyry from "@/assets/images/transportation/cyry.png";
import cyry_active from "@/assets/images/transportation/cyry_active.png";
import cl from "@/assets/images/transportation/cl.png";
import cl_active from "@/assets/images/transportation/cl_active.png";
import rw from "@/assets/images/transportation/rw.png";
import rw_active from "@/assets/images/transportation/rw_active.png";
import sxl from "@/assets/images/transportation/sxl.png";
import sxl_active from "@/assets/images/transportation/sxl_active.png";
import StatisCard from "@/components/common/StatisCard.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import {useStore} from "@/store";
import {storeToRefs} from "pinia";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import {Dayjs} from "dayjs";
import BaseTable from "@/components/v2/table/BaseTable.vue";

const tags_data = ref([
  {
    name: '企业数量',
    count: 12,
    unit: '家',
    icon: qy,
    active_icon: qy_active,img: 'enterprise-count'
  },
  {
    name: '车辆总数',
    count: 12,
    unit: '辆',
    icon: zs,
    active_icon: zs_active,img: 'vehicle-count'
  },
  {
    name: '从业人员',
    count: 12,
    unit: '人',
    icon: cyry,
    active_icon: cyry_active,
    img: 'employees'
  },
  {
    name: '今日运行车辆总数',
    count: 123,
    unit: '辆',
    icon: cl,
    active_icon: cl_active,
    img: 'today-vehicle-count'
  },
  {
    name: '今日车辆入网率',
    count: 80,
    unit: '%',
    icon: rw,
    active_icon: rw_active,
    img: 'vehicle-network'
  },
  {
    name: '今日车辆上线率',
    count: 60,
    unit: '%',
    icon: sxl,
    active_icon: sxl_active,
    img: 'vehicle-online'
  }
])

onMounted(() => {
  // todo
})

import http from '@/utils/http/http'

async function page (params: object) {
  const {data} = await http.post('/tdss/cockpit/highway/operatorIndex', params, {target: '.el-table'})
  return data
}

async function exportExcel (params: object) {
  const {data} = await http.post('/export', params, {responseType: "blob"})
  return data
}


const date = ref<Array<Dayjs>>()


// 详情
const showDetail = ref(false)

function handleMoreClick() {
  showDetail.value = true
}

const store = useStore()
const {currentSonMenuIndex} = storeToRefs(store)
const mock_data = [
  [
    {
      time: '2025年5月13日',
      ct: 123,
      ly: 234,
      ctrwl: '80%',
      lyrwl: '80%',
      ctonline: '80%',
      lyonline: '80%'
    },
    {
      time: '2025年5月13日',
      ct: 123,
      ly: 234,
      ctrwl: '80%',
      lyrwl: '80%',
      ctonline: '80%',
      lyonline: '80%'
    },
    {
      time: '2025年5月13日',
      ct: 123,
      ly: 234,
      ctrwl: '80%',
      lyrwl: '80%',
      ctonline: '80%',
      lyonline: '80%'
    },
    {
      time: '2025年5月13日',
      ct: 123,
      ly: 234,
      ctrwl: '80%',
      lyrwl: '80%',
      ctonline: '80%',
      lyonline: '80%'
    },
    {
      time: '2025年5月13日',
      ct: 123,
      ly: 234,
      ctrwl: '80%',
      lyrwl: '80%',
      ctonline: '80%',
      lyonline: '80%'
    },
    {
      time: '2025年5月13日',
      ct: 123,
      ly: 234,
      ctrwl: '80%',
      lyrwl: '80%',
      ctonline: '80%',
      lyonline: '80%'
    },
    {
      time: '2025年5月13日',
      ct: 123,
      ly: 234,
      ctrwl: '80%',
      lyrwl: '80%',
      ctonline: '80%',
      lyonline: '80%'
    }
  ],
  [
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    },
    {
      time: '2025年5月13日',
      carNum: '123',
      in: '89%',
      online: '80%'
    }
  ],
  [
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    },
    {
      time: '2025年5月13日',
      total: '123',
      zstb: '12%',
      zshb: '12%',
      xb: '129',
      xbtb: '12%',
      xbhb: '12%',
      zx: '123',
      zxtb: '12%',
      zxhb: '12%',
      qc: '123',
      qctb: '12%',
      qchb: '12%'
    }
  ]
]

defineExpose({
  handleMoreClick
})
</script>

<style scoped lang="scss">
.num_tags_header {
  display: flex;
  justify-content: space-between;
  padding: 0 10px;
  margin-top: 10px;

  .title {
    font-family: Alibaba-PuHuiTi, Alibaba-PuHuiTi;
    font-weight: 500;
    font-size: 16px;
    color: #1d2129;
  }
}

.num_tags {
  display: flex;
  flex-wrap: wrap;
  justify-content: space-between;
}

.pop-content {
  .search-bar {
    display: flex;
    justify-content: space-between;
  }

  .left-btn {
    display: flex;

    .el-button {
      margin: 0;
      border-radius: 0;
    }

    .margin10 {
      margin-right: 10px;
    }
  }
}

.table-box {
  width: 100%;
  height: calc(100vh - 250px);
}

.page {
  display: flex;
  justify-content: end;
  align-items: center;
  height: 60px;
}

.active-btn {
  color: #165dff;
  border-color: #165dff;
}

.picker {
  width: 230px;
  height: auto;
}
</style>