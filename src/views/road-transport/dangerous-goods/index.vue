<!--
@description: TODO 客运
@author: liudingbang
@date: 2025/5/30 10:01
-->
<template>
  <div class="full-container">
    <!--左侧抽屉-->
    <LeftDrawer :show="show_left_panel">
      <Tabs :menus="menu[Number(currentMenuIndex)]!.children![1]!.tabs_data" v-slot="{active}">
        <template v-if="active == 0">
          <ItemTitle title="基础指标" :more-click="() => handleMoreClick('基础指标')">
            <BasicIndex ref="BasicIndexRef"/>
          </ItemTitle>
          <ItemTitle title="当前危化品分类占比">
            <GoodsRate ref="TodaySidePointMonitorRef"/>
          </ItemTitle>
          <ItemTitle title="今日危货运输货运量趋势" :more-click="() => handleMoreClick('今日危货运输货运量趋势')">
            <TodayTransportNumTrend ref="TodayOutInCityPersonNumTrendRef"/>
          </ItemTitle>
        </template>
        <template v-if="active == 1">
          <VehicleTree :data-tree="dataTree"/>
        </template>
        <template v-if="active == 2">
          <ItemTitle title="今日预警记录" :more-click="() => handleMoreClick('今日预警记录')">
            <TodayWarnRecord ref="TodayWarnRecordRef"/>
          </ItemTitle>
        </template>
      </Tabs>
    </LeftDrawer>
    <!--右侧排行-->
    <RightDataDetailPopup width="30vw" v-model:visible="visible" title="数据排名">
      <ItemTitle style="height: 33.33%" title="近一周未上线车辆排名">
        <BaseTable :is-has-search="false" :data="data" table-height="16vh">
          <template #default>
            <el-table-column type="index" label="排名" width="60"/>
            <el-table-column prop="dt" label="企业名称"/>
            <el-table-column prop="name" label="车辆未上线数"/>
            <el-table-column prop="warn_cnt" label="占比"/>
          </template>
        </BaseTable>
      </ItemTitle>
      <ItemTitle style="height: 33.33%" title="今日危货运输货运排行">
        <BaseTable :is-has-search="false" :data="data" table-height="16vh">
          <template #default>
            <el-table-column type="index" label="排名" width="60"/>
            <el-table-column prop="dt" label="危货类型"/>
            <el-table-column prop="name" label="运输次数"/>
            <el-table-column prop="warn_cnt" label="占比"/>
          </template>
        </BaseTable>
      </ItemTitle>
      <ItemTitle style="height: 33.33%" title="今日危货运输出发量排行">
        <BaseTable :is-has-search="false" :data="data" table-height="16vh">
          <template #default>
            <el-table-column type="index" label="排名" width="60"/>
            <el-table-column prop="dt" label="危货类型"/>
            <el-table-column prop="name" label="出发总量"/>
            <el-table-column prop="warn_cnt" label="占比"/>
          </template>
        </BaseTable>
      </ItemTitle>
    </RightDataDetailPopup>
    <!--地图-->
    <!--todo 地图图例及其修饰信息-->
  </div>
</template>
<script lang="ts" setup>
import {ref} from 'vue'
import {useStore} from '@/store'
import {storeToRefs} from 'pinia'
import LeftDrawer from "@/components/v2/LeftDrawer.vue";
import Tabs from "@/components/v2/Tabs.vue";
import ItemTitle from "@/components/common/ItemTitle.vue";
import BasicIndex from "@/views/road-transport/dangerous-goods/components/data-total/BasicIndex.vue";
import TodayTransportNumTrend from "@/views/road-transport/dangerous-goods/components/data-total/TodayTransportNumTrend.vue";
import GoodsRate from "@/views/road-transport/dangerous-goods/components/data-total/GoodsRate.vue";
import TodayWarnRecord from "@/views/road-transport/passenger/components/monitor-warn/TodayWarnRecord.vue";
import VehicleTree from "@/components/common/VehicleTree.vue";
import RightDataDetailPopup from "@/components/v2/RightDataDetailPopup.vue";
import DateRangeSearch from "@/components/v2/DateRangeSearch.vue";
import BaseTable from "@/components/v2/table/BaseTable.vue";

const store = useStore()
const show_left_panel = ref(true)
const {menu, currentMenuIndex} = storeToRefs(store)

const BasicIndexRef = ref<InstanceType<typeof BasicIndex> | null>(null)
const TodaySidePointMonitorRef = ref<InstanceType<typeof GoodsRate> | null>(null)
const TodayOutInCityPersonNumTrendRef = ref<InstanceType<typeof TodayTransportNumTrend> | null>(null)
const TodayWarnRecordRef = ref<InstanceType<typeof TodayWarnRecord> | null>(null)

/**
 * 更多按钮点击
 * @param type 点击类型
 */
function handleMoreClick(type: '基础指标' | '当前危化品分类占比'| '今日危货运输货运量趋势' | '今日预警记录'): void {
  switch (type) {
    case '基础指标':
      BasicIndexRef.value?.handleMoreClick()
      break;
    case '当前危化品分类占比':
      TodaySidePointMonitorRef.value?.handleMoreClick()
      break;
    case '今日危货运输货运量趋势':
      TodayOutInCityPersonNumTrendRef.value?.handleMoreClick()
      break;
    case "今日预警记录":
      TodayWarnRecordRef.value?.handleMoreClick()
      break
  }
}

const dataTree: unknown[] = [
  {
    id: '1',
    label: 'XX公司(5/7)',
    children: [
      {
        id: '1-1',
        label: '晋A15171 赵小刚',
        online: true,
        lng: 112.735295,
        lat: 38.448514
      },
      {
        id: '1-2',
        label: '晋A15172 赵小刚',
        online: true,
        lng: 112.73368581216272,
        lat: 38.410806760867665
      },
      {
        id: '1-3',
        label: '晋A15173 赵小刚',
        online: true,
      },
      {
        id: '1-4',
        label: '晋A15174 赵小刚',
        online: false,
      },
    ],
  },
  {
    id: '2',
    label: 'XX公司(5/7)',
    children: [
      {
        id: '2-1',
        label: '晋B15171 赵小刚',
        online: true,
      },
      {
        id: '2-2',
        label: '晋B15172 赵小刚',
      },
      {
        id: '2-3',
        label: '晋B15171 赵小刚',
      },
      {
        id: '2-4',
        label: '晋B15172 赵小刚',
      },
    ],
  },
]

const data = Array.from({ length: 20 }, (_, i) => ({
  id: i + 1,
  title: `标题 ${i + 1}`,
  description: `这是第 ${i + 1} 条数据的描述内容。`,
  timestamp: new Date().toISOString(),
}));

const visible = ref(true)
</script>
<style scoped lang="scss">

</style>
