<template>
  <div class="container" style="align-items: center">
    <el-row>
      <el-col :span="7"></el-col>
      <el-col :span="10">
        <div style=" margin-top: 130px; padding: 24px 0; font-size: 42px; text-align: center; color: white;">
          交通运行监测调度中心
        </div>
        <el-row style="background: white; border-radius: 6px; padding: 20px 10px">
          <el-col :span="12" style="padding-left: 20px">
            <el-image style="width: 76%; height: 90%" src="/static/base/login-pic.png" fit="cover" />
          </el-col>
          <el-col :span="12" style="padding: 36px 20px 0 0; text-align: center">
            <el-form label-width="auto" :model="dataForm" status-icon @keydown.enter="submitForm">
              <el-form-item prop="username">
                <el-input v-model="dataForm.username" prefix-icon="User" size="large" placeholder="请输入登录账号" />
              </el-form-item>
              <el-form-item prop="password">
                <el-input v-model="dataForm.password" prefix-icon="Lock" size="large" placeholder="请输入登录密码"
                  type="password" />
              </el-form-item>
              <el-form-item>
                <el-col :span="24" class="text-center">
                  <el-button type="primary" @click="submitForm()" size="large" style="width: 100%">登录系统</el-button>
                </el-col>
              </el-form-item>
            </el-form>
          </el-col>
        </el-row>
      </el-col>
      <el-col :span="7"></el-col>
    </el-row>
  </div>
</template>

<script lang="ts">
export default {
  name: 'Login'
}
</script>

<script lang="ts" setup>
import { reactive, ref } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { useSessionStore } from '@/store/session'

import loginApi from '@/service/login'

const session = useSessionStore()
const router = useRouter()

const dataForm = reactive({
  username: 'admin',
  password: 'admin123',
  target: 'form'
})

const submitForm = async () => {
  if (dataForm.username == null || dataForm.username == '') {
    ElMessage({
      message: '请输入登录账号',
      type: 'warning'
    })
    return
  }
  if (dataForm.password == null || dataForm.password == '') {
    ElMessage({
      message: '请输入登录密码',
      type: 'warning'
    })
    return
  }
  session.$patch({
    token: ''
  })
  const res = await loginApi.login(dataForm);

  session.$patch({
    token: res.data.data.token,
    menuTree: res.data.data.menuTree,
    sessionUser: res.data.data.sessionUser
  })
  router.replace({path: '/sys/user'})
}


</script>

<style scoped>
.container {
  background-color: #0c588f;
  /* 设置白色背景 */
  min-height: 100vh;
  /* 确保背景覆盖整个视口高度 */
  /* opacity: 0.7; */
  /* background: rgba(0, 24, 42, 1); */
}
</style>
