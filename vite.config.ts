import { fileURLToPath, URL } from 'node:url'
import { createSvgIconsPlugin } from 'vite-plugin-svg-icons'
import path from 'node:path'
import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import vueJsx from '@vitejs/plugin-vue-jsx'
import fs from 'fs'
import pxtorem from 'postcss-pxtorem'
import autoprefixer from 'autoprefixer'
// 时间戳
const timestamp = new Date().getTime()
// svgIconsPlugin 配置
const svgIconsPlugin = createSvgIconsPlugin({
  iconDirs: [
    // 通过扫描文件夹的方式引入
    ...scanIconDirs(path.resolve(process.cwd(), 'src/assets/images'))
    // 手动方式引入
    // path.resolve(process.cwd(), 'src/assets/images/svgs/aviation-railway')
  ],
  symbolId: 'icon-[name]', // 支持目录层级：'icon-[dir]-[name]'
  inject: 'body-last', // DOM插入位置
  customDomId: '__svg_icons', // 自定义容器ID
  // ...其他配置
  svgoOptions: {
    plugins: [
      { name: 'removeAttrs', params: { attrs: 'fill|stroke' } }, // 移除 fill/stroke 属性（用于单色图标）
      { name: 'removeStyleElement' }, // 移除 <style> 标签
      { name: 'inlineStyles' }, // 将样式内联到元素上
      { name: 'removeDimensions' } // 移除 width/height 属性（依赖 viewBox）
    ]
  }
})
// 打包文件时候 更新版本号 用作判断是否要刷新页面
const updateVersion = () => {
  return {
    name: 'update-version',
    buildStart() {
      if (process.env.NODE_ENV !== 'development') {
        let version = 1.0
        try {
          // 读取 json 文件里面版本号
          const packageJson = JSON.parse(fs.readFileSync('public/version.json', 'utf-8'))
          version = Number(packageJson.version)
          version = Number((version + 0.1).toFixed(1)) // 增加0.1并保留1位小数
        } catch (error) {
          console.log(error)
        }
        const versionData = { version: version.toString() } // 根据需要生成新版本号
        // 写入 json 文件里面版本号
        fs.writeFileSync('public/version.json', JSON.stringify(versionData, null, 2))
      }
    }
  }
}

// https://vitejs.dev/config/
export default defineConfig({
  base: '/tocc',
  publicDir: './public',
  css: {
    postcss: {
      plugins: [
        pxtorem({
          rootValue: 19.2,
          propList: ['*'],
          selectorBlackList: [/^el-/]
        }),
        autoprefixer({
          overrideBrowserslist: ['Chrome > 40', 'ff > 31', 'ie 11']
        })
      ]
    }
  },
  plugins: [vue(), vueJsx(), updateVersion(), svgIconsPlugin],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  // 配置服务器的代理设置
  server: {
    // 代理配置，用于重定向请求到其他服务器
    proxy: {
      // TODO 建议改成 Proxy 配置，而不是前端直接访问后端接口
      // '/api/': {
      //   target: 'http://127.0.0.1:9091/tocc/api',
      //   rewrite: (path) => path.replace(/^\/api/, '')
      // },
      // 定义一个代理规则，将/dataroom路径下的请求代理到指定的目标服务器
      '/dataroom': {
        // 目标服务器的地址
        target: 'http://127.0.0.1:7521',
        // 更改请求的origin为代理服务器的origin，以便与目标服务器交互
        changeOrigin: true, //开启代理
        configure: (proxy, options) => {
          // 解决请求403问题：invalid CORS request
          proxy.on('proxyReq', function (proxyReq, req, res) {
            proxyReq.removeHeader('referer') // 移除请求头
            proxyReq.removeHeader('origin') // 移除请求头
          })
        }
        // 重写请求路径，移除/dataroom前缀
        //rewrite: (path) => path.replace(/^\/dataroom/, '')
      }
    }
  },
  build: {
    // 配置输出带时间戳，处理浏览器缓存
    rollupOptions: {
      output: {
        entryFileNames: `assets/[name].${timestamp}[hash].js`,
        chunkFileNames: `assets/[name].${timestamp}[hash].js`,
        assetFileNames: `assets/[name].${timestamp}[hash].[ext]`
      }
    }
  }
})

/*
 * 扫描指定目录下的所有子目录
 * */
function scanIconDirs(rootDir: string) {
  return fs
    .readdirSync(rootDir, { withFileTypes: true })
    .filter((dirent) => dirent.isDirectory())
    .map((dirent) => path.resolve(rootDir, dirent.name))
}
